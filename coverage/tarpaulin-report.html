<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <style>html, body {
  margin: 0;
  padding: 0;
}

.app {
  margin: 10px;
  padding: 0;
}

.files-list {
  margin: 10px 0 0;
  width: 100%;
  border-collapse: collapse;
}
.files-list__head {
  border: 1px solid #999;
}
.files-list__head > tr > th {
  padding: 10px;
  border: 1px solid #999;
  text-align: left;
  font-weight: normal;
  background: #ddd;
}
.files-list__body {
}
.files-list__file {
  cursor: pointer;
}
.files-list__file:hover {
  background: #ccf;
}
.files-list__file > td {
  padding: 10px;
  border: 1px solid #999;
}
.files-list__file > td:first-child::before {
  content: '\01F4C4';
  margin-right: 1em;
}
.files-list__file_low {
  background: #fcc;
}
.files-list__file_medium {
  background: #ffc;
}
.files-list__file_high {
  background: #cfc;
}
.files-list__file_folder > td:first-child::before {
  content: '\01F4C1';
  margin-right: 1em;
}

.file-header {
  border: 1px solid #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: white;
}

.file-header__back {
  margin: 10px;
  cursor: pointer;
  flex-shrink: 0;
  flex-grow: 0;
  text-decoration: underline;
  color: #338;
}

.file-header__name {
  margin: 10px;
  flex-shrink: 2;
  flex-grow: 2;
}

.file-header__stat {
  margin: 10px;
  flex-shrink: 0;
  flex-grow: 0;
}

.file-content {
  margin: 10px 0 0;
  border: 1px solid #999;
  padding: 10px;
  counter-reset: line;
  display: flex;
  flex-direction: column;
}

.code-line::before {
    content: counter(line);
    margin-right: 10px;
}
.code-line {
  margin: 0;
  padding: 0.3em;
  height: 1em;
  counter-increment: line;
}
.code-line_covered {
  background: #cfc;
}
.code-line_uncovered {
  background: #fcc;
}
</style>
</head>
<body>
    <div id="root"></div>
    <script>
        var data = {"files":[{"path":["/","Users","yeheng","workspaces","rust","conflux","examples","auth_demo.rs"],"content":"use conflux::auth::{AuthzService, actions, roles, ResourcePath};\nuse std::sync::Arc;\nuse tracing_subscriber::fmt::init;\n\n/// 演示认证授权系统的基本功能\n/// \n/// 这个示例展示了如何：\n/// 1. 初始化AuthzService\n/// 2. 设置角色和权限\n/// 3. 分配用户角色\n/// 4. 检查权限\n#[tokio::main]\nasync fn main() -\u003e Result\u003c(), Box\u003cdyn std::error::Error\u003e\u003e {\n    // 初始化日志\n    init();\n    \n    println!(\"🚀 Conflux 认证授权系统演示\");\n    println!(\"================================\");\n    \n    // 注意：这需要一个运行中的PostgreSQL数据库\n    // 可以通过以下命令启动：\n    // docker run -d -p 5432:5432 -e POSTGRES_PASSWORD=password postgres\n    let database_url = \"postgresql://postgres:password@localhost:5432/conflux_demo\";\n    \n    println!(\"📊 连接数据库: {}\", database_url);\n    \n    // 初始化AuthzService\n    let authz_service = match AuthzService::new(database_url).await {\n        Ok(service) =\u003e {\n            println!(\"✅ AuthzService 初始化成功\");\n            Arc::new(service)\n        }\n        Err(e) =\u003e {\n            println!(\"❌ AuthzService 初始化失败: {}\", e);\n            println!(\"💡 请确保PostgreSQL数据库正在运行并且连接字符串正确\");\n            println!(\"   可以使用以下命令启动PostgreSQL:\");\n            println!(\"   docker run -d -p 5432:5432 -e POSTGRES_PASSWORD=password postgres\");\n            return Err(e.into());\n        }\n    };\n    \n    println!(\"\\n🔧 设置权限策略\");\n    println!(\"================\");\n    \n    // 设置租户管理员权限\n    authz_service\n        .add_permission_for_role(\n            roles::TENANT_ADMIN,\n            \"demo_tenant\",\n            \"/tenants/demo_tenant/*\",\n            actions::ADMIN,\n        )\n        .await?;\n    println!(\"✅ 租户管理员权限设置完成\");\n    \n    // 设置开发者权限\n    authz_service\n        .add_permission_for_role(\n            roles::DEVELOPER,\n            \"demo_tenant\",\n            \"/tenants/demo_tenant/apps/*\",\n            actions::READ,\n        )\n        .await?;\n    \n    authz_service\n        .add_permission_for_role(\n            roles::DEVELOPER,\n            \"demo_tenant\",\n            \"/tenants/demo_tenant/apps/*\",\n            actions::WRITE,\n        )\n        .await?;\n    println!(\"✅ 开发者权限设置完成\");\n    \n    // 设置查看者权限\n    authz_service\n        .add_permission_for_role(\n            roles::VIEWER,\n            \"demo_tenant\",\n            \"/tenants/demo_tenant/apps/*\",\n            actions::READ,\n        )\n        .await?;\n    println!(\"✅ 查看者权限设置完成\");\n    \n    println!(\"\\n👥 分配用户角色\");\n    println!(\"================\");\n    \n    // 分配用户角色\n    authz_service\n        .assign_role_to_user(\"alice\", roles::TENANT_ADMIN, \"demo_tenant\")\n        .await?;\n    println!(\"✅ Alice 被分配为租户管理员\");\n    \n    authz_service\n        .assign_role_to_user(\"bob\", roles::DEVELOPER, \"demo_tenant\")\n        .await?;\n    println!(\"✅ Bob 被分配为开发者\");\n    \n    authz_service\n        .assign_role_to_user(\"charlie\", roles::VIEWER, \"demo_tenant\")\n        .await?;\n    println!(\"✅ Charlie 被分配为查看者\");\n    \n    println!(\"\\n🔍 权限检查测试\");\n    println!(\"================\");\n    \n    // 测试各种权限检查\n    let test_cases = vec![\n        (\"alice\", \"demo_tenant\", \"/tenants/demo_tenant/admin/users\", actions::ADMIN, \"管理员访问用户管理\"),\n        (\"alice\", \"demo_tenant\", \"/tenants/demo_tenant/apps/myapp\", actions::WRITE, \"管理员写入应用配置\"),\n        (\"bob\", \"demo_tenant\", \"/tenants/demo_tenant/apps/myapp/configs/db.toml\", actions::READ, \"开发者读取配置\"),\n        (\"bob\", \"demo_tenant\", \"/tenants/demo_tenant/apps/myapp/configs/db.toml\", actions::WRITE, \"开发者写入配置\"),\n        (\"bob\", \"demo_tenant\", \"/tenants/demo_tenant/admin/users\", actions::ADMIN, \"开发者访问用户管理（应该被拒绝）\"),\n        (\"charlie\", \"demo_tenant\", \"/tenants/demo_tenant/apps/myapp/configs/db.toml\", actions::READ, \"查看者读取配置\"),\n        (\"charlie\", \"demo_tenant\", \"/tenants/demo_tenant/apps/myapp/configs/db.toml\", actions::WRITE, \"查看者写入配置（应该被拒绝）\"),\n    ];\n    \n    for (user, tenant, resource, action, description) in test_cases {\n        let allowed = authz_service.check(user, tenant, resource, action).await?;\n        let status = if allowed { \"✅ 允许\" } else { \"❌ 拒绝\" };\n        println!(\"{} {} - {} 在 {} 对 {} 执行 {}\", \n                 status, user, description, tenant, resource, action);\n    }\n    \n    println!(\"\\n📋 用户角色查询\");\n    println!(\"================\");\n    \n    let users = vec![\"alice\", \"bob\", \"charlie\"];\n    for user in users {\n        let roles = authz_service\n            .get_roles_for_user_in_tenant(user, \"demo_tenant\")\n            .await?;\n        println!(\"👤 {} 的角色: {:?}\", user, roles);\n    }\n    \n    println!(\"\\n🎯 资源路径构建器演示\");\n    println!(\"======================\");\n    \n    let config_path = ResourcePath::config(\"demo_tenant\", \"myapp\", \"production\", \"database.toml\");\n    println!(\"📄 配置文件路径: {}\", config_path);\n    \n    let app_path = ResourcePath::app(\"demo_tenant\", \"myapp\");\n    println!(\"📱 应用路径: {}\", app_path);\n    \n    let admin_path = ResourcePath::admin(\"demo_tenant\", \"users\");\n    println!(\"⚙️  管理路径: {}\", admin_path);\n    \n    println!(\"\\n🎉 演示完成！\");\n    println!(\"==============\");\n    println!(\"认证授权系统已成功演示以下功能：\");\n    println!(\"• 基于角色的访问控制 (RBAC)\");\n    println!(\"• 多租户支持\");\n    println!(\"• 细粒度权限控制\");\n    println!(\"• 资源路径模式匹配\");\n    println!(\"• 动态权限检查\");\n    \n    Ok(())\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","app.rs"],"content":"use crate::auth::AuthzService;\nuse crate::raft::{RaftClient, Store};\nuse std::sync::Arc;\n\n/// 核心应用句柄，封装了所有核心服务的引用\n/// 这个结构体是协议层与核心业务逻辑之间的桥梁\n#[derive(Clone)]\npub struct CoreAppHandle {\n    /// Raft 客户端，用于处理分布式共识操作\n    pub raft_client: Arc\u003cRaftClient\u003e,\n    \n    /// 存储实例，用于直接访问数据（读操作）\n    pub store: Arc\u003cStore\u003e,\n\n    /// 认证授权服务\n    pub authz_service: Arc\u003cAuthzService\u003e,\n\n    // TODO: 在后续的 Epic 中添加更多服务\n    // pub metadata_service: Arc\u003cMetadataService\u003e,\n    // pub watch_service: Arc\u003cWatchService\u003e,\n}\n\nimpl CoreAppHandle {\n    /// 创建新的核心应用句柄\n    pub fn new(raft_client: Arc\u003cRaftClient\u003e, store: Arc\u003cStore\u003e, authz_service: Arc\u003cAuthzService\u003e) -\u003e Self {\n        Self {\n            raft_client,\n            store,\n            authz_service,\n        }\n    }\n    \n    /// 获取 Raft 客户端的引用\n    pub fn raft_client(\u0026self) -\u003e \u0026RaftClient {\n        \u0026self.raft_client\n    }\n    \n    /// 获取存储实例的引用\n    pub fn store(\u0026self) -\u003e \u0026Store {\n        \u0026self.store\n    }\n\n    /// 获取认证授权服务的引用\n    pub fn authz_service(\u0026self) -\u003e \u0026AuthzService {\n        \u0026self.authz_service\n    }\n}\n\n// TODO: 更新测试以包含AuthzService\n// #[cfg(test)]\n// mod tests {\n//     use super::*;\n//     use crate::raft::Store;\n//     use tempfile::TempDir;\n\n//     #[tokio::test]\n//     async fn test_core_app_handle_creation() {\n//         let temp_dir = TempDir::new().unwrap();\n//         let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n//         let raft_client = Arc::new(RaftClient::new(store.clone()));\n\n//         let handle = CoreAppHandle::new(raft_client.clone(), store.clone());\n\n//         // 验证句柄正确包含了服务引用\n//         assert!(Arc::ptr_eq(\u0026handle.raft_client, \u0026raft_client));\n//         assert!(Arc::ptr_eq(\u0026handle.store, \u0026store));\n//     }\n// }\n","traces":[{"line":25,"address":[],"length":0,"stats":{"Line":0}},{"line":34,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":7},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","auth","api.rs"],"content":"use axum::{\n    extract::{Path, Query, State},\n    http::StatusCode,\n    response::Json,\n    routing::{delete, get, post},\n    Router,\n};\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse std::sync::Arc;\nuse tracing::{debug, info};\n\nuse super::AuthzService;\n\n/// 权限检查请求\n#[derive(Debug, Deserialize)]\npub struct CheckPermissionRequest {\n    pub user_id: String,\n    pub tenant: String,\n    pub resource: String,\n    pub action: String,\n}\n\n/// 权限检查响应\n#[derive(Debug, Serialize)]\npub struct CheckPermissionResponse {\n    pub allowed: bool,\n    pub user_id: String,\n    pub tenant: String,\n    pub resource: String,\n    pub action: String,\n}\n\n/// 添加权限请求\n#[derive(Debug, Deserialize)]\npub struct AddPermissionRequest {\n    pub resource: String,\n    pub action: String,\n}\n\n/// 角色分配请求\n#[derive(Debug, Deserialize)]\npub struct AssignRoleRequest {\n    pub role: String,\n}\n\n/// 角色列表响应\n#[derive(Debug, Serialize)]\npub struct RolesResponse {\n    pub roles: Vec\u003cString\u003e,\n}\n\n/// 操作结果响应\n#[derive(Debug, Serialize)]\npub struct OperationResponse {\n    pub success: bool,\n    pub message: String,\n}\n\n/// 创建认证授权相关的路由\npub fn create_auth_routes\u003cS\u003e(authz_service: Arc\u003cAuthzService\u003e) -\u003e Router\u003cS\u003e\nwhere\n    S: Clone + Send + Sync + 'static,\n{\n    Router::new()\n        // 权限检查端点（主要用于调试）\n        .route(\"/_auth/check\", post(check_permission))\n        // 租户角色管理\n        .route(\"/tenants/:tenant/roles\", get(list_tenant_roles))\n        // 角色权限管理\n        .route(\n            \"/tenants/:tenant/roles/:role/permissions\",\n            post(add_role_permission),\n        )\n        .route(\n            \"/tenants/:tenant/roles/:role/permissions\",\n            delete(remove_role_permission),\n        )\n        // 用户角色管理\n        .route(\n            \"/tenants/:tenant/users/:user_id/roles\",\n            get(get_user_roles),\n        )\n        .route(\n            \"/tenants/:tenant/users/:user_id/roles\",\n            post(assign_user_role),\n        )\n        .route(\n            \"/tenants/:tenant/users/:user_id/roles/:role\",\n            delete(revoke_user_role),\n        )\n        // 策略重新加载\n        .route(\"/_auth/reload\", post(reload_policies))\n        .with_state(authz_service)\n}\n\n/// 检查权限（调试用）\nasync fn check_permission(\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n    Json(request): Json\u003cCheckPermissionRequest\u003e,\n) -\u003e std::result::Result\u003cJson\u003cCheckPermissionResponse\u003e, StatusCode\u003e {\n    debug!(\"Checking permission: {:?}\", request);\n\n    let allowed = authz_service\n        .check(\u0026request.user_id, \u0026request.tenant, \u0026request.resource, \u0026request.action)\n        .await\n        .map_err(|e| {\n            tracing::error!(\"Permission check failed: {}\", e);\n            StatusCode::INTERNAL_SERVER_ERROR\n        })?;\n\n    Ok(Json(CheckPermissionResponse {\n        allowed,\n        user_id: request.user_id,\n        tenant: request.tenant,\n        resource: request.resource,\n        action: request.action,\n    }))\n}\n\n/// 列出租户下的所有角色（占位符实现）\nasync fn list_tenant_roles(\n    Path(tenant): Path\u003cString\u003e,\n    State(_authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n) -\u003e std::result::Result\u003cJson\u003cRolesResponse\u003e, StatusCode\u003e {\n    debug!(\"Listing roles for tenant: {}\", tenant);\n\n    // TODO: 实现从Casbin中获取租户的所有角色\n    // 目前返回一些预定义的角色\n    let roles = vec![\n        \"admin\".to_string(),\n        \"developer\".to_string(),\n        \"viewer\".to_string(),\n    ];\n\n    Ok(Json(RolesResponse { roles }))\n}\n\n/// 为角色添加权限\nasync fn add_role_permission(\n    Path((tenant, role)): Path\u003c(String, String)\u003e,\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n    Json(request): Json\u003cAddPermissionRequest\u003e,\n) -\u003e std::result::Result\u003cJson\u003cOperationResponse\u003e, StatusCode\u003e {\n    info!(\n        \"Adding permission to role: tenant={}, role={}, resource={}, action={}\",\n        tenant, role, request.resource, request.action\n    );\n\n    let success = authz_service\n        .add_permission_for_role(\u0026role, \u0026tenant, \u0026request.resource, \u0026request.action)\n        .await\n        .map_err(|e| {\n            tracing::error!(\"Failed to add permission: {}\", e);\n            StatusCode::INTERNAL_SERVER_ERROR\n        })?;\n\n    let message = if success {\n        \"Permission added successfully\".to_string()\n    } else {\n        \"Permission already exists\".to_string()\n    };\n\n    Ok(Json(OperationResponse { success, message }))\n}\n\n/// 移除角色的权限\nasync fn remove_role_permission(\n    Path((tenant, role)): Path\u003c(String, String)\u003e,\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n    Query(params): Query\u003cHashMap\u003cString, String\u003e\u003e,\n) -\u003e std::result::Result\u003cJson\u003cOperationResponse\u003e, StatusCode\u003e {\n    let resource = params\n        .get(\"resource\")\n        .ok_or(StatusCode::BAD_REQUEST)?\n        .clone();\n    let action = params.get(\"action\").ok_or(StatusCode::BAD_REQUEST)?.clone();\n\n    info!(\n        \"Removing permission from role: tenant={}, role={}, resource={}, action={}\",\n        tenant, role, resource, action\n    );\n\n    let success = authz_service\n        .remove_permission_for_role(\u0026role, \u0026tenant, \u0026resource, \u0026action)\n        .await\n        .map_err(|e| {\n            tracing::error!(\"Failed to remove permission: {}\", e);\n            StatusCode::INTERNAL_SERVER_ERROR\n        })?;\n\n    let message = if success {\n        \"Permission removed successfully\".to_string()\n    } else {\n        \"Permission not found\".to_string()\n    };\n\n    Ok(Json(OperationResponse { success, message }))\n}\n\n/// 获取用户在租户下的角色\nasync fn get_user_roles(\n    Path((tenant, user_id)): Path\u003c(String, String)\u003e,\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n) -\u003e std::result::Result\u003cJson\u003cRolesResponse\u003e, StatusCode\u003e {\n    debug!(\"Getting roles for user: tenant={}, user_id={}\", tenant, user_id);\n\n    let roles = authz_service\n        .get_roles_for_user_in_tenant(\u0026user_id, \u0026tenant)\n        .await\n        .map_err(|e| {\n            tracing::error!(\"Failed to get user roles: {}\", e);\n            StatusCode::INTERNAL_SERVER_ERROR\n        })?;\n\n    Ok(Json(RolesResponse { roles }))\n}\n\n/// 为用户分配角色\nasync fn assign_user_role(\n    Path((tenant, user_id)): Path\u003c(String, String)\u003e,\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n    Json(request): Json\u003cAssignRoleRequest\u003e,\n) -\u003e std::result::Result\u003cJson\u003cOperationResponse\u003e, StatusCode\u003e {\n    info!(\n        \"Assigning role to user: tenant={}, user_id={}, role={}\",\n        tenant, user_id, request.role\n    );\n\n    let success = authz_service\n        .assign_role_to_user(\u0026user_id, \u0026request.role, \u0026tenant)\n        .await\n        .map_err(|e| {\n            tracing::error!(\"Failed to assign role: {}\", e);\n            StatusCode::INTERNAL_SERVER_ERROR\n        })?;\n\n    let message = if success {\n        \"Role assigned successfully\".to_string()\n    } else {\n        \"Role already assigned\".to_string()\n    };\n\n    Ok(Json(OperationResponse { success, message }))\n}\n\n/// 撤销用户的角色\nasync fn revoke_user_role(\n    Path((tenant, user_id, role)): Path\u003c(String, String, String)\u003e,\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n) -\u003e std::result::Result\u003cJson\u003cOperationResponse\u003e, StatusCode\u003e {\n    info!(\n        \"Revoking role from user: tenant={}, user_id={}, role={}\",\n        tenant, user_id, role\n    );\n\n    let success = authz_service\n        .revoke_role_from_user(\u0026user_id, \u0026role, \u0026tenant)\n        .await\n        .map_err(|e| {\n            tracing::error!(\"Failed to revoke role: {}\", e);\n            StatusCode::INTERNAL_SERVER_ERROR\n        })?;\n\n    let message = if success {\n        \"Role revoked successfully\".to_string()\n    } else {\n        \"Role assignment not found\".to_string()\n    };\n\n    Ok(Json(OperationResponse { success, message }))\n}\n\n/// 重新加载策略\nasync fn reload_policies(\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n) -\u003e std::result::Result\u003cJson\u003cOperationResponse\u003e, StatusCode\u003e {\n    info!(\"Reloading Casbin policies\");\n\n    authz_service.reload_policy().await.map_err(|e| {\n        tracing::error!(\"Failed to reload policies: {}\", e);\n        StatusCode::INTERNAL_SERVER_ERROR\n    })?;\n\n    Ok(Json(OperationResponse {\n        success: true,\n        message: \"Policies reloaded successfully\".to_string(),\n    }))\n}\n","traces":[{"line":61,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":69,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":77,"address":[],"length":0,"stats":{"Line":0}},{"line":82,"address":[],"length":0,"stats":{"Line":0}},{"line":86,"address":[],"length":0,"stats":{"Line":0}},{"line":90,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":104,"address":[],"length":0,"stats":{"Line":0}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":106,"address":[],"length":0,"stats":{"Line":0}},{"line":107,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":0}},{"line":109,"address":[],"length":0,"stats":{"Line":0}},{"line":112,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":0}},{"line":115,"address":[],"length":0,"stats":{"Line":0}},{"line":116,"address":[],"length":0,"stats":{"Line":0}},{"line":117,"address":[],"length":0,"stats":{"Line":0}},{"line":122,"address":[],"length":0,"stats":{"Line":0}},{"line":126,"address":[],"length":0,"stats":{"Line":0}},{"line":130,"address":[],"length":0,"stats":{"Line":0}},{"line":131,"address":[],"length":0,"stats":{"Line":0}},{"line":132,"address":[],"length":0,"stats":{"Line":0}},{"line":133,"address":[],"length":0,"stats":{"Line":0}},{"line":136,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":145,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":150,"address":[],"length":0,"stats":{"Line":0}},{"line":151,"address":[],"length":0,"stats":{"Line":0}},{"line":152,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":0}},{"line":154,"address":[],"length":0,"stats":{"Line":0}},{"line":155,"address":[],"length":0,"stats":{"Line":0}},{"line":158,"address":[],"length":0,"stats":{"Line":0}},{"line":159,"address":[],"length":0,"stats":{"Line":0}},{"line":161,"address":[],"length":0,"stats":{"Line":0}},{"line":164,"address":[],"length":0,"stats":{"Line":0}},{"line":168,"address":[],"length":0,"stats":{"Line":0}},{"line":173,"address":[],"length":0,"stats":{"Line":0}},{"line":175,"address":[],"length":0,"stats":{"Line":0}},{"line":177,"address":[],"length":0,"stats":{"Line":0}},{"line":179,"address":[],"length":0,"stats":{"Line":0}},{"line":180,"address":[],"length":0,"stats":{"Line":0}},{"line":184,"address":[],"length":0,"stats":{"Line":0}},{"line":185,"address":[],"length":0,"stats":{"Line":0}},{"line":186,"address":[],"length":0,"stats":{"Line":0}},{"line":187,"address":[],"length":0,"stats":{"Line":0}},{"line":188,"address":[],"length":0,"stats":{"Line":0}},{"line":189,"address":[],"length":0,"stats":{"Line":0}},{"line":192,"address":[],"length":0,"stats":{"Line":0}},{"line":193,"address":[],"length":0,"stats":{"Line":0}},{"line":195,"address":[],"length":0,"stats":{"Line":0}},{"line":198,"address":[],"length":0,"stats":{"Line":0}},{"line":202,"address":[],"length":0,"stats":{"Line":0}},{"line":206,"address":[],"length":0,"stats":{"Line":0}},{"line":208,"address":[],"length":0,"stats":{"Line":0}},{"line":209,"address":[],"length":0,"stats":{"Line":0}},{"line":210,"address":[],"length":0,"stats":{"Line":0}},{"line":211,"address":[],"length":0,"stats":{"Line":0}},{"line":212,"address":[],"length":0,"stats":{"Line":0}},{"line":213,"address":[],"length":0,"stats":{"Line":0}},{"line":216,"address":[],"length":0,"stats":{"Line":0}},{"line":220,"address":[],"length":0,"stats":{"Line":0}},{"line":225,"address":[],"length":0,"stats":{"Line":0}},{"line":226,"address":[],"length":0,"stats":{"Line":0}},{"line":230,"address":[],"length":0,"stats":{"Line":0}},{"line":231,"address":[],"length":0,"stats":{"Line":0}},{"line":232,"address":[],"length":0,"stats":{"Line":0}},{"line":233,"address":[],"length":0,"stats":{"Line":0}},{"line":234,"address":[],"length":0,"stats":{"Line":0}},{"line":235,"address":[],"length":0,"stats":{"Line":0}},{"line":238,"address":[],"length":0,"stats":{"Line":0}},{"line":239,"address":[],"length":0,"stats":{"Line":0}},{"line":241,"address":[],"length":0,"stats":{"Line":0}},{"line":244,"address":[],"length":0,"stats":{"Line":0}},{"line":248,"address":[],"length":0,"stats":{"Line":0}},{"line":252,"address":[],"length":0,"stats":{"Line":0}},{"line":253,"address":[],"length":0,"stats":{"Line":0}},{"line":257,"address":[],"length":0,"stats":{"Line":0}},{"line":258,"address":[],"length":0,"stats":{"Line":0}},{"line":259,"address":[],"length":0,"stats":{"Line":0}},{"line":260,"address":[],"length":0,"stats":{"Line":0}},{"line":261,"address":[],"length":0,"stats":{"Line":0}},{"line":262,"address":[],"length":0,"stats":{"Line":0}},{"line":265,"address":[],"length":0,"stats":{"Line":0}},{"line":266,"address":[],"length":0,"stats":{"Line":0}},{"line":268,"address":[],"length":0,"stats":{"Line":0}},{"line":271,"address":[],"length":0,"stats":{"Line":0}},{"line":275,"address":[],"length":0,"stats":{"Line":0}},{"line":278,"address":[],"length":0,"stats":{"Line":0}},{"line":280,"address":[],"length":0,"stats":{"Line":0}},{"line":281,"address":[],"length":0,"stats":{"Line":0}},{"line":282,"address":[],"length":0,"stats":{"Line":0}},{"line":285,"address":[],"length":0,"stats":{"Line":0}},{"line":286,"address":[],"length":0,"stats":{"Line":0}},{"line":287,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":104},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","auth","middleware.rs"],"content":"use axum::{\n    extract::{Request, State},\n    http::{HeaderMap, StatusCode, Uri, Method},\n    middleware::Next,\n    response::Response,\n};\nuse std::sync::Arc;\nuse tracing::{debug, error, warn};\n\nuse super::{AuthContext, AuthzService, actions};\nuse crate::error::{ConfluxError, Result};\n\n/// 授权中间件\n/// \n/// 负责检查用户是否有权限访问特定资源\n#[derive(Clone)]\npub struct AuthzMiddleware {\n    authz_service: Arc\u003cAuthzService\u003e,\n}\n\nimpl AuthzMiddleware {\n    /// 创建新的授权中间件\n    pub fn new(authz_service: Arc\u003cAuthzService\u003e) -\u003e Self {\n        Self { authz_service }\n    }\n}\n\n/// Axum授权中间件函数\n///\n/// 这个函数会被注册为Axum的中间件，在每个请求处理前进行权限检查\npub async fn authz_middleware(\n    State(authz_service): State\u003cArc\u003cAuthzService\u003e\u003e,\n    mut request: Request,\n    next: Next,\n) -\u003e std::result::Result\u003cResponse, StatusCode\u003e {\n    let method = request.method().clone();\n    let uri = request.uri().clone();\n    let headers = request.headers().clone();\n\n    // 检查是否为公共端点\n    if is_public_endpoint(uri.path()) {\n        debug!(\"Public endpoint accessed: {}\", uri.path());\n        return Ok(next.run(request).await);\n    }\n\n    // 提取认证信息\n    let auth_context = match extract_auth_context(\u0026headers) {\n        Ok(ctx) =\u003e ctx,\n        Err(e) =\u003e {\n            warn!(\"Authentication failed for {}: {}\", uri.path(), e);\n            return Err(StatusCode::UNAUTHORIZED);\n        }\n    };\n\n    // 解析资源和操作\n    let (resource, action) = match parse_resource_and_action(\u0026method, \u0026uri) {\n        Ok((res, act)) =\u003e (res, act),\n        Err(e) =\u003e {\n            error!(\"Failed to parse resource and action: {}\", e);\n            return Err(StatusCode::BAD_REQUEST);\n        }\n    };\n\n    // 执行权限检查\n    match authz_service\n        .check(\u0026auth_context.user_id, \u0026auth_context.tenant_id, \u0026resource, \u0026action)\n        .await\n    {\n        Ok(true) =\u003e {\n            debug!(\n                \"Permission granted: user={}, tenant={}, resource={}, action={}\",\n                auth_context.user_id, auth_context.tenant_id, resource, action\n            );\n            \n            // 将认证上下文添加到请求扩展中，供后续处理器使用\n            request.extensions_mut().insert(auth_context);\n            \n            Ok(next.run(request).await)\n        }\n        Ok(false) =\u003e {\n            warn!(\n                \"Permission denied: user={}, tenant={}, resource={}, action={}\",\n                auth_context.user_id, auth_context.tenant_id, resource, action\n            );\n            Err(StatusCode::FORBIDDEN)\n        }\n        Err(e) =\u003e {\n            error!(\"Permission check error: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// 从请求头中提取认证上下文\n/// \n/// 目前是一个简化的实现，在实际项目中应该验证JWT token\nfn extract_auth_context(headers: \u0026HeaderMap) -\u003e Result\u003cAuthContext\u003e {\n    // 检查Authorization头\n    let auth_header = headers\n        .get(\"authorization\")\n        .ok_or_else(|| ConfluxError::AuthError(\"Missing authorization header\".to_string()))?;\n\n    let auth_str = auth_header\n        .to_str()\n        .map_err(|_| ConfluxError::AuthError(\"Invalid authorization header\".to_string()))?;\n\n    if !auth_str.starts_with(\"Bearer \") {\n        return Err(ConfluxError::AuthError(\"Invalid authorization format\".to_string()));\n    }\n\n    let token = \u0026auth_str[7..]; // 移除 \"Bearer \" 前缀\n\n    // TODO: 在实际实现中，这里应该验证JWT token并提取用户信息\n    // 现在我们使用一个简化的实现，假设token格式为 \"user_id:tenant_id\"\n    let parts: Vec\u003c\u0026str\u003e = token.split(':').collect();\n    if parts.len() != 2 {\n        return Err(ConfluxError::AuthError(\n            \"Invalid token format, expected user_id:tenant_id\".to_string(),\n        ));\n    }\n\n    let user_id = parts[0].to_string();\n    let tenant_id = parts[1].to_string();\n\n    if user_id.is_empty() || tenant_id.is_empty() {\n        return Err(ConfluxError::AuthError(\"Empty user_id or tenant_id\".to_string()));\n    }\n\n    Ok(AuthContext::new(user_id, tenant_id))\n}\n\n/// 解析请求的资源路径和操作类型\nfn parse_resource_and_action(method: \u0026Method, uri: \u0026Uri) -\u003e Result\u003c(String, String)\u003e {\n    let path = uri.path();\n    \n    // 根据HTTP方法确定操作类型\n    let action = match method {\n        \u0026Method::GET =\u003e actions::READ,\n        \u0026Method::POST =\u003e actions::WRITE,\n        \u0026Method::PUT =\u003e actions::WRITE,\n        \u0026Method::PATCH =\u003e actions::WRITE,\n        \u0026Method::DELETE =\u003e actions::DELETE,\n        _ =\u003e {\n            return Err(ConfluxError::AuthError(format!(\n                \"Unsupported HTTP method: {}\",\n                method\n            )));\n        }\n    };\n\n    // 解析资源路径\n    let resource = if path.starts_with(\"/api/v1/\") {\n        // 移除API版本前缀\n        path.strip_prefix(\"/api/v1\").unwrap_or(path).to_string()\n    } else {\n        path.to_string()\n    };\n\n    Ok((resource, action.to_string()))\n}\n\n/// 检查是否为公共端点（不需要认证）\nfn is_public_endpoint(path: \u0026str) -\u003e bool {\n    let public_paths = [\n        \"/health\",\n        \"/ready\",\n        \"/_cluster/status\",\n        \"/metrics\",\n        \"/api/v1/auth/login\", // 登录端点\n    ];\n\n    public_paths.iter().any(|\u0026public_path| {\n        path == public_path || path.starts_with(\u0026format!(\"{}/\", public_path))\n    })\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use axum::http::{HeaderValue, Method, Uri};\n\n    #[test]\n    fn test_extract_auth_context() {\n        let mut headers = HeaderMap::new();\n        headers.insert(\n            \"authorization\",\n            HeaderValue::from_static(\"Bearer user123:tenant456\"),\n        );\n\n        let ctx = extract_auth_context(\u0026headers).unwrap();\n        assert_eq!(ctx.user_id, \"user123\");\n        assert_eq!(ctx.tenant_id, \"tenant456\");\n    }\n\n    #[test]\n    fn test_extract_auth_context_invalid() {\n        let mut headers = HeaderMap::new();\n        headers.insert(\"authorization\", HeaderValue::from_static(\"Invalid\"));\n\n        assert!(extract_auth_context(\u0026headers).is_err());\n    }\n\n    #[test]\n    fn test_parse_resource_and_action() {\n        let method = Method::GET;\n        let uri: Uri = \"/api/v1/tenants/tenant1/configs\".parse().unwrap();\n\n        let (resource, action) = parse_resource_and_action(\u0026method, \u0026uri).unwrap();\n        assert_eq!(resource, \"/tenants/tenant1/configs\");\n        assert_eq!(action, \"read\");\n\n        let method = Method::POST;\n        let uri: Uri = \"/api/v1/tenants/tenant1/configs\".parse().unwrap();\n\n        let (resource, action) = parse_resource_and_action(\u0026method, \u0026uri).unwrap();\n        assert_eq!(resource, \"/tenants/tenant1/configs\");\n        assert_eq!(action, \"write\");\n    }\n\n    #[test]\n    fn test_is_public_endpoint() {\n        assert!(is_public_endpoint(\"/health\"));\n        assert!(is_public_endpoint(\"/ready\"));\n        assert!(is_public_endpoint(\"/api/v1/auth/login\"));\n        assert!(!is_public_endpoint(\"/api/v1/configs\"));\n    }\n}\n","traces":[{"line":23,"address":[],"length":0,"stats":{"Line":0}},{"line":31,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":43,"address":[],"length":0,"stats":{"Line":0}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":48,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":58,"address":[],"length":0,"stats":{"Line":0}},{"line":59,"address":[],"length":0,"stats":{"Line":0}},{"line":60,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":66,"address":[],"length":0,"stats":{"Line":0}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":70,"address":[],"length":0,"stats":{"Line":0}},{"line":71,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":78,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":82,"address":[],"length":0,"stats":{"Line":0}},{"line":85,"address":[],"length":0,"stats":{"Line":0}},{"line":87,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":97,"address":[],"length":0,"stats":{"Line":2}},{"line":99,"address":[],"length":0,"stats":{"Line":4}},{"line":101,"address":[],"length":0,"stats":{"Line":4}},{"line":103,"address":[],"length":0,"stats":{"Line":2}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":1}},{"line":111,"address":[],"length":0,"stats":{"Line":1}},{"line":115,"address":[],"length":0,"stats":{"Line":1}},{"line":116,"address":[],"length":0,"stats":{"Line":1}},{"line":117,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":0}},{"line":122,"address":[],"length":0,"stats":{"Line":1}},{"line":123,"address":[],"length":0,"stats":{"Line":1}},{"line":125,"address":[],"length":0,"stats":{"Line":2}},{"line":126,"address":[],"length":0,"stats":{"Line":0}},{"line":129,"address":[],"length":0,"stats":{"Line":1}},{"line":133,"address":[],"length":0,"stats":{"Line":2}},{"line":134,"address":[],"length":0,"stats":{"Line":2}},{"line":137,"address":[],"length":0,"stats":{"Line":4}},{"line":138,"address":[],"length":0,"stats":{"Line":1}},{"line":139,"address":[],"length":0,"stats":{"Line":1}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":141,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":144,"address":[],"length":0,"stats":{"Line":0}},{"line":145,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":154,"address":[],"length":0,"stats":{"Line":2}},{"line":156,"address":[],"length":0,"stats":{"Line":0}},{"line":163,"address":[],"length":0,"stats":{"Line":4}},{"line":164,"address":[],"length":0,"stats":{"Line":4}},{"line":165,"address":[],"length":0,"stats":{"Line":4}},{"line":166,"address":[],"length":0,"stats":{"Line":4}},{"line":167,"address":[],"length":0,"stats":{"Line":4}},{"line":168,"address":[],"length":0,"stats":{"Line":4}},{"line":169,"address":[],"length":0,"stats":{"Line":4}},{"line":172,"address":[],"length":0,"stats":{"Line":17}},{"line":173,"address":[],"length":0,"stats":{"Line":23}}],"covered":27,"coverable":69},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","auth","mod.rs"],"content":"//! 认证与授权模块\n//! \n//! 基于Casbin实现的RBAC权限控制系统，支持多租户架构\n\npub mod api;\npub mod middleware;\npub mod service;\n\n#[cfg(test)]\nmod unit_tests;\n\npub use api::create_auth_routes;\npub use middleware::{authz_middleware, AuthzMiddleware};\npub use service::AuthzService;\n\n/// 认证上下文\n/// \n/// 包含从JWT或其他认证方式中提取的用户信息\n#[derive(Debug, Clone)]\npub struct AuthContext {\n    /// 用户ID\n    pub user_id: String,\n    /// 租户ID\n    pub tenant_id: String,\n    /// 用户角色列表（可选，用于缓存）\n    pub roles: Option\u003cVec\u003cString\u003e\u003e,\n}\n\nimpl AuthContext {\n    /// 创建新的认证上下文\n    pub fn new(user_id: String, tenant_id: String) -\u003e Self {\n        Self {\n            user_id,\n            tenant_id,\n            roles: None,\n        }\n    }\n\n    /// 创建带角色信息的认证上下文\n    pub fn with_roles(user_id: String, tenant_id: String, roles: Vec\u003cString\u003e) -\u003e Self {\n        Self {\n            user_id,\n            tenant_id,\n            roles: Some(roles),\n        }\n    }\n}\n\n/// 权限检查结果\n#[derive(Debug, Clone)]\npub struct PermissionResult {\n    /// 是否有权限\n    pub allowed: bool,\n    /// 检查的资源\n    pub resource: String,\n    /// 检查的操作\n    pub action: String,\n    /// 检查的用户\n    pub user_id: String,\n    /// 检查的租户\n    pub tenant_id: String,\n}\n\nimpl PermissionResult {\n    /// 创建允许的权限结果\n    pub fn allowed(user_id: String, tenant_id: String, resource: String, action: String) -\u003e Self {\n        Self {\n            allowed: true,\n            resource,\n            action,\n            user_id,\n            tenant_id,\n        }\n    }\n\n    /// 创建拒绝的权限结果\n    pub fn denied(user_id: String, tenant_id: String, resource: String, action: String) -\u003e Self {\n        Self {\n            allowed: false,\n            resource,\n            action,\n            user_id,\n            tenant_id,\n        }\n    }\n}\n\n/// 常用的操作类型\npub mod actions {\n    pub const READ: \u0026str = \"read\";\n    pub const WRITE: \u0026str = \"write\";\n    pub const DELETE: \u0026str = \"delete\";\n    pub const ADMIN: \u0026str = \"admin\";\n}\n\n/// 常用的角色类型\npub mod roles {\n    pub const SUPER_ADMIN: \u0026str = \"super_admin\";\n    pub const TENANT_ADMIN: \u0026str = \"tenant_admin\";\n    pub const DEVELOPER: \u0026str = \"developer\";\n    pub const VIEWER: \u0026str = \"viewer\";\n}\n\n/// 资源路径构建器\npub struct ResourcePath;\n\nimpl ResourcePath {\n    /// 构建配置资源路径\n    pub fn config(tenant: \u0026str, app: \u0026str, env: \u0026str, config_name: \u0026str) -\u003e String {\n        format!(\"/tenants/{}/apps/{}/envs/{}/configs/{}\", tenant, app, env, config_name)\n    }\n\n    /// 构建应用资源路径\n    pub fn app(tenant: \u0026str, app: \u0026str) -\u003e String {\n        format!(\"/tenants/{}/apps/{}\", tenant, app)\n    }\n\n    /// 构建环境资源路径\n    pub fn env(tenant: \u0026str, app: \u0026str, env: \u0026str) -\u003e String {\n        format!(\"/tenants/{}/apps/{}/envs/{}\", tenant, app, env)\n    }\n\n    /// 构建租户资源路径\n    pub fn tenant(tenant: \u0026str) -\u003e String {\n        format!(\"/tenants/{}\", tenant)\n    }\n\n    /// 构建管理资源路径\n    pub fn admin(tenant: \u0026str, resource: \u0026str) -\u003e String {\n        format!(\"/tenants/{}/admin/{}\", tenant, resource)\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_auth_context_creation() {\n        let ctx = AuthContext::new(\"user1\".to_string(), \"tenant1\".to_string());\n        assert_eq!(ctx.user_id, \"user1\");\n        assert_eq!(ctx.tenant_id, \"tenant1\");\n        assert!(ctx.roles.is_none());\n\n        let ctx_with_roles = AuthContext::with_roles(\n            \"user1\".to_string(),\n            \"tenant1\".to_string(),\n            vec![\"admin\".to_string()],\n        );\n        assert!(ctx_with_roles.roles.is_some());\n        assert_eq!(ctx_with_roles.roles.unwrap(), vec![\"admin\"]);\n    }\n\n    #[test]\n    fn test_permission_result() {\n        let allowed = PermissionResult::allowed(\n            \"user1\".to_string(),\n            \"tenant1\".to_string(),\n            \"/resource\".to_string(),\n            \"read\".to_string(),\n        );\n        assert!(allowed.allowed);\n\n        let denied = PermissionResult::denied(\n            \"user1\".to_string(),\n            \"tenant1\".to_string(),\n            \"/resource\".to_string(),\n            \"write\".to_string(),\n        );\n        assert!(!denied.allowed);\n    }\n\n    #[test]\n    fn test_resource_path_builder() {\n        assert_eq!(\n            ResourcePath::config(\"tenant1\", \"app1\", \"prod\", \"db.toml\"),\n            \"/tenants/tenant1/apps/app1/envs/prod/configs/db.toml\"\n        );\n\n        assert_eq!(\n            ResourcePath::app(\"tenant1\", \"app1\"),\n            \"/tenants/tenant1/apps/app1\"\n        );\n\n        assert_eq!(\n            ResourcePath::tenant(\"tenant1\"),\n            \"/tenants/tenant1\"\n        );\n    }\n}\n","traces":[{"line":31,"address":[],"length":0,"stats":{"Line":3}},{"line":40,"address":[],"length":0,"stats":{"Line":1}},{"line":44,"address":[],"length":0,"stats":{"Line":1}},{"line":66,"address":[],"length":0,"stats":{"Line":2}},{"line":77,"address":[],"length":0,"stats":{"Line":1}},{"line":109,"address":[],"length":0,"stats":{"Line":2}},{"line":110,"address":[],"length":0,"stats":{"Line":2}},{"line":114,"address":[],"length":0,"stats":{"Line":2}},{"line":115,"address":[],"length":0,"stats":{"Line":2}},{"line":119,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":124,"address":[],"length":0,"stats":{"Line":2}},{"line":125,"address":[],"length":0,"stats":{"Line":2}},{"line":129,"address":[],"length":0,"stats":{"Line":1}},{"line":130,"address":[],"length":0,"stats":{"Line":1}}],"covered":13,"coverable":15},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","auth","service.rs"],"content":"use casbin::{CoreApi, Enforcer, MgmtApi, RbacApi};\nuse sqlx_adapter::SqlxAdapter;\nuse std::sync::Arc;\nuse tokio::sync::RwLock;\nuse tracing::{debug, error, info};\n\nuse crate::error::{ConfluxError, Result};\n\n/// 认证授权服务\n/// \n/// 封装了Casbin Enforcer，提供更符合业务的接口\n#[derive(Clone)]\npub struct AuthzService {\n    enforcer: Arc\u003cRwLock\u003cEnforcer\u003e\u003e,\n}\n\nimpl AuthzService {\n    /// 创建一个新的AuthzService实例\n    ///\n    /// # Arguments\n    /// * `database_url` - PostgreSQL数据库连接字符串\n    ///\n    /// # Returns\n    /// * `Result\u003cSelf\u003e` - 成功时返回AuthzService实例\n    pub async fn new(database_url: \u0026str) -\u003e Result\u003cSelf\u003e {\n        info!(\"Initializing AuthzService with Casbin\");\n\n        // 创建SqlxAdapter\n        let adapter = SqlxAdapter::new(database_url, 8)\n            .await\n            .map_err(|e| {\n                error!(\"Failed to create SqlxAdapter: {}\", e);\n                ConfluxError::AuthError(format!(\"Failed to create SqlxAdapter: {}\", e))\n            })?;\n\n        // 创建Enforcer，使用model.conf文件\n        let model_path = \"src/auth/model.conf\";\n        let mut enforcer = Enforcer::new(model_path, adapter)\n            .await\n            .map_err(|e| {\n                error!(\"Failed to create Casbin Enforcer: {}\", e);\n                ConfluxError::AuthError(format!(\"Failed to create Casbin Enforcer: {}\", e))\n            })?;\n\n        // 构建角色链接，对于RBAC模型是必须的\n        enforcer.build_role_links().map_err(|e| {\n            error!(\"Failed to build role links: {}\", e);\n            ConfluxError::AuthError(format!(\"Failed to build role links: {}\", e))\n        })?;\n\n        info!(\"AuthzService initialized successfully\");\n        \n        Ok(Self {\n            enforcer: Arc::new(RwLock::new(enforcer)),\n        })\n    }\n\n    /// 核心检查函数：检查一个用户在特定租户下是否有权对资源执行操作\n    /// \n    /// # Arguments\n    /// * `user_id` - 发起请求的用户唯一标识\n    /// * `tenant` - 请求所属的租户\n    /// * `resource` - 被访问的资源路径，例如 \"/apps/my-app/envs/prod/configs/db.toml\"\n    /// * `action` - 执行的操作，例如 \"read\", \"write\"\n    /// \n    /// # Returns\n    /// * `Result\u003cbool\u003e` - 是否有权限\n    pub async fn check(\n        \u0026self,\n        user_id: \u0026str,\n        tenant: \u0026str,\n        resource: \u0026str,\n        action: \u0026str,\n    ) -\u003e Result\u003cbool\u003e {\n        debug!(\n            \"Checking permission: user={}, tenant={}, resource={}, action={}\",\n            user_id, tenant, resource, action\n        );\n\n        let enforcer = self.enforcer.read().await;\n        let result = enforcer\n            .enforce((user_id, tenant, resource, action))\n            .map_err(|e| {\n                error!(\"Permission check failed: {}\", e);\n                ConfluxError::AuthError(format!(\"Permission check failed: {}\", e))\n            })?;\n\n        debug!(\n            \"Permission check result: user={}, tenant={}, resource={}, action={}, allowed={}\",\n            user_id, tenant, resource, action, result\n        );\n\n        Ok(result)\n    }\n\n    /// 为角色添加权限\n    /// \n    /// # Arguments\n    /// * `role` - 角色名称\n    /// * `tenant` - 租户ID\n    /// * `resource` - 资源路径模式，支持通配符\n    /// * `action` - 操作类型\n    /// \n    /// # Returns\n    /// * `Result\u003cbool\u003e` - 是否成功添加\n    pub async fn add_permission_for_role(\n        \u0026self,\n        role: \u0026str,\n        tenant: \u0026str,\n        resource: \u0026str,\n        action: \u0026str,\n    ) -\u003e Result\u003cbool\u003e {\n        info!(\n            \"Adding permission: role={}, tenant={}, resource={}, action={}\",\n            role, tenant, resource, action\n        );\n\n        let mut enforcer = self.enforcer.write().await;\n        let rules = vec![vec![\n            role.to_string(),\n            tenant.to_string(),\n            resource.to_string(),\n            action.to_string(),\n        ]];\n\n        let result = enforcer.add_policies(rules).await.map_err(|e| {\n            error!(\"Failed to add permission: {}\", e);\n            ConfluxError::AuthError(format!(\"Failed to add permission: {}\", e))\n        })?;\n\n        info!(\n            \"Permission added successfully: role={}, tenant={}, resource={}, action={}\",\n            role, tenant, resource, action\n        );\n\n        Ok(result)\n    }\n\n    /// 移除角色的权限\n    /// \n    /// # Arguments\n    /// * `role` - 角色名称\n    /// * `tenant` - 租户ID\n    /// * `resource` - 资源路径模式\n    /// * `action` - 操作类型\n    /// \n    /// # Returns\n    /// * `Result\u003cbool\u003e` - 是否成功移除\n    pub async fn remove_permission_for_role(\n        \u0026self,\n        role: \u0026str,\n        tenant: \u0026str,\n        resource: \u0026str,\n        action: \u0026str,\n    ) -\u003e Result\u003cbool\u003e {\n        info!(\n            \"Removing permission: role={}, tenant={}, resource={}, action={}\",\n            role, tenant, resource, action\n        );\n\n        let mut enforcer = self.enforcer.write().await;\n        let rules = vec![vec![\n            role.to_string(),\n            tenant.to_string(),\n            resource.to_string(),\n            action.to_string(),\n        ]];\n\n        let result = enforcer.remove_policies(rules).await.map_err(|e| {\n            error!(\"Failed to remove permission: {}\", e);\n            ConfluxError::AuthError(format!(\"Failed to remove permission: {}\", e))\n        })?;\n\n        info!(\n            \"Permission removed successfully: role={}, tenant={}, resource={}, action={}\",\n            role, tenant, resource, action\n        );\n\n        Ok(result)\n    }\n\n    /// 为用户分配角色\n    /// \n    /// # Arguments\n    /// * `user_id` - 用户ID\n    /// * `role` - 角色名称\n    /// * `tenant` - 租户ID\n    /// \n    /// # Returns\n    /// * `Result\u003cbool\u003e` - 是否成功分配\n    pub async fn assign_role_to_user(\n        \u0026self,\n        user_id: \u0026str,\n        role: \u0026str,\n        tenant: \u0026str,\n    ) -\u003e Result\u003cbool\u003e {\n        info!(\n            \"Assigning role to user: user={}, role={}, tenant={}\",\n            user_id, role, tenant\n        );\n\n        let mut enforcer = self.enforcer.write().await;\n        let result = enforcer\n            .add_role_for_user(user_id, role, Some(tenant))\n            .await\n            .map_err(|e| {\n                error!(\"Failed to assign role: {}\", e);\n                ConfluxError::AuthError(format!(\"Failed to assign role: {}\", e))\n            })?;\n\n        info!(\n            \"Role assigned successfully: user={}, role={}, tenant={}\",\n            user_id, role, tenant\n        );\n\n        Ok(result)\n    }\n\n    /// 撤销用户的角色\n    /// \n    /// # Arguments\n    /// * `user_id` - 用户ID\n    /// * `role` - 角色名称\n    /// * `tenant` - 租户ID\n    /// \n    /// # Returns\n    /// * `Result\u003cbool\u003e` - 是否成功撤销\n    pub async fn revoke_role_from_user(\n        \u0026self,\n        user_id: \u0026str,\n        role: \u0026str,\n        tenant: \u0026str,\n    ) -\u003e Result\u003cbool\u003e {\n        info!(\n            \"Revoking role from user: user={}, role={}, tenant={}\",\n            user_id, role, tenant\n        );\n\n        let mut enforcer = self.enforcer.write().await;\n        let result = enforcer\n            .delete_role_for_user(user_id, role, Some(tenant))\n            .await\n            .map_err(|e| {\n                error!(\"Failed to revoke role: {}\", e);\n                ConfluxError::AuthError(format!(\"Failed to revoke role: {}\", e))\n            })?;\n\n        info!(\n            \"Role revoked successfully: user={}, role={}, tenant={}\",\n            user_id, role, tenant\n        );\n\n        Ok(result)\n    }\n\n    /// 获取用户在租户下的所有角色\n    /// \n    /// # Arguments\n    /// * `user_id` - 用户ID\n    /// * `tenant` - 租户ID\n    /// \n    /// # Returns\n    /// * `Result\u003cVec\u003cString\u003e\u003e` - 角色列表\n    pub async fn get_roles_for_user_in_tenant(\n        \u0026self,\n        user_id: \u0026str,\n        tenant: \u0026str,\n    ) -\u003e Result\u003cVec\u003cString\u003e\u003e {\n        debug!(\"Getting roles for user: user={}, tenant={}\", user_id, tenant);\n\n        let enforcer = self.enforcer.read().await;\n        let roles = enforcer.get_roles_for_user(user_id, Some(tenant));\n\n        debug!(\n            \"Found roles for user: user={}, tenant={}, roles={:?}\",\n            user_id, tenant, roles\n        );\n\n        Ok(roles)\n    }\n\n    /// 重新加载策略（用于热更新）\n    /// \n    /// # Returns\n    /// * `Result\u003c()\u003e` - 是否成功重新加载\n    pub async fn reload_policy(\u0026self) -\u003e Result\u003c()\u003e {\n        info!(\"Reloading Casbin policies\");\n\n        let mut enforcer = self.enforcer.write().await;\n        enforcer.load_policy().await.map_err(|e| {\n            error!(\"Failed to reload policy: {}\", e);\n            ConfluxError::AuthError(format!(\"Failed to reload policy: {}\", e))\n        })?;\n\n        // 重新构建角色链接\n        enforcer.build_role_links().map_err(|e| {\n            error!(\"Failed to rebuild role links: {}\", e);\n            ConfluxError::AuthError(format!(\"Failed to rebuild role links: {}\", e))\n        })?;\n\n        info!(\"Casbin policies reloaded successfully\");\n        Ok(())\n    }\n}\n","traces":[{"line":25,"address":[],"length":0,"stats":{"Line":0}},{"line":26,"address":[],"length":0,"stats":{"Line":0}},{"line":29,"address":[],"length":0,"stats":{"Line":0}},{"line":30,"address":[],"length":0,"stats":{"Line":0}},{"line":31,"address":[],"length":0,"stats":{"Line":0}},{"line":32,"address":[],"length":0,"stats":{"Line":0}},{"line":33,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":48,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":0}},{"line":68,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":80,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":82,"address":[],"length":0,"stats":{"Line":0}},{"line":83,"address":[],"length":0,"stats":{"Line":0}},{"line":84,"address":[],"length":0,"stats":{"Line":0}},{"line":85,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":106,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":0}},{"line":119,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":121,"address":[],"length":0,"stats":{"Line":0}},{"line":122,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":0}},{"line":126,"address":[],"length":0,"stats":{"Line":0}},{"line":127,"address":[],"length":0,"stats":{"Line":0}},{"line":128,"address":[],"length":0,"stats":{"Line":0}},{"line":132,"address":[],"length":0,"stats":{"Line":0}},{"line":149,"address":[],"length":0,"stats":{"Line":0}},{"line":156,"address":[],"length":0,"stats":{"Line":0}},{"line":157,"address":[],"length":0,"stats":{"Line":0}},{"line":161,"address":[],"length":0,"stats":{"Line":0}},{"line":162,"address":[],"length":0,"stats":{"Line":0}},{"line":163,"address":[],"length":0,"stats":{"Line":0}},{"line":164,"address":[],"length":0,"stats":{"Line":0}},{"line":165,"address":[],"length":0,"stats":{"Line":0}},{"line":166,"address":[],"length":0,"stats":{"Line":0}},{"line":169,"address":[],"length":0,"stats":{"Line":0}},{"line":170,"address":[],"length":0,"stats":{"Line":0}},{"line":171,"address":[],"length":0,"stats":{"Line":0}},{"line":174,"address":[],"length":0,"stats":{"Line":0}},{"line":175,"address":[],"length":0,"stats":{"Line":0}},{"line":179,"address":[],"length":0,"stats":{"Line":0}},{"line":191,"address":[],"length":0,"stats":{"Line":0}},{"line":197,"address":[],"length":0,"stats":{"Line":0}},{"line":198,"address":[],"length":0,"stats":{"Line":0}},{"line":202,"address":[],"length":0,"stats":{"Line":0}},{"line":203,"address":[],"length":0,"stats":{"Line":0}},{"line":204,"address":[],"length":0,"stats":{"Line":0}},{"line":205,"address":[],"length":0,"stats":{"Line":0}},{"line":206,"address":[],"length":0,"stats":{"Line":0}},{"line":207,"address":[],"length":0,"stats":{"Line":0}},{"line":208,"address":[],"length":0,"stats":{"Line":0}},{"line":212,"address":[],"length":0,"stats":{"Line":0}},{"line":228,"address":[],"length":0,"stats":{"Line":0}},{"line":234,"address":[],"length":0,"stats":{"Line":0}},{"line":235,"address":[],"length":0,"stats":{"Line":0}},{"line":239,"address":[],"length":0,"stats":{"Line":0}},{"line":240,"address":[],"length":0,"stats":{"Line":0}},{"line":241,"address":[],"length":0,"stats":{"Line":0}},{"line":242,"address":[],"length":0,"stats":{"Line":0}},{"line":243,"address":[],"length":0,"stats":{"Line":0}},{"line":244,"address":[],"length":0,"stats":{"Line":0}},{"line":245,"address":[],"length":0,"stats":{"Line":0}},{"line":248,"address":[],"length":0,"stats":{"Line":0}},{"line":249,"address":[],"length":0,"stats":{"Line":0}},{"line":253,"address":[],"length":0,"stats":{"Line":0}},{"line":264,"address":[],"length":0,"stats":{"Line":0}},{"line":269,"address":[],"length":0,"stats":{"Line":0}},{"line":271,"address":[],"length":0,"stats":{"Line":0}},{"line":272,"address":[],"length":0,"stats":{"Line":0}},{"line":274,"address":[],"length":0,"stats":{"Line":0}},{"line":275,"address":[],"length":0,"stats":{"Line":0}},{"line":279,"address":[],"length":0,"stats":{"Line":0}},{"line":286,"address":[],"length":0,"stats":{"Line":0}},{"line":287,"address":[],"length":0,"stats":{"Line":0}},{"line":289,"address":[],"length":0,"stats":{"Line":0}},{"line":290,"address":[],"length":0,"stats":{"Line":0}},{"line":291,"address":[],"length":0,"stats":{"Line":0}},{"line":292,"address":[],"length":0,"stats":{"Line":0}},{"line":296,"address":[],"length":0,"stats":{"Line":0}},{"line":297,"address":[],"length":0,"stats":{"Line":0}},{"line":298,"address":[],"length":0,"stats":{"Line":0}},{"line":301,"address":[],"length":0,"stats":{"Line":0}},{"line":302,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":95},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","auth","unit_tests.rs"],"content":"use super::*;\n\n/// 测试用的数据库设置\n/// \n/// 注意：这些测试需要一个运行中的PostgreSQL实例\n/// 可以通过docker运行：docker run -d -p 5432:5432 -e POSTGRES_PASSWORD=password postgres\n\n#[cfg(test)]\nmod integration_tests {\n    use super::*;\n    use sqlx::PgPool;\n\n    // 这个测试需要真实的数据库连接，所以我们先跳过\n    #[tokio::test]\n    #[ignore = \"需要PostgreSQL数据库\"]\n    async fn test_authz_service_creation() {\n        // 这里需要一个真实的数据库连接字符串\n        let database_url = \"postgresql://postgres:password@localhost:5432/conflux_test\";\n        \n        let pool = PgPool::connect(database_url).await.unwrap();\n        \n        // 创建casbin_rule表\n        sqlx::query(\n            r#\"\n            CREATE TABLE IF NOT EXISTS casbin_rule (\n                id SERIAL PRIMARY KEY,\n                ptype VARCHAR(100) NOT NULL,\n                v0 VARCHAR(100) NOT NULL,\n                v1 VARCHAR(100) NOT NULL,\n                v2 VARCHAR(100) NOT NULL,\n                v3 VARCHAR(100) NOT NULL,\n                v4 VARCHAR(100) NOT NULL DEFAULT '',\n                v5 VARCHAR(100) NOT NULL DEFAULT ''\n            )\n            \"#,\n        )\n        .execute(\u0026pool)\n        .await\n        .unwrap();\n\n        let authz_service = AuthzService::new(database_url).await.unwrap();\n        \n        // 测试权限检查\n        let result = authz_service\n            .check(\"user1\", \"tenant1\", \"/test\", \"read\")\n            .await\n            .unwrap();\n        \n        // 初始状态应该没有权限\n        assert!(!result);\n    }\n\n    #[tokio::test]\n    #[ignore = \"需要PostgreSQL数据库\"]\n    async fn test_permission_management() {\n        let database_url = \"postgresql://postgres:password@localhost:5432/conflux_test\";\n        let pool = PgPool::connect(database_url).await.unwrap();\n        \n        // 清理测试数据\n        sqlx::query(\"DELETE FROM casbin_rule\").execute(\u0026pool).await.unwrap();\n        \n        let authz_service = AuthzService::new(database_url).await.unwrap();\n        \n        // 添加权限\n        let result = authz_service\n            .add_permission_for_role(\"admin\", \"tenant1\", \"/test/*\", \"read\")\n            .await\n            .unwrap();\n        assert!(result);\n        \n        // 分配角色\n        let result = authz_service\n            .assign_role_to_user(\"user1\", \"admin\", \"tenant1\")\n            .await\n            .unwrap();\n        assert!(result);\n        \n        // 检查权限\n        let result = authz_service\n            .check(\"user1\", \"tenant1\", \"/test/config\", \"read\")\n            .await\n            .unwrap();\n        assert!(result);\n        \n        // 检查没有权限的操作\n        let result = authz_service\n            .check(\"user1\", \"tenant1\", \"/test/config\", \"write\")\n            .await\n            .unwrap();\n        assert!(!result);\n    }\n}\n\n#[cfg(test)]\nmod unit_tests {\n    use super::*;\n\n    #[test]\n    fn test_auth_context() {\n        let ctx = AuthContext::new(\"user1\".to_string(), \"tenant1\".to_string());\n        assert_eq!(ctx.user_id, \"user1\");\n        assert_eq!(ctx.tenant_id, \"tenant1\");\n        assert!(ctx.roles.is_none());\n    }\n\n    #[test]\n    fn test_permission_result() {\n        let allowed = PermissionResult::allowed(\n            \"user1\".to_string(),\n            \"tenant1\".to_string(),\n            \"/resource\".to_string(),\n            \"read\".to_string(),\n        );\n        assert!(allowed.allowed);\n        assert_eq!(allowed.user_id, \"user1\");\n        assert_eq!(allowed.tenant_id, \"tenant1\");\n        assert_eq!(allowed.resource, \"/resource\");\n        assert_eq!(allowed.action, \"read\");\n    }\n\n    #[test]\n    fn test_resource_path_builder() {\n        assert_eq!(\n            ResourcePath::config(\"tenant1\", \"app1\", \"prod\", \"db.toml\"),\n            \"/tenants/tenant1/apps/app1/envs/prod/configs/db.toml\"\n        );\n\n        assert_eq!(\n            ResourcePath::app(\"tenant1\", \"app1\"),\n            \"/tenants/tenant1/apps/app1\"\n        );\n\n        assert_eq!(\n            ResourcePath::tenant(\"tenant1\"),\n            \"/tenants/tenant1\"\n        );\n\n        assert_eq!(\n            ResourcePath::admin(\"tenant1\", \"users\"),\n            \"/tenants/tenant1/admin/users\"\n        );\n    }\n\n    #[test]\n    fn test_constants() {\n        assert_eq!(actions::READ, \"read\");\n        assert_eq!(actions::WRITE, \"write\");\n        assert_eq!(actions::DELETE, \"delete\");\n        assert_eq!(actions::ADMIN, \"admin\");\n\n        assert_eq!(roles::SUPER_ADMIN, \"super_admin\");\n        assert_eq!(roles::TENANT_ADMIN, \"tenant_admin\");\n        assert_eq!(roles::DEVELOPER, \"developer\");\n        assert_eq!(roles::VIEWER, \"viewer\");\n    }\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","config.rs"],"content":"use anyhow::Result;\nuse config::{Config, ConfigError, Environment, File};\nuse serde::{Deserialize, Serialize};\nuse std::path::Path;\n\n/// Main application configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct AppConfig {\n    pub server: ServerConfig,\n    pub raft: RaftConfig,\n    pub storage: StorageConfig,\n    pub database: DatabaseConfig,\n    pub security: SecurityConfig,\n    pub observability: ObservabilityConfig,\n}\n\n/// HTTP server configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ServerConfig {\n    pub host: String,\n    pub port: u16,\n    pub max_connections: usize,\n    pub request_timeout_secs: u64,\n}\n\n/// Raft consensus configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct RaftConfig {\n    pub node_id: u64,\n    pub cluster_name: String,\n    pub data_dir: String,\n    pub heartbeat_interval_ms: u64,\n    pub election_timeout_ms: u64,\n    pub snapshot_threshold: u64,\n    pub max_applied_log_to_keep: u64,\n}\n\n/// Storage configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct StorageConfig {\n    pub data_dir: String,\n    pub max_open_files: i32,\n    pub cache_size_mb: usize,\n    pub write_buffer_size_mb: usize,\n    pub max_write_buffer_number: i32,\n}\n\n/// Database configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct DatabaseConfig {\n    pub url: String,\n    pub max_connections: u32,\n    pub min_connections: u32,\n    pub connect_timeout_secs: u64,\n    pub idle_timeout_secs: u64,\n    pub max_lifetime_secs: u64,\n}\n\n/// Security configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct SecurityConfig {\n    pub jwt_secret: String,\n    pub jwt_expiration_hours: u64,\n    pub enable_mtls: bool,\n    pub cert_file: Option\u003cString\u003e,\n    pub key_file: Option\u003cString\u003e,\n    pub ca_file: Option\u003cString\u003e,\n}\n\n/// Observability configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ObservabilityConfig {\n    pub metrics_enabled: bool,\n    pub metrics_port: u16,\n    pub tracing_enabled: bool,\n    pub tracing_endpoint: Option\u003cString\u003e,\n    pub log_level: String,\n}\n\nimpl Default for AppConfig {\n    fn default() -\u003e Self {\n        Self {\n            server: ServerConfig {\n                host: \"0.0.0.0\".to_string(),\n                port: 8080,\n                max_connections: 1000,\n                request_timeout_secs: 30,\n            },\n            raft: RaftConfig {\n                node_id: 1,\n                cluster_name: \"conflux-cluster\".to_string(),\n                data_dir: \"./data/raft\".to_string(),\n                heartbeat_interval_ms: 500,\n                election_timeout_ms: 1500,\n                snapshot_threshold: 1000,\n                max_applied_log_to_keep: 1000,\n            },\n            storage: StorageConfig {\n                data_dir: \"./data/storage\".to_string(),\n                max_open_files: 1000,\n                cache_size_mb: 256,\n                write_buffer_size_mb: 64,\n                max_write_buffer_number: 3,\n            },\n            database: DatabaseConfig {\n                url: \"postgres://postgres:postgres@localhost:5432/conflux\".to_string(),\n                max_connections: 10,\n                min_connections: 1,\n                connect_timeout_secs: 30,\n                idle_timeout_secs: 600,\n                max_lifetime_secs: 3600,\n            },\n            security: SecurityConfig {\n                jwt_secret: \"your-secret-key-change-in-production\".to_string(),\n                jwt_expiration_hours: 24,\n                enable_mtls: false,\n                cert_file: None,\n                key_file: None,\n                ca_file: None,\n            },\n            observability: ObservabilityConfig {\n                metrics_enabled: true,\n                metrics_port: 9090,\n                tracing_enabled: true,\n                tracing_endpoint: None,\n                log_level: \"info\".to_string(),\n            },\n        }\n    }\n}\n\nimpl AppConfig {\n    /// Load configuration from files and environment variables\n    pub async fn load() -\u003e Result\u003cSelf\u003e {\n        let mut config_builder = Config::builder()\n            // Start with default values\n            .add_source(Config::try_from(\u0026AppConfig::default())?);\n\n        // Add configuration files if they exist\n        let config_files = [\n            \"config/default.toml\",\n            \"config/local.toml\",\n            \"/etc/conflux/config.toml\",\n        ];\n\n        for config_file in \u0026config_files {\n            if Path::new(config_file).exists() {\n                config_builder = config_builder.add_source(File::with_name(config_file));\n            }\n        }\n\n        // Add environment variables with CONFLUX_ prefix\n        config_builder = config_builder.add_source(\n            Environment::with_prefix(\"CONFLUX\")\n                .separator(\"_\")\n                .try_parsing(true),\n        );\n\n        let config = config_builder.build()?;\n        let app_config: AppConfig = config.try_deserialize()?;\n\n        // Validate configuration\n        app_config.validate()?;\n\n        Ok(app_config)\n    }\n\n    /// Validate the configuration\n    fn validate(\u0026self) -\u003e Result\u003c(), ConfigError\u003e {\n        // Validate server configuration\n        if self.server.port == 0 {\n            return Err(ConfigError::Message(\"Server port cannot be 0\".to_string()));\n        }\n\n        // Validate Raft configuration\n        if self.raft.node_id == 0 {\n            return Err(ConfigError::Message(\"Raft node_id cannot be 0\".to_string()));\n        }\n\n        if self.raft.heartbeat_interval_ms == 0 {\n            return Err(ConfigError::Message(\n                \"Raft heartbeat_interval_ms cannot be 0\".to_string(),\n            ));\n        }\n\n        if self.raft.election_timeout_ms \u003c= self.raft.heartbeat_interval_ms {\n            return Err(ConfigError::Message(\n                \"Raft election_timeout_ms must be greater than heartbeat_interval_ms\".to_string(),\n            ));\n        }\n\n        // Validate database configuration\n        if self.database.url.is_empty() {\n            return Err(ConfigError::Message(\n                \"Database URL cannot be empty\".to_string(),\n            ));\n        }\n\n        // Validate security configuration\n        if self.security.jwt_secret.is_empty() {\n            return Err(ConfigError::Message(\n                \"JWT secret cannot be empty\".to_string(),\n            ));\n        }\n\n        Ok(())\n    }\n}\n","traces":[{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":83,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":121,"address":[],"length":0,"stats":{"Line":0}},{"line":134,"address":[],"length":0,"stats":{"Line":0}},{"line":135,"address":[],"length":0,"stats":{"Line":0}},{"line":137,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":141,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":147,"address":[],"length":0,"stats":{"Line":0}},{"line":148,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":0}},{"line":154,"address":[],"length":0,"stats":{"Line":0}},{"line":155,"address":[],"length":0,"stats":{"Line":0}},{"line":156,"address":[],"length":0,"stats":{"Line":0}},{"line":159,"address":[],"length":0,"stats":{"Line":0}},{"line":160,"address":[],"length":0,"stats":{"Line":0}},{"line":163,"address":[],"length":0,"stats":{"Line":0}},{"line":165,"address":[],"length":0,"stats":{"Line":0}},{"line":169,"address":[],"length":0,"stats":{"Line":0}},{"line":171,"address":[],"length":0,"stats":{"Line":0}},{"line":172,"address":[],"length":0,"stats":{"Line":0}},{"line":176,"address":[],"length":0,"stats":{"Line":0}},{"line":177,"address":[],"length":0,"stats":{"Line":0}},{"line":180,"address":[],"length":0,"stats":{"Line":0}},{"line":181,"address":[],"length":0,"stats":{"Line":0}},{"line":182,"address":[],"length":0,"stats":{"Line":0}},{"line":186,"address":[],"length":0,"stats":{"Line":0}},{"line":187,"address":[],"length":0,"stats":{"Line":0}},{"line":188,"address":[],"length":0,"stats":{"Line":0}},{"line":193,"address":[],"length":0,"stats":{"Line":0}},{"line":194,"address":[],"length":0,"stats":{"Line":0}},{"line":195,"address":[],"length":0,"stats":{"Line":0}},{"line":200,"address":[],"length":0,"stats":{"Line":0}},{"line":201,"address":[],"length":0,"stats":{"Line":0}},{"line":202,"address":[],"length":0,"stats":{"Line":0}},{"line":206,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":43},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","error.rs"],"content":"use thiserror::Error;\n\n/// Main error type for the Conflux application\n#[derive(Error, Debug)]\npub enum ConfluxError {\n    #[error(\"Configuration error: {0}\")]\n    Config(#[from] config::ConfigError),\n\n    #[error(\"IO error: {0}\")]\n    Io(#[from] std::io::Error),\n\n    #[error(\"Serialization error: {0}\")]\n    Serialization(#[from] serde_json::Error),\n\n    #[error(\"Database error: {0}\")]\n    Database(#[from] sqlx::Error),\n\n    #[error(\"Raft error: {0}\")]\n    Raft(String),\n\n    #[error(\"Storage error: {0}\")]\n    Storage(String),\n\n    #[error(\"Network error: {0}\")]\n    Network(#[from] reqwest::Error),\n\n    #[error(\"RocksDB error: {0}\")]\n    RocksDB(#[from] rocksdb::Error),\n\n    #[error(\"Bincode error: {0}\")]\n    Bincode(#[from] bincode::error::DecodeError),\n\n    #[error(\"Authentication error: {0}\")]\n    Auth(String),\n\n    #[error(\"Authorization error: {0}\")]\n    Authz(String),\n\n    #[error(\"Auth error: {0}\")]\n    AuthError(String),\n\n    #[error(\"Validation error: {0}\")]\n    Validation(String),\n\n    #[error(\"Internal error: {0}\")]\n    Internal(String),\n}\n\n/// Result type alias for convenience\npub type Result\u003cT\u003e = std::result::Result\u003cT, ConfluxError\u003e;\n\nimpl ConfluxError {\n    pub fn raft(msg: impl Into\u003cString\u003e) -\u003e Self {\n        Self::Raft(msg.into())\n    }\n\n    pub fn storage(msg: impl Into\u003cString\u003e) -\u003e Self {\n        Self::Storage(msg.into())\n    }\n\n    pub fn auth(msg: impl Into\u003cString\u003e) -\u003e Self {\n        Self::Auth(msg.into())\n    }\n\n    pub fn authz(msg: impl Into\u003cString\u003e) -\u003e Self {\n        Self::Authz(msg.into())\n    }\n\n    pub fn validation(msg: impl Into\u003cString\u003e) -\u003e Self {\n        Self::Validation(msg.into())\n    }\n\n    pub fn internal(msg: impl Into\u003cString\u003e) -\u003e Self {\n        Self::Internal(msg.into())\n    }\n}\n","traces":[{"line":53,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":58,"address":[],"length":0,"stats":{"Line":0}},{"line":61,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":66,"address":[],"length":0,"stats":{"Line":0}},{"line":69,"address":[],"length":0,"stats":{"Line":4}},{"line":70,"address":[],"length":0,"stats":{"Line":4}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}}],"covered":2,"coverable":12},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","lib.rs"],"content":"pub mod auth;\npub mod config;\npub mod error;\npub mod raft;\npub mod protocol;\npub mod app;\n\npub use error::{ConfluxError, Result};\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","main.rs"],"content":"mod config;\nmod error;\nmod raft;\n\nuse anyhow::Result;\nuse config::AppConfig;\nuse tracing::{error, info};\nuse tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};\n\n#[tokio::main]\nasync fn main() -\u003e Result\u003c()\u003e {\n    // Initialize tracing\n    init_tracing()?;\n\n    info!(\"Starting Conflux distributed configuration center\");\n\n    // Load configuration\n    let config = AppConfig::load().await?;\n    info!(\"Configuration loaded successfully\");\n\n    // TODO: Initialize and start the application\n    info!(\n        \"Conflux server starting on {}:{}\",\n        config.server.host, config.server.port\n    );\n\n    // Keep the application running\n    tokio::signal::ctrl_c().await?;\n    info!(\"Shutting down Conflux server\");\n\n    Ok(())\n}\n\nfn init_tracing() -\u003e Result\u003c()\u003e {\n    tracing_subscriber::registry()\n        .with(\n            tracing_subscriber::EnvFilter::try_from_default_env()\n                .unwrap_or_else(|_| \"conflux=debug,tower_http=debug\".into()),\n        )\n        .with(tracing_subscriber::fmt::layer())\n        .init();\n\n    Ok(())\n}\n","traces":[{"line":11,"address":[],"length":0,"stats":{"Line":0}},{"line":13,"address":[],"length":0,"stats":{"Line":0}},{"line":15,"address":[],"length":0,"stats":{"Line":0}},{"line":18,"address":[],"length":0,"stats":{"Line":0}},{"line":19,"address":[],"length":0,"stats":{"Line":0}},{"line":22,"address":[],"length":0,"stats":{"Line":0}},{"line":23,"address":[],"length":0,"stats":{"Line":0}},{"line":24,"address":[],"length":0,"stats":{"Line":0}},{"line":28,"address":[],"length":0,"stats":{"Line":0}},{"line":29,"address":[],"length":0,"stats":{"Line":0}},{"line":31,"address":[],"length":0,"stats":{"Line":0}},{"line":34,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":43,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":17},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","protocol","http","handlers.rs"],"content":"use crate::protocol::http::{AppState, CreateVersionRequest, UpdateReleasesRequest, FetchConfigResponse};\nuse crate::raft::types::*;\nuse crate::raft::client::{create_write_request, create_get_config_request};\nuse axum::{\n    extract::{Path, Query, State},\n    http::StatusCode,\n    response::Json,\n};\nuse serde_json::{json, Value};\nuse std::collections::BTreeMap;\nuse tracing::{debug, error, info};\n\n/// 创建配置版本处理器\n/// POST /api/v1/configs/{tenant}/{app}/{env}/{name}/versions\npub async fn create_version_handler(\n    Path((tenant, app, env, name)): Path\u003c(String, String, String, String)\u003e,\n    State(app_state): State\u003cAppState\u003e,\n    Json(request): Json\u003cCreateVersionRequest\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    info!(\"Creating version for config: {}/{}/{}/{}\", tenant, app, env, name);\n\n    let namespace = ConfigNamespace { tenant, app, env };\n\n    // 首先需要找到配置的ID\n    let config = match app_state.core_handle.store().get_config(\u0026namespace, \u0026name).await {\n        Some(config) =\u003e config,\n        None =\u003e {\n            error!(\"Config not found: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            return Err(StatusCode::NOT_FOUND);\n        }\n    };\n\n    // 创建 Raft 命令\n    let command = RaftCommand::CreateVersion {\n        config_id: config.id,\n        content: request.content.into_bytes(),\n        format: request.format,\n        creator_id: request.creator_id.unwrap_or_else(|| \"system\".to_string()).parse().unwrap_or(0),\n        description: request.description.unwrap_or_else(|| \"Created via API\".to_string()),\n    };\n\n    // 提交到 Raft\n    let write_request = create_write_request(command);\n    match app_state.core_handle.raft_client().write(write_request).await {\n        Ok(response) =\u003e {\n            info!(\"Version created successfully for {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            Ok(Json(json!({\n                \"success\": true,\n                \"data\": response.data,\n                \"message\": \"Version created successfully\"\n            })))\n        }\n        Err(e) =\u003e {\n            error!(\"Failed to create version: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// 更新发布规则处理器\n/// PUT /api/v1/configs/{tenant}/{app}/{env}/{name}/releases\npub async fn update_releases_handler(\n    Path((tenant, app, env, name)): Path\u003c(String, String, String, String)\u003e,\n    State(app_state): State\u003cAppState\u003e,\n    Json(request): Json\u003cUpdateReleasesRequest\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    info!(\"Updating releases for config: {}/{}/{}/{}\", tenant, app, env, name);\n\n    let namespace = ConfigNamespace { tenant, app, env };\n\n    // 首先需要找到配置的ID\n    let config = match app_state.core_handle.store().get_config(\u0026namespace, \u0026name).await {\n        Some(config) =\u003e config,\n        None =\u003e {\n            error!(\"Config not found: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            return Err(StatusCode::NOT_FOUND);\n        }\n    };\n\n    // 创建 Raft 命令\n    let command = RaftCommand::UpdateReleaseRules {\n        config_id: config.id,\n        releases: request.releases,\n    };\n\n    // 提交到 Raft\n    let write_request = create_write_request(command);\n    match app_state.core_handle.raft_client().write(write_request).await {\n        Ok(response) =\u003e {\n            info!(\"Releases updated successfully for {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            Ok(Json(json!({\n                \"success\": true,\n                \"data\": response.data,\n                \"message\": \"Releases updated successfully\"\n            })))\n        }\n        Err(e) =\u003e {\n            error!(\"Failed to update releases: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// 获取发布配置处理器\n/// GET /api/v1/fetch/configs/{tenant}/{app}/{env}/{name}\npub async fn fetch_config_handler(\n    Path((tenant, app, env, name)): Path\u003c(String, String, String, String)\u003e,\n    Query(params): Query\u003cBTreeMap\u003cString, String\u003e\u003e,\n    State(app_state): State\u003cAppState\u003e,\n) -\u003e Result\u003cJson\u003cFetchConfigResponse\u003e, StatusCode\u003e {\n    debug!(\"Fetching config: {}/{}/{}/{} with labels: {:?}\", tenant, app, env, name, params);\n\n    let namespace = ConfigNamespace { tenant, app, env };\n    \n    // 创建读取请求\n    let read_request = create_get_config_request(namespace.clone(), name.clone(), params);\n    \n    match app_state.core_handle.raft_client().read(read_request).await {\n        Ok(response) =\u003e {\n            if let Some(data) = response.data {\n                // 解析返回的数据\n                if let Ok(config_data) = serde_json::from_value::\u003cserde_json::Value\u003e(data) {\n                    if let (Some(config), Some(version)) = (\n                        config_data.get(\"config\"),\n                        config_data.get(\"version\")\n                    ) {\n                        let fetch_response = FetchConfigResponse {\n                            namespace: namespace.clone(),\n                            name: name.clone(),\n                            content: version.get(\"content\").and_then(|v| v.as_str()).unwrap_or(\"\").to_string(),\n                            format: config.get(\"format\").and_then(|v| v.as_str()).and_then(|s| {\n                                match s {\n                                    \"Json\" =\u003e Some(ConfigFormat::Json),\n                                    \"Yaml\" =\u003e Some(ConfigFormat::Yaml),\n                                    \"Toml\" =\u003e Some(ConfigFormat::Toml),\n                                    \"Properties\" =\u003e Some(ConfigFormat::Properties),\n                                    \"Xml\" =\u003e Some(ConfigFormat::Xml),\n                                    _ =\u003e None,\n                                }\n                            }).unwrap_or(ConfigFormat::Json),\n                            version_id: version.get(\"id\").and_then(|v| v.as_u64()).unwrap_or(0),\n                            hash: version.get(\"hash\").and_then(|v| v.as_str()).unwrap_or(\"\").to_string(),\n                            created_at: chrono::Utc::now(), // TODO: 从实际数据中获取\n                        };\n                        \n                        info!(\"Config fetched successfully: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n                        return Ok(Json(fetch_response));\n                    }\n                }\n            }\n            \n            error!(\"Config not found: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            Err(StatusCode::NOT_FOUND)\n        }\n        Err(e) =\u003e {\n            error!(\"Failed to fetch config: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// 获取配置元数据处理器\n/// GET /api/v1/configs/{tenant}/{app}/{env}/{name}\npub async fn get_config_handler(\n    Path((tenant, app, env, name)): Path\u003c(String, String, String, String)\u003e,\n    State(app_state): State\u003cAppState\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    debug!(\"Getting config metadata: {}/{}/{}/{}\", tenant, app, env, name);\n\n    let namespace = ConfigNamespace { tenant, app, env };\n\n    // 直接从存储中读取配置元数据\n    match app_state.core_handle.store().get_config(\u0026namespace, \u0026name).await {\n        Some(config) =\u003e {\n            info!(\"Config metadata retrieved: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            Ok(Json(json!(config)))\n        }\n        None =\u003e {\n            debug!(\"Config not found: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            Err(StatusCode::NOT_FOUND)\n        }\n    }\n}\n\n/// 列出配置版本处理器\n/// GET /api/v1/configs/{tenant}/{app}/{env}/{name}/versions\npub async fn list_versions_handler(\n    Path((tenant, app, env, name)): Path\u003c(String, String, String, String)\u003e,\n    State(app_state): State\u003cAppState\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    debug!(\"Listing versions for config: {}/{}/{}/{}\", tenant, app, env, name);\n\n    let namespace = ConfigNamespace { tenant, app, env };\n\n    // 首先需要找到配置的ID\n    let config = match app_state.core_handle.store().get_config(\u0026namespace, \u0026name).await {\n        Some(config) =\u003e config,\n        None =\u003e {\n            debug!(\"Config not found: {}/{}/{}/{}\", namespace.tenant, namespace.app, namespace.env, name);\n            return Err(StatusCode::NOT_FOUND);\n        }\n    };\n\n    // 从存储中获取配置版本列表\n    let versions = app_state.core_handle.store().list_config_versions(config.id).await;\n    info!(\"Listed {} versions for config: {}/{}/{}/{}\", versions.len(), namespace.tenant, namespace.app, namespace.env, name);\n    Ok(Json(json!({\n        \"versions\": versions,\n        \"count\": versions.len()\n    })))\n}\n\n/// 集群状态处理器\n/// GET /_cluster/status\npub async fn cluster_status_handler(\n    State(app_state): State\u003cAppState\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    debug!(\"Getting cluster status\");\n\n    match app_state.core_handle.raft_client().get_cluster_status().await {\n        Ok(status) =\u003e {\n            debug!(\"Cluster status retrieved successfully\");\n            Ok(Json(json!(status)))\n        }\n        Err(e) =\u003e {\n            error!(\"Failed to get cluster status: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// 添加节点处理器\n/// POST /_cluster/nodes\npub async fn add_node_handler(\n    State(_app_state): State\u003cAppState\u003e,\n    Json(_request): Json\u003cValue\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    // TODO: 实现添加节点逻辑\n    info!(\"Add node request received (not implemented yet)\");\n    Err(StatusCode::NOT_IMPLEMENTED)\n}\n\n/// 移除节点处理器\n/// DELETE /_cluster/nodes/{node_id}\npub async fn remove_node_handler(\n    Path(_node_id): Path\u003cu64\u003e,\n    State(_app_state): State\u003cAppState\u003e,\n) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    // TODO: 实现移除节点逻辑\n    info!(\"Remove node request received (not implemented yet)\");\n    Err(StatusCode::NOT_IMPLEMENTED)\n}\n","traces":[{"line":15,"address":[],"length":0,"stats":{"Line":0}},{"line":20,"address":[],"length":0,"stats":{"Line":0}},{"line":22,"address":[],"length":0,"stats":{"Line":0}},{"line":25,"address":[],"length":0,"stats":{"Line":0}},{"line":26,"address":[],"length":0,"stats":{"Line":0}},{"line":28,"address":[],"length":0,"stats":{"Line":0}},{"line":29,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}},{"line":43,"address":[],"length":0,"stats":{"Line":0}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":48,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":53,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":0}},{"line":55,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":69,"address":[],"length":0,"stats":{"Line":0}},{"line":72,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":82,"address":[],"length":0,"stats":{"Line":0}},{"line":83,"address":[],"length":0,"stats":{"Line":0}},{"line":87,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":90,"address":[],"length":0,"stats":{"Line":0}},{"line":91,"address":[],"length":0,"stats":{"Line":0}},{"line":92,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":97,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":99,"address":[],"length":0,"stats":{"Line":0}},{"line":106,"address":[],"length":0,"stats":{"Line":0}},{"line":111,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":116,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":0}},{"line":119,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":122,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":0}},{"line":124,"address":[],"length":0,"stats":{"Line":0}},{"line":125,"address":[],"length":0,"stats":{"Line":0}},{"line":128,"address":[],"length":0,"stats":{"Line":0}},{"line":129,"address":[],"length":0,"stats":{"Line":0}},{"line":130,"address":[],"length":0,"stats":{"Line":0}},{"line":131,"address":[],"length":0,"stats":{"Line":0}},{"line":141,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":147,"address":[],"length":0,"stats":{"Line":0}},{"line":152,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":0}},{"line":155,"address":[],"length":0,"stats":{"Line":0}},{"line":156,"address":[],"length":0,"stats":{"Line":0}},{"line":157,"address":[],"length":0,"stats":{"Line":0}},{"line":164,"address":[],"length":0,"stats":{"Line":0}},{"line":168,"address":[],"length":0,"stats":{"Line":0}},{"line":170,"address":[],"length":0,"stats":{"Line":0}},{"line":173,"address":[],"length":0,"stats":{"Line":0}},{"line":174,"address":[],"length":0,"stats":{"Line":0}},{"line":175,"address":[],"length":0,"stats":{"Line":0}},{"line":176,"address":[],"length":0,"stats":{"Line":0}},{"line":179,"address":[],"length":0,"stats":{"Line":0}},{"line":180,"address":[],"length":0,"stats":{"Line":0}},{"line":187,"address":[],"length":0,"stats":{"Line":0}},{"line":191,"address":[],"length":0,"stats":{"Line":0}},{"line":193,"address":[],"length":0,"stats":{"Line":0}},{"line":196,"address":[],"length":0,"stats":{"Line":0}},{"line":197,"address":[],"length":0,"stats":{"Line":0}},{"line":199,"address":[],"length":0,"stats":{"Line":0}},{"line":200,"address":[],"length":0,"stats":{"Line":0}},{"line":205,"address":[],"length":0,"stats":{"Line":0}},{"line":206,"address":[],"length":0,"stats":{"Line":0}},{"line":207,"address":[],"length":0,"stats":{"Line":0}},{"line":208,"address":[],"length":0,"stats":{"Line":0}},{"line":209,"address":[],"length":0,"stats":{"Line":0}},{"line":215,"address":[],"length":0,"stats":{"Line":0}},{"line":218,"address":[],"length":0,"stats":{"Line":0}},{"line":220,"address":[],"length":0,"stats":{"Line":0}},{"line":221,"address":[],"length":0,"stats":{"Line":0}},{"line":222,"address":[],"length":0,"stats":{"Line":0}},{"line":223,"address":[],"length":0,"stats":{"Line":0}},{"line":225,"address":[],"length":0,"stats":{"Line":0}},{"line":226,"address":[],"length":0,"stats":{"Line":0}},{"line":227,"address":[],"length":0,"stats":{"Line":0}},{"line":234,"address":[],"length":0,"stats":{"Line":0}},{"line":239,"address":[],"length":0,"stats":{"Line":0}},{"line":240,"address":[],"length":0,"stats":{"Line":0}},{"line":245,"address":[],"length":0,"stats":{"Line":0}},{"line":250,"address":[],"length":0,"stats":{"Line":0}},{"line":251,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":104},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","protocol","http","middleware.rs"],"content":"use axum::{\n    extract::Request,\n    http::{HeaderMap, StatusCode},\n    middleware::Next,\n    response::Response,\n};\nuse std::time::Instant;\nuse tracing::{debug, info, warn};\n\n/// 请求日志中间件\npub async fn logging_middleware(request: Request, next: Next) -\u003e Response {\n    let start = Instant::now();\n    let method = request.method().clone();\n    let uri = request.uri().clone();\n    let headers = request.headers().clone();\n\n    // 提取客户端IP（如果有的话）\n    let client_ip = extract_client_ip(\u0026headers);\n\n    debug!(\n        \"Incoming request: {} {} from {}\",\n        method,\n        uri,\n        client_ip.unwrap_or_else(|| \"unknown\".to_string())\n    );\n\n    // 处理请求\n    let response = next.run(request).await;\n\n    let duration = start.elapsed();\n    let status = response.status();\n\n    // 记录请求完成日志\n    if status.is_success() {\n        info!(\n            \"Request completed: {} {} -\u003e {} in {:?}\",\n            method, uri, status, duration\n        );\n    } else if status.is_client_error() {\n        warn!(\n            \"Client error: {} {} -\u003e {} in {:?}\",\n            method, uri, status, duration\n        );\n    } else {\n        warn!(\n            \"Server error: {} {} -\u003e {} in {:?}\",\n            method, uri, status, duration\n        );\n    }\n\n    response\n}\n\n/// 认证中间件（占位符实现）\n/// \n/// 在后续的 Epic 中，这里会集成 JWT 验证和 RBAC 授权\npub async fn auth_middleware(request: Request, next: Next) -\u003e Result\u003cResponse, StatusCode\u003e {\n    let headers = request.headers();\n\n    // 检查 Authorization 头\n    if let Some(auth_header) = headers.get(\"authorization\") {\n        if let Ok(auth_str) = auth_header.to_str() {\n            if auth_str.starts_with(\"Bearer \") {\n                // TODO: 在后续的 Epic 中实现 JWT 验证\n                debug!(\"Authorization header found: {}\", auth_str);\n                \n                // 暂时允许所有带有 Bearer token 的请求通过\n                return Ok(next.run(request).await);\n            }\n        }\n    }\n\n    // 对于某些端点，我们允许匿名访问\n    let path = request.uri().path();\n    if is_public_endpoint(path) {\n        debug!(\"Public endpoint accessed: {}\", path);\n        return Ok(next.run(request).await);\n    }\n\n    // 其他请求需要认证\n    warn!(\"Unauthorized request to: {}\", path);\n    Err(StatusCode::UNAUTHORIZED)\n}\n\n/// 速率限制中间件（占位符实现）\n/// \n/// 在后续的 Epic 中，这里会实现基于令牌桶或滑动窗口的速率限制\npub async fn rate_limit_middleware(request: Request, next: Next) -\u003e Result\u003cResponse, StatusCode\u003e {\n    let client_ip = extract_client_ip(request.headers());\n    \n    // TODO: 实现实际的速率限制逻辑\n    debug!(\n        \"Rate limit check for client: {}\",\n        client_ip.unwrap_or_else(|| \"unknown\".to_string())\n    );\n\n    // 暂时允许所有请求通过\n    Ok(next.run(request).await)\n}\n\n/// 请求ID中间件\n/// \n/// 为每个请求生成唯一的ID，用于链路追踪\npub async fn request_id_middleware(mut request: Request, next: Next) -\u003e Response {\n    let request_id = generate_request_id();\n    \n    // 将请求ID添加到请求头中\n    request.headers_mut().insert(\n        \"x-request-id\",\n        request_id.parse().unwrap(),\n    );\n\n    debug!(\"Request ID generated: {}\", request_id);\n\n    let mut response = next.run(request).await;\n\n    // 将请求ID添加到响应头中\n    response.headers_mut().insert(\n        \"x-request-id\",\n        request_id.parse().unwrap(),\n    );\n\n    response\n}\n\n/// 提取客户端IP地址\nfn extract_client_ip(headers: \u0026HeaderMap) -\u003e Option\u003cString\u003e {\n    // 尝试从各种可能的头部提取客户端IP\n    let ip_headers = [\n        \"x-forwarded-for\",\n        \"x-real-ip\",\n        \"x-client-ip\",\n        \"cf-connecting-ip\",\n    ];\n\n    for header_name in \u0026ip_headers {\n        if let Some(header_value) = headers.get(*header_name) {\n            if let Ok(ip_str) = header_value.to_str() {\n                // X-Forwarded-For 可能包含多个IP，取第一个\n                let ip = ip_str.split(',').next().unwrap_or(ip_str).trim();\n                if !ip.is_empty() {\n                    return Some(ip.to_string());\n                }\n            }\n        }\n    }\n\n    None\n}\n\n/// 检查是否为公共端点（不需要认证）\nfn is_public_endpoint(path: \u0026str) -\u003e bool {\n    let public_paths = [\n        \"/health\",\n        \"/ready\",\n        \"/_cluster/status\",\n        \"/api/v1/fetch/configs\", // 配置获取端点允许匿名访问\n    ];\n\n    public_paths.iter().any(|\u0026public_path| {\n        path == public_path || path.starts_with(\u0026format!(\"{}/\", public_path))\n    })\n}\n\n/// 生成请求ID\nfn generate_request_id() -\u003e String {\n    use std::sync::atomic::{AtomicU64, Ordering};\n    \n    static COUNTER: AtomicU64 = AtomicU64::new(0);\n    \n    let timestamp = chrono::Utc::now().timestamp_millis() as u64;\n    let counter = COUNTER.fetch_add(1, Ordering::Relaxed);\n    \n    format!(\"{:x}-{:x}\", timestamp, counter)\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use axum::http::{HeaderValue};\n\n    #[test]\n    fn test_extract_client_ip() {\n        let mut headers = HeaderMap::new();\n        \n        // 测试没有IP头的情况\n        assert_eq!(extract_client_ip(\u0026headers), None);\n        \n        // 测试 X-Forwarded-For 头\n        headers.insert(\"x-forwarded-for\", HeaderValue::from_static(\"***********, ********\"));\n        assert_eq!(extract_client_ip(\u0026headers), Some(\"***********\".to_string()));\n        \n        // 测试 X-Real-IP 头\n        headers.clear();\n        headers.insert(\"x-real-ip\", HeaderValue::from_static(\"***********\"));\n        assert_eq!(extract_client_ip(\u0026headers), Some(\"***********\".to_string()));\n    }\n\n    #[test]\n    fn test_is_public_endpoint() {\n        assert!(is_public_endpoint(\"/health\"));\n        assert!(is_public_endpoint(\"/ready\"));\n        assert!(is_public_endpoint(\"/_cluster/status\"));\n        assert!(is_public_endpoint(\"/api/v1/fetch/configs/tenant/app/env/config\"));\n        \n        assert!(!is_public_endpoint(\"/api/v1/configs/tenant/app/env/config/versions\"));\n        assert!(!is_public_endpoint(\"/api/v1/configs/tenant/app/env/config/releases\"));\n        assert!(!is_public_endpoint(\"/_cluster/nodes\"));\n    }\n\n    #[test]\n    fn test_generate_request_id() {\n        let id1 = generate_request_id();\n        let id2 = generate_request_id();\n        \n        // 确保生成的ID不同\n        assert_ne!(id1, id2);\n        \n        // 确保ID格式正确（包含连字符）\n        assert!(id1.contains('-'));\n        assert!(id2.contains('-'));\n    }\n\n    #[tokio::test]\n    async fn test_middleware_functions_exist() {\n        // 这个测试只是确保中间件函数能够编译\n        // 实际的功能测试需要更复杂的设置\n        \n        // 测试请求ID生成\n        let request_id = generate_request_id();\n        assert!(!request_id.is_empty());\n        \n        // 测试公共端点检查\n        assert!(is_public_endpoint(\"/health\"));\n        assert!(!is_public_endpoint(\"/private\"));\n    }\n}\n","traces":[{"line":11,"address":[],"length":0,"stats":{"Line":0}},{"line":12,"address":[],"length":0,"stats":{"Line":0}},{"line":13,"address":[],"length":0,"stats":{"Line":0}},{"line":14,"address":[],"length":0,"stats":{"Line":0}},{"line":15,"address":[],"length":0,"stats":{"Line":0}},{"line":18,"address":[],"length":0,"stats":{"Line":0}},{"line":20,"address":[],"length":0,"stats":{"Line":0}},{"line":21,"address":[],"length":0,"stats":{"Line":0}},{"line":22,"address":[],"length":0,"stats":{"Line":0}},{"line":23,"address":[],"length":0,"stats":{"Line":0}},{"line":24,"address":[],"length":0,"stats":{"Line":0}},{"line":28,"address":[],"length":0,"stats":{"Line":0}},{"line":30,"address":[],"length":0,"stats":{"Line":0}},{"line":31,"address":[],"length":0,"stats":{"Line":0}},{"line":34,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":58,"address":[],"length":0,"stats":{"Line":0}},{"line":61,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":63,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":68,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":77,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":82,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":92,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":104,"address":[],"length":0,"stats":{"Line":0}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":0}},{"line":110,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":115,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":0}},{"line":127,"address":[],"length":0,"stats":{"Line":3}},{"line":129,"address":[],"length":0,"stats":{"Line":3}},{"line":130,"address":[],"length":0,"stats":{"Line":3}},{"line":131,"address":[],"length":0,"stats":{"Line":3}},{"line":132,"address":[],"length":0,"stats":{"Line":3}},{"line":133,"address":[],"length":0,"stats":{"Line":3}},{"line":136,"address":[],"length":0,"stats":{"Line":15}},{"line":137,"address":[],"length":0,"stats":{"Line":9}},{"line":138,"address":[],"length":0,"stats":{"Line":2}},{"line":142,"address":[],"length":0,"stats":{"Line":2}},{"line":148,"address":[],"length":0,"stats":{"Line":1}},{"line":152,"address":[],"length":0,"stats":{"Line":9}},{"line":153,"address":[],"length":0,"stats":{"Line":9}},{"line":154,"address":[],"length":0,"stats":{"Line":9}},{"line":155,"address":[],"length":0,"stats":{"Line":9}},{"line":156,"address":[],"length":0,"stats":{"Line":9}},{"line":157,"address":[],"length":0,"stats":{"Line":9}},{"line":160,"address":[],"length":0,"stats":{"Line":36}},{"line":161,"address":[],"length":0,"stats":{"Line":50}},{"line":166,"address":[],"length":0,"stats":{"Line":3}},{"line":171,"address":[],"length":0,"stats":{"Line":3}},{"line":172,"address":[],"length":0,"stats":{"Line":3}},{"line":174,"address":[],"length":0,"stats":{"Line":3}}],"covered":23,"coverable":74},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","protocol","http","mod.rs"],"content":"use crate::app::CoreAppHandle;\nuse crate::protocol::{ProtocolConfig, ProtocolPlugin};\nuse async_trait::async_trait;\nuse axum::{\n    extract::State,\n    http::StatusCode,\n    middleware::from_fn,\n    response::Json,\n    routing::{get, post, put},\n    Router,\n};\nuse serde_json::{json, Value};\nuse std::net::SocketAddr;\nuse tower::ServiceBuilder;\nuse tower_http::{cors::CorsLayer, trace::TraceLayer};\nuse tracing::{info, warn};\n\npub mod handlers;\npub mod middleware;\npub mod schemas;\n\npub use handlers::*;\npub use middleware::logging_middleware;\npub use schemas::*;\n\n/// HTTP 协议插件实现\npub struct HttpProtocol;\n\n#[async_trait]\nimpl ProtocolPlugin for HttpProtocol {\n    fn name(\u0026self) -\u003e \u0026'static str {\n        \"http-rest\"\n    }\n\n    async fn start(\u0026self, core_handle: CoreAppHandle, config: ProtocolConfig) -\u003e anyhow::Result\u003c()\u003e {\n        info!(\"Starting HTTP protocol plugin on {}\", config.listen_addr);\n\n        // 创建应用状态\n        let app_state = AppState::new(core_handle);\n\n        // 构建路由\n        let app = create_router(app_state);\n\n        // 解析监听地址\n        let addr: SocketAddr = config.listen_addr.parse()\n            .map_err(|e| anyhow::anyhow!(\"Invalid listen address: {}\", e))?;\n\n        // 启动服务器\n        let listener = tokio::net::TcpListener::bind(addr).await?;\n        info!(\"HTTP server listening on {}\", addr);\n\n        axum::serve(listener, app).await?;\n\n        Ok(())\n    }\n\n    async fn health_check(\u0026self) -\u003e bool {\n        // TODO: 实现更复杂的健康检查逻辑\n        true\n    }\n\n    async fn shutdown(\u0026self) -\u003e anyhow::Result\u003c()\u003e {\n        info!(\"Shutting down HTTP protocol plugin\");\n        Ok(())\n    }\n}\n\n/// 应用状态，包含核心服务的引用\n#[derive(Clone)]\npub struct AppState {\n    pub core_handle: CoreAppHandle,\n}\n\nimpl AppState {\n    pub fn new(core_handle: CoreAppHandle) -\u003e Self {\n        Self { core_handle }\n    }\n}\n\n/// 创建 Axum 路由器\nfn create_router(app_state: AppState) -\u003e Router {\n    Router::new()\n        // 健康检查端点（公共访问）\n        .route(\"/health\", get(health_handler))\n        .route(\"/ready\", get(readiness_handler))\n\n        // API v1 路由（暂时不添加授权中间件）\n        .nest(\"/api/v1\", create_v1_routes())\n\n        // 集群管理路由\n        .nest(\"/_cluster\", create_cluster_routes())\n\n        // 设置应用状态\n        .with_state(app_state)\n\n        // 添加全局中间件\n        .layer(\n            ServiceBuilder::new()\n                .layer(TraceLayer::new_for_http())\n                .layer(CorsLayer::permissive())\n                // 添加请求日志中间件\n                .layer(from_fn(logging_middleware))\n        )\n}\n\n/// 创建 API v1 路由\nfn create_v1_routes() -\u003e Router\u003cAppState\u003e {\n    Router::new()\n        // 配置管理路由\n        .route(\"/configs/{tenant}/{app}/{env}/{name}/versions\", post(create_version_handler))\n        .route(\"/configs/{tenant}/{app}/{env}/{name}/releases\", put(update_releases_handler))\n        .route(\"/fetch/configs/{tenant}/{app}/{env}/{name}\", get(fetch_config_handler))\n\n        // 配置查询路由\n        .route(\"/configs/{tenant}/{app}/{env}/{name}\", get(get_config_handler))\n        .route(\"/configs/{tenant}/{app}/{env}/{name}/versions\", get(list_versions_handler))\n}\n\n/// 创建集群管理路由\nfn create_cluster_routes() -\u003e Router\u003cAppState\u003e {\n    Router::new()\n        .route(\"/status\", get(cluster_status_handler))\n        .route(\"/nodes\", post(add_node_handler))\n        .route(\"/nodes/{node_id}\", axum::routing::delete(remove_node_handler))\n}\n\n/// 健康检查处理器\nasync fn health_handler() -\u003e Json\u003cValue\u003e {\n    Json(json!({\n        \"status\": \"healthy\",\n        \"timestamp\": chrono::Utc::now().to_rfc3339()\n    }))\n}\n\n/// 就绪检查处理器\nasync fn readiness_handler(State(app_state): State\u003cAppState\u003e) -\u003e Result\u003cJson\u003cValue\u003e, StatusCode\u003e {\n    // 检查核心服务是否就绪\n    let cluster_status = app_state.core_handle.raft_client()\n        .get_cluster_status()\n        .await;\n\n    match cluster_status {\n        Ok(_) =\u003e Ok(Json(json!({\n            \"status\": \"ready\",\n            \"timestamp\": chrono::Utc::now().to_rfc3339()\n        }))),\n        Err(e) =\u003e {\n            warn!(\"Readiness check failed: {}\", e);\n            Err(StatusCode::SERVICE_UNAVAILABLE)\n        }\n    }\n}\n\n// TODO: 更新测试以包含AuthzService\n// #[cfg(test)]\n// mod tests {\n//     use super::*;\n//     use crate::raft::{RaftClient, Store};\n//     use std::sync::Arc;\n//     use tempfile::TempDir;\n\n//     #[tokio::test]\n//     async fn test_http_protocol_creation() {\n//         let protocol = HttpProtocol;\n//         assert_eq!(protocol.name(), \"http-rest\");\n//         assert!(protocol.health_check().await);\n//     }\n\n//     #[tokio::test]\n//     async fn test_app_state_creation() {\n//         let temp_dir = TempDir::new().unwrap();\n//         let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n//         let raft_client = Arc::new(RaftClient::new(store.clone()));\n//         let core_handle = CoreAppHandle::new(raft_client, store);\n\n//         let app_state = AppState::new(core_handle);\n\n//         // 验证状态创建成功\n//         assert!(Arc::ptr_eq(\u0026app_state.core_handle.raft_client, \u0026app_state.core_handle.raft_client));\n//     }\n\n//     #[tokio::test]\n//     async fn test_router_creation() {\n//         let temp_dir = TempDir::new().unwrap();\n//         let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n//         let raft_client = Arc::new(RaftClient::new(store.clone()));\n//         let core_handle = CoreAppHandle::new(raft_client, store);\n//         let app_state = AppState::new(core_handle);\n\n//         let _router = create_router(app_state);\n//         // 如果能创建路由器而不出错，测试就通过了\n//     }\n// }\n","traces":[{"line":31,"address":[],"length":0,"stats":{"Line":0}},{"line":32,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":52,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":59,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":63,"address":[],"length":0,"stats":{"Line":0}},{"line":64,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":82,"address":[],"length":0,"stats":{"Line":0}},{"line":84,"address":[],"length":0,"stats":{"Line":0}},{"line":85,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":91,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":99,"address":[],"length":0,"stats":{"Line":0}},{"line":100,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":107,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":0}},{"line":110,"address":[],"length":0,"stats":{"Line":0}},{"line":111,"address":[],"length":0,"stats":{"Line":0}},{"line":112,"address":[],"length":0,"stats":{"Line":0}},{"line":115,"address":[],"length":0,"stats":{"Line":0}},{"line":116,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":121,"address":[],"length":0,"stats":{"Line":0}},{"line":122,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":0}},{"line":124,"address":[],"length":0,"stats":{"Line":0}},{"line":128,"address":[],"length":0,"stats":{"Line":0}},{"line":129,"address":[],"length":0,"stats":{"Line":0}},{"line":130,"address":[],"length":0,"stats":{"Line":0}},{"line":131,"address":[],"length":0,"stats":{"Line":0}},{"line":136,"address":[],"length":0,"stats":{"Line":0}},{"line":138,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":144,"address":[],"length":0,"stats":{"Line":0}},{"line":145,"address":[],"length":0,"stats":{"Line":0}},{"line":147,"address":[],"length":0,"stats":{"Line":0}},{"line":148,"address":[],"length":0,"stats":{"Line":0}},{"line":149,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":55},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","protocol","http","schemas.rs"],"content":"use crate::raft::types::{ConfigFormat, ConfigNamespace, Release};\nuse serde::{Deserialize, Serialize};\n\n/// 创建配置版本请求\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct CreateVersionRequest {\n    /// 配置内容\n    pub content: String,\n    /// 配置格式（可选，如果不提供则继承配置的默认格式）\n    pub format: Option\u003cConfigFormat\u003e,\n    /// 创建者ID（可选，默认为 \"system\"）\n    pub creator_id: Option\u003cString\u003e,\n    /// 版本描述（可选）\n    pub description: Option\u003cString\u003e,\n}\n\n/// 更新发布规则请求\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct UpdateReleasesRequest {\n    /// 新的发布规则列表\n    pub releases: Vec\u003cRelease\u003e,\n    /// 更新者ID（可选，默认为 \"system\"）\n    pub updater_id: Option\u003cString\u003e,\n}\n\n/// 获取配置响应\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct FetchConfigResponse {\n    /// 配置命名空间\n    pub namespace: ConfigNamespace,\n    /// 配置名称\n    pub name: String,\n    /// 配置内容\n    pub content: String,\n    /// 配置格式\n    pub format: ConfigFormat,\n    /// 版本ID\n    pub version_id: u64,\n    /// 内容哈希\n    pub hash: String,\n    /// 创建时间\n    pub created_at: chrono::DateTime\u003cchrono::Utc\u003e,\n}\n\n/// 通用API响应\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ApiResponse\u003cT\u003e {\n    /// 操作是否成功\n    pub success: bool,\n    /// 响应数据\n    pub data: Option\u003cT\u003e,\n    /// 响应消息\n    pub message: Option\u003cString\u003e,\n    /// 错误信息（当 success 为 false 时）\n    pub error: Option\u003cString\u003e,\n}\n\nimpl\u003cT\u003e ApiResponse\u003cT\u003e {\n    /// 创建成功响应\n    pub fn success(data: T) -\u003e Self {\n        Self {\n            success: true,\n            data: Some(data),\n            message: None,\n            error: None,\n        }\n    }\n\n    /// 创建成功响应（带消息）\n    pub fn success_with_message(data: T, message: String) -\u003e Self {\n        Self {\n            success: true,\n            data: Some(data),\n            message: Some(message),\n            error: None,\n        }\n    }\n\n    /// 创建错误响应\n    pub fn error(error: String) -\u003e Self {\n        Self {\n            success: false,\n            data: None,\n            message: None,\n            error: Some(error),\n        }\n    }\n}\n\n/// 分页查询参数\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct PaginationParams {\n    /// 页码（从1开始）\n    pub page: Option\u003cu32\u003e,\n    /// 每页大小\n    pub page_size: Option\u003cu32\u003e,\n}\n\nimpl Default for PaginationParams {\n    fn default() -\u003e Self {\n        Self {\n            page: Some(1),\n            page_size: Some(20),\n        }\n    }\n}\n\n/// 分页响应\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct PaginatedResponse\u003cT\u003e {\n    /// 数据列表\n    pub items: Vec\u003cT\u003e,\n    /// 当前页码\n    pub page: u32,\n    /// 每页大小\n    pub page_size: u32,\n    /// 总数量\n    pub total: u64,\n    /// 总页数\n    pub total_pages: u32,\n}\n\nimpl\u003cT\u003e PaginatedResponse\u003cT\u003e {\n    /// 创建分页响应\n    pub fn new(items: Vec\u003cT\u003e, page: u32, page_size: u32, total: u64) -\u003e Self {\n        let total_pages = ((total as f64) / (page_size as f64)).ceil() as u32;\n        Self {\n            items,\n            page,\n            page_size,\n            total,\n            total_pages,\n        }\n    }\n}\n\n/// 配置查询参数\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ConfigQueryParams {\n    /// 配置名称前缀过滤\n    pub prefix: Option\u003cString\u003e,\n    /// 分页参数\n    #[serde(flatten)]\n    pub pagination: PaginationParams,\n}\n\n/// 版本查询参数\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct VersionQueryParams {\n    /// 版本ID过滤\n    pub version_id: Option\u003cu64\u003e,\n    /// 创建者过滤\n    pub creator_id: Option\u003cString\u003e,\n    /// 分页参数\n    #[serde(flatten)]\n    pub pagination: PaginationParams,\n}\n\n/// 健康检查响应\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct HealthResponse {\n    /// 服务状态\n    pub status: String,\n    /// 时间戳\n    pub timestamp: String,\n    /// 版本信息\n    pub version: Option\u003cString\u003e,\n    /// 额外信息\n    pub details: Option\u003cserde_json::Value\u003e,\n}\n\n/// 集群节点信息\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct NodeInfo {\n    /// 节点ID\n    pub id: u64,\n    /// 节点地址\n    pub address: String,\n    /// 节点状态\n    pub status: String,\n    /// 是否为领导者\n    pub is_leader: bool,\n    /// 最后心跳时间\n    pub last_heartbeat: Option\u003cchrono::DateTime\u003cchrono::Utc\u003e\u003e,\n}\n\n/// 添加节点请求\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct AddNodeRequest {\n    /// 节点ID\n    pub node_id: u64,\n    /// 节点地址\n    pub address: String,\n}\n\n/// 移除节点请求\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct RemoveNodeRequest {\n    /// 节点ID\n    pub node_id: u64,\n    /// 是否强制移除\n    pub force: Option\u003cbool\u003e,\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use std::collections::BTreeMap;\n\n    #[test]\n    fn test_create_version_request_serialization() {\n        let request = CreateVersionRequest {\n            content: \"test content\".to_string(),\n            format: Some(ConfigFormat::Json),\n            creator_id: Some(\"user123\".to_string()),\n            description: Some(\"Test version\".to_string()),\n        };\n\n        let json = serde_json::to_string(\u0026request).unwrap();\n        let deserialized: CreateVersionRequest = serde_json::from_str(\u0026json).unwrap();\n\n        assert_eq!(request.content, deserialized.content);\n        assert_eq!(request.format, deserialized.format);\n        assert_eq!(request.creator_id, deserialized.creator_id);\n        assert_eq!(request.description, deserialized.description);\n    }\n\n    #[test]\n    fn test_api_response_creation() {\n        let success_response = ApiResponse::success(\"test data\".to_string());\n        assert!(success_response.success);\n        assert_eq!(success_response.data, Some(\"test data\".to_string()));\n\n        let error_response = ApiResponse::\u003cString\u003e::error(\"test error\".to_string());\n        assert!(!error_response.success);\n        assert_eq!(error_response.error, Some(\"test error\".to_string()));\n    }\n\n    #[test]\n    fn test_paginated_response() {\n        let items = vec![\"item1\".to_string(), \"item2\".to_string()];\n        let response = PaginatedResponse::new(items.clone(), 1, 10, 25);\n\n        assert_eq!(response.items, items);\n        assert_eq!(response.page, 1);\n        assert_eq!(response.page_size, 10);\n        assert_eq!(response.total, 25);\n        assert_eq!(response.total_pages, 3);\n    }\n\n    #[test]\n    fn test_update_releases_request() {\n        let releases = vec![Release::new(BTreeMap::new(), 1, 0)];\n        let request = UpdateReleasesRequest {\n            releases: releases.clone(),\n            updater_id: Some(\"admin\".to_string()),\n        };\n\n        let json = serde_json::to_string(\u0026request).unwrap();\n        let deserialized: UpdateReleasesRequest = serde_json::from_str(\u0026json).unwrap();\n\n        assert_eq!(request.releases.len(), deserialized.releases.len());\n        assert_eq!(request.updater_id, deserialized.updater_id);\n    }\n}\n","traces":[{"line":60,"address":[],"length":0,"stats":{"Line":1}},{"line":63,"address":[],"length":0,"stats":{"Line":1}},{"line":70,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":80,"address":[],"length":0,"stats":{"Line":1}},{"line":85,"address":[],"length":0,"stats":{"Line":1}},{"line":100,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":103,"address":[],"length":0,"stats":{"Line":0}},{"line":125,"address":[],"length":0,"stats":{"Line":1}},{"line":126,"address":[],"length":0,"stats":{"Line":1}}],"covered":6,"coverable":12},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","protocol","mod.rs"],"content":"use crate::app::CoreAppHandle;\nuse async_trait::async_trait;\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\n\npub mod http;\n\n/// 协议插件的配置\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ProtocolConfig {\n    /// 监听地址\n    pub listen_addr: String,\n    /// 协议特定的配置项\n    pub options: HashMap\u003cString, String\u003e,\n}\n\nimpl Default for ProtocolConfig {\n    fn default() -\u003e Self {\n        Self {\n            listen_addr: \"127.0.0.1:8080\".to_string(),\n            options: HashMap::new(),\n        }\n    }\n}\n\n/// 协议插件 trait\n/// \n/// 所有协议插件（HTTP、gRPC 等）都必须实现这个 trait\n/// 它定义了插件的生命周期和与系统核心的交互方式\n#[async_trait]\npub trait ProtocolPlugin: Send + Sync {\n    /// 返回协议的唯一名称\n    /// \n    /// 例如: \"http-rest\", \"grpc\", \"websocket\"\n    fn name(\u0026self) -\u003e \u0026'static str;\n    \n    /// 启动协议服务\n    /// \n    /// 这是一个长时运行的异步任务，它接收一个到应用核心的句柄，\n    /// 用于执行业务操作\n    /// \n    /// # Arguments\n    /// * `core_handle` - 包含了 RaftClient, Store 等核心服务的句柄\n    /// * `config` - 此协议实例的配置\n    async fn start(\u0026self, core_handle: CoreAppHandle, config: ProtocolConfig) -\u003e anyhow::Result\u003c()\u003e;\n    \n    /// 获取协议的健康状态\n    /// \n    /// 返回协议是否正常运行\n    async fn health_check(\u0026self) -\u003e bool {\n        // 默认实现总是返回健康状态\n        true\n    }\n    \n    /// 优雅关闭协议服务\n    /// \n    /// 在应用关闭时调用，允许协议插件进行清理工作\n    async fn shutdown(\u0026self) -\u003e anyhow::Result\u003c()\u003e {\n        // 默认实现不做任何事情\n        Ok(())\n    }\n}\n\n/// 协议插件管理器\n/// \n/// 负责管理和启动所有已注册的协议插件\npub struct ProtocolManager {\n    plugins: Vec\u003cBox\u003cdyn ProtocolPlugin\u003e\u003e,\n    configs: HashMap\u003cString, ProtocolConfig\u003e,\n}\n\nimpl ProtocolManager {\n    /// 创建新的协议管理器\n    pub fn new() -\u003e Self {\n        Self {\n            plugins: Vec::new(),\n            configs: HashMap::new(),\n        }\n    }\n    \n    /// 注册协议插件\n    pub fn register_plugin(\u0026mut self, plugin: Box\u003cdyn ProtocolPlugin\u003e) {\n        self.plugins.push(plugin);\n    }\n    \n    /// 设置协议配置\n    pub fn set_config(\u0026mut self, protocol_name: String, config: ProtocolConfig) {\n        self.configs.insert(protocol_name, config);\n    }\n    \n    /// 启动所有已注册的协议插件\n    pub async fn start_all(\u0026self, core_handle: CoreAppHandle) -\u003e anyhow::Result\u003cVec\u003ctokio::task::JoinHandle\u003c()\u003e\u003e\u003e {\n        let mut handles = Vec::new();\n        \n        for plugin in \u0026self.plugins {\n            let plugin_name = plugin.name();\n            let config = self.configs.get(plugin_name)\n                .cloned()\n                .unwrap_or_default();\n            \n            let core_handle_clone = core_handle.clone();\n            let plugin_name_owned = plugin_name.to_string();\n            \n            // 为每个插件创建一个独立的任务\n            let handle = tokio::spawn(async move {\n                // 注意：这里我们无法直接使用 plugin，因为它不是 Clone 的\n                // 在实际实现中，我们需要重新设计这个部分\n                tracing::info!(\"Starting protocol plugin: {}\", plugin_name_owned);\n                \n                // TODO: 实际启动插件的逻辑需要在具体的插件实现中处理\n                // 这里只是一个占位符\n                loop {\n                    tokio::time::sleep(std::time::Duration::from_secs(1)).await;\n                }\n            });\n            \n            handles.push(handle);\n        }\n        \n        Ok(handles)\n    }\n    \n    /// 获取已注册的插件数量\n    pub fn plugin_count(\u0026self) -\u003e usize {\n        self.plugins.len()\n    }\n    \n    /// 获取所有插件的名称\n    pub fn plugin_names(\u0026self) -\u003e Vec\u003c\u0026str\u003e {\n        self.plugins.iter().map(|p| p.name()).collect()\n    }\n}\n\nimpl Default for ProtocolManager {\n    fn default() -\u003e Self {\n        Self::new()\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use crate::raft::{RaftClient, Store};\n    use std::sync::Arc;\n    use tempfile::TempDir;\n\n    // 测试用的协议插件实现\n    struct TestProtocol {\n        name: \u0026'static str,\n    }\n\n    #[async_trait]\n    impl ProtocolPlugin for TestProtocol {\n        fn name(\u0026self) -\u003e \u0026'static str {\n            self.name\n        }\n\n        async fn start(\u0026self, _core_handle: CoreAppHandle, _config: ProtocolConfig) -\u003e anyhow::Result\u003c()\u003e {\n            // 测试实现，不做任何事情\n            Ok(())\n        }\n    }\n\n    #[tokio::test]\n    async fn test_protocol_config_default() {\n        let config = ProtocolConfig::default();\n        assert_eq!(config.listen_addr, \"127.0.0.1:8080\");\n        assert!(config.options.is_empty());\n    }\n\n    #[tokio::test]\n    async fn test_protocol_manager() {\n        let mut manager = ProtocolManager::new();\n        \n        // 注册测试插件\n        let plugin = Box::new(TestProtocol { name: \"test-http\" });\n        manager.register_plugin(plugin);\n        \n        // 验证插件注册\n        assert_eq!(manager.plugin_count(), 1);\n        assert_eq!(manager.plugin_names(), vec![\"test-http\"]);\n        \n        // 设置配置\n        let config = ProtocolConfig {\n            listen_addr: \"0.0.0.0:9090\".to_string(),\n            options: HashMap::new(),\n        };\n        manager.set_config(\"test-http\".to_string(), config);\n    }\n\n    // TODO: 修复这个测试以包含AuthzService\n    // #[tokio::test]\n    // async fn test_core_app_handle_integration() {\n    //     let temp_dir = TempDir::new().unwrap();\n    //     let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n    //     let raft_client = Arc::new(RaftClient::new(store.clone()));\n    //     // 需要AuthzService参数\n    //     // let core_handle = CoreAppHandle::new(raft_client, store, authz_service);\n    //\n    //     let plugin = TestProtocol { name: \"test\" };\n    //     let config = ProtocolConfig::default();\n    //\n    //     // 测试插件启动\n    //     // let result = plugin.start(core_handle, config).await;\n    //     // assert!(result.is_ok());\n    // }\n}\n","traces":[{"line":18,"address":[],"length":0,"stats":{"Line":1}},{"line":20,"address":[],"length":0,"stats":{"Line":1}},{"line":21,"address":[],"length":0,"stats":{"Line":1}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":52,"address":[],"length":0,"stats":{"Line":0}},{"line":58,"address":[],"length":0,"stats":{"Line":0}},{"line":60,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":1}},{"line":76,"address":[],"length":0,"stats":{"Line":1}},{"line":77,"address":[],"length":0,"stats":{"Line":1}},{"line":82,"address":[],"length":0,"stats":{"Line":1}},{"line":83,"address":[],"length":0,"stats":{"Line":1}},{"line":87,"address":[],"length":0,"stats":{"Line":1}},{"line":88,"address":[],"length":0,"stats":{"Line":1}},{"line":92,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":95,"address":[],"length":0,"stats":{"Line":0}},{"line":96,"address":[],"length":0,"stats":{"Line":0}},{"line":97,"address":[],"length":0,"stats":{"Line":0}},{"line":101,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":117,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":124,"address":[],"length":0,"stats":{"Line":1}},{"line":125,"address":[],"length":0,"stats":{"Line":1}},{"line":129,"address":[],"length":0,"stats":{"Line":1}},{"line":130,"address":[],"length":0,"stats":{"Line":3}},{"line":135,"address":[],"length":0,"stats":{"Line":0}},{"line":136,"address":[],"length":0,"stats":{"Line":0}}],"covered":14,"coverable":32},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","client","helpers.rs"],"content":"use crate::raft::types::*;\nuse super::types::*;\nuse std::collections::BTreeMap;\n\n/// Helper function to create a write request\npub fn create_write_request(command: RaftCommand) -\u003e ClientWriteRequest {\n    ClientWriteRequest {\n        command,\n        request_id: None,\n    }\n}\n\n/// Helper function to create a read request\npub fn create_read_request(operation: ReadOperation) -\u003e ClientReadRequest {\n    ClientReadRequest {\n        operation,\n        consistency: Some(ReadConsistency::default()),\n    }\n}\n\n/// Helper function to create a get config request\npub fn create_get_config_request(\n    namespace: ConfigNamespace,\n    name: String,\n    client_labels: BTreeMap\u003cString, String\u003e,\n) -\u003e ClientReadRequest {\n    create_read_request(ReadOperation::GetConfig {\n        namespace,\n        name,\n        client_labels,\n    })\n}\n\n/// Helper function to create a list configs request\npub fn create_list_configs_request(\n    namespace: ConfigNamespace,\n    prefix: Option\u003cString\u003e,\n) -\u003e ClientReadRequest {\n    create_read_request(ReadOperation::ListConfigs { namespace, prefix })\n}\n","traces":[{"line":6,"address":[],"length":0,"stats":{"Line":2}},{"line":14,"address":[],"length":0,"stats":{"Line":2}},{"line":17,"address":[],"length":0,"stats":{"Line":2}},{"line":22,"address":[],"length":0,"stats":{"Line":2}},{"line":27,"address":[],"length":0,"stats":{"Line":2}},{"line":28,"address":[],"length":0,"stats":{"Line":2}},{"line":29,"address":[],"length":0,"stats":{"Line":2}},{"line":30,"address":[],"length":0,"stats":{"Line":2}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}}],"covered":8,"coverable":10},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","client","mod.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse std::sync::Arc;\nuse tokio::sync::RwLock;\nuse tracing::{debug, info, warn};\n\n// 重新导出模块内容\npub mod types;\npub mod helpers;\n#[cfg(test)]\nmod tests;\n\npub use types::*;\npub use helpers::*;\n\n/// Client interface for interacting with the Raft cluster\n#[derive(Clone)]\npub struct RaftClient {\n    /// Local store for direct access (fallback when Raft is not available)\n    store: Arc\u003ccrate::raft::store::Store\u003e,\n    /// Raft node for consensus operations\n    raft_node: Option\u003cArc\u003cRwLock\u003ccrate::raft::node::RaftNode\u003e\u003e\u003e,\n    /// Current leader node (for routing requests)\n    current_leader: Arc\u003cRwLock\u003cOption\u003cNodeId\u003e\u003e\u003e,\n}\n\nimpl RaftClient {\n    /// Create a new Raft client with store only (fallback mode)\n    pub fn new(store: Arc\u003ccrate::raft::store::Store\u003e) -\u003e Self {\n        Self {\n            store,\n            raft_node: None,\n            current_leader: Arc::new(RwLock::new(Some(1))), // Default to node 1 as leader\n        }\n    }\n\n    /// Create a new Raft client with Raft node (consensus mode)\n    pub fn new_with_raft_node(\n        store: Arc\u003ccrate::raft::store::Store\u003e,\n        raft_node: Arc\u003cRwLock\u003ccrate::raft::node::RaftNode\u003e\u003e,\n    ) -\u003e Self {\n        Self {\n            store,\n            raft_node: Some(raft_node),\n            current_leader: Arc::new(RwLock::new(Some(1))), // Default to node 1 as leader\n        }\n    }\n\n    /// Submit a write request to the cluster\n    pub async fn write(\u0026self, request: ClientWriteRequest) -\u003e Result\u003cClientWriteResponse\u003e {\n        info!(\"Processing client write request: {:?}\", request.command);\n\n        // Try to use Raft consensus if available\n        if let Some(ref raft_node) = self.raft_node {\n            debug!(\"Routing write request through Raft consensus\");\n            let node = raft_node.read().await;\n\n            // Convert ClientWriteRequest to ClientRequest\n            let client_request = ClientRequest {\n                command: request.command.clone(),\n            };\n\n            match node.client_write(client_request).await {\n                Ok(response) =\u003e {\n                    debug!(\"Raft write completed successfully\");\n                    return Ok(response);\n                }\n                Err(e) =\u003e {\n                    warn!(\"Raft write failed, falling back to direct store access: {}\", e);\n                }\n            }\n        }\n\n        // Fallback to direct store access\n        warn!(\"Using direct store access (bypassing consensus)\");\n        let response = self.store.apply_command(\u0026request.command).await?;\n\n        debug!(\"Direct store write completed\");\n        Ok(response)\n    }\n\n    /// Submit a write request with automatic leader detection\n    pub async fn write_with_leader_detection(\u0026self, request: ClientWriteRequest) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Check if we know the current leader\n        let leader_id = self.current_leader.read().await;\n        \n        if leader_id.is_none() {\n            return Err(crate::error::ConfluxError::raft(\"No leader available\"));\n        }\n\n        // For now, just use the local store (same as write method)\n        // TODO: Implement actual leader detection and request forwarding\n        self.write(request).await\n    }\n\n    /// Batch write multiple requests\n    pub async fn batch_write(\u0026self, requests: Vec\u003cClientWriteRequest\u003e) -\u003e Result\u003cVec\u003cClientWriteResponse\u003e\u003e {\n        info!(\"Processing batch write with {} requests\", requests.len());\n        \n        let mut responses = Vec::with_capacity(requests.len());\n        \n        for request in requests {\n            match self.write(request).await {\n                Ok(response) =\u003e responses.push(response),\n                Err(e) =\u003e {\n                    // For batch operations, we could either fail fast or continue\n                    // For now, we fail fast\n                    return Err(e);\n                }\n            }\n        }\n        \n        debug!(\"Batch write completed successfully\");\n        Ok(responses)\n    }\n\n    /// Submit a read request to the cluster\n    pub async fn read(\u0026self, request: ClientReadRequest) -\u003e Result\u003cClientReadResponse\u003e {\n        debug!(\"Processing client read request: {:?}\", request.operation);\n\n        let data = match request.operation {\n            ReadOperation::GetConfig {\n                namespace,\n                name,\n                client_labels,\n            } =\u003e {\n                let result = self\n                    .store\n                    .get_published_config(\u0026namespace, \u0026name, \u0026client_labels)\n                    .await;\n                result.map(|(config, version)| {\n                    serde_json::json!({\n                        \"config\": config,\n                        \"version\": version\n                    })\n                })\n            }\n            ReadOperation::GetConfigVersion {\n                config_id,\n                version_id,\n            } =\u003e {\n                let result = self.store.get_config_version(config_id, version_id).await;\n                result.map(|version| serde_json::json!(version))\n            }\n            ReadOperation::ListConfigs { namespace, prefix } =\u003e {\n                // For MVP, return empty list\n                // In a real implementation, this would query the store\n                let _ = (namespace, prefix);\n                Some(serde_json::json!([]))\n            }\n        };\n\n        let response = ClientReadResponse {\n            success: true,\n            data,\n            leader_id: *self.current_leader.read().await,\n            consistency_level: request.consistency.unwrap_or_default(),\n        };\n\n        debug!(\"Client read completed successfully\");\n        Ok(response)\n    }\n\n    /// Get current cluster status\n    pub async fn get_cluster_status(\u0026self) -\u003e Result\u003cClusterStatus\u003e {\n        debug!(\"Getting cluster status\");\n\n        let status = ClusterStatus {\n            leader_id: *self.current_leader.read().await,\n            members: vec![1], // For MVP, single node cluster\n            term: 1,\n            last_log_index: 0,\n            commit_index: 0,\n            applied_index: 0,\n        };\n\n        Ok(status)\n    }\n\n    /// Set the current leader (for testing and manual control)\n    pub async fn set_leader(\u0026self, leader_id: Option\u003cNodeId\u003e) {\n        let mut current_leader = self.current_leader.write().await;\n        *current_leader = leader_id;\n        info!(\"Leader set to: {:?}\", leader_id);\n    }\n\n    /// Check if the client is connected to the leader\n    pub async fn is_connected_to_leader(\u0026self) -\u003e bool {\n        self.current_leader.read().await.is_some()\n    }\n\n    /// Wait for the cluster to have a leader\n    pub async fn wait_for_leader(\u0026self, timeout: std::time::Duration) -\u003e Result\u003cNodeId\u003e {\n        let start = std::time::Instant::now();\n\n        loop {\n            if let Some(leader_id) = *self.current_leader.read().await {\n                return Ok(leader_id);\n            }\n\n            if start.elapsed() \u003e timeout {\n                return Err(crate::error::ConfluxError::raft(\n                    \"Timeout waiting for leader\",\n                ));\n            }\n\n            tokio::time::sleep(std::time::Duration::from_millis(100)).await;\n        }\n    }\n}\n","traces":[{"line":29,"address":[],"length":0,"stats":{"Line":6}},{"line":33,"address":[],"length":0,"stats":{"Line":6}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":4}},{"line":51,"address":[],"length":0,"stats":{"Line":2}},{"line":54,"address":[],"length":0,"stats":{"Line":2}},{"line":55,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":60,"address":[],"length":0,"stats":{"Line":0}},{"line":63,"address":[],"length":0,"stats":{"Line":0}},{"line":64,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":68,"address":[],"length":0,"stats":{"Line":0}},{"line":69,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":2}},{"line":76,"address":[],"length":0,"stats":{"Line":2}},{"line":78,"address":[],"length":0,"stats":{"Line":0}},{"line":83,"address":[],"length":0,"stats":{"Line":0}},{"line":85,"address":[],"length":0,"stats":{"Line":0}},{"line":87,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":97,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":100,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":103,"address":[],"length":0,"stats":{"Line":0}},{"line":104,"address":[],"length":0,"stats":{"Line":0}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":4}},{"line":119,"address":[],"length":0,"stats":{"Line":2}},{"line":121,"address":[],"length":0,"stats":{"Line":4}},{"line":123,"address":[],"length":0,"stats":{"Line":2}},{"line":124,"address":[],"length":0,"stats":{"Line":2}},{"line":125,"address":[],"length":0,"stats":{"Line":2}},{"line":127,"address":[],"length":0,"stats":{"Line":2}},{"line":131,"address":[],"length":0,"stats":{"Line":2}},{"line":132,"address":[],"length":0,"stats":{"Line":0}},{"line":133,"address":[],"length":0,"stats":{"Line":0}},{"line":134,"address":[],"length":0,"stats":{"Line":0}},{"line":139,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":145,"address":[],"length":0,"stats":{"Line":0}},{"line":148,"address":[],"length":0,"stats":{"Line":0}},{"line":149,"address":[],"length":0,"stats":{"Line":0}},{"line":157,"address":[],"length":0,"stats":{"Line":2}},{"line":160,"address":[],"length":0,"stats":{"Line":2}},{"line":161,"address":[],"length":0,"stats":{"Line":2}},{"line":165,"address":[],"length":0,"stats":{"Line":4}},{"line":166,"address":[],"length":0,"stats":{"Line":2}},{"line":169,"address":[],"length":0,"stats":{"Line":2}},{"line":181,"address":[],"length":0,"stats":{"Line":0}},{"line":182,"address":[],"length":0,"stats":{"Line":0}},{"line":183,"address":[],"length":0,"stats":{"Line":0}},{"line":184,"address":[],"length":0,"stats":{"Line":0}},{"line":188,"address":[],"length":0,"stats":{"Line":0}},{"line":189,"address":[],"length":0,"stats":{"Line":0}},{"line":193,"address":[],"length":0,"stats":{"Line":0}},{"line":194,"address":[],"length":0,"stats":{"Line":0}},{"line":197,"address":[],"length":0,"stats":{"Line":0}},{"line":198,"address":[],"length":0,"stats":{"Line":0}},{"line":201,"address":[],"length":0,"stats":{"Line":0}},{"line":202,"address":[],"length":0,"stats":{"Line":0}},{"line":203,"address":[],"length":0,"stats":{"Line":0}},{"line":207,"address":[],"length":0,"stats":{"Line":0}}],"covered":21,"coverable":72},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","client","tests.rs"],"content":"#[cfg(test)]\nmod tests {\n    use super::super::*;\n    use crate::raft::store::Store;\n    use std::collections::BTreeMap;\n    use std::sync::Arc;\n\n    #[tokio::test]\n    async fn test_client_write() {\n        use tempfile::tempdir;\n        let temp_dir = tempdir().unwrap();\n        let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n        let client = RaftClient::new(store);\n\n        let command = RaftCommand::CreateConfig {\n            namespace: ConfigNamespace {\n                tenant: \"test\".to_string(),\n                app: \"app\".to_string(),\n                env: \"dev\".to_string(),\n            },\n            name: \"test-config\".to_string(),\n            content: \"test content\".as_bytes().to_vec(),\n            format: ConfigFormat::Json,\n            schema: None,\n            creator_id: 1,\n            description: \"Test configuration\".to_string(),\n        };\n\n        let request = create_write_request(command);\n        let response = client.write(request).await.unwrap();\n\n        assert!(response.success);\n    }\n\n    #[tokio::test]\n    async fn test_client_read() {\n        use tempfile::tempdir;\n        let temp_dir = tempdir().unwrap();\n        let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n        let client = RaftClient::new(store);\n\n        let request = create_get_config_request(\n            ConfigNamespace {\n                tenant: \"test\".to_string(),\n                app: \"app\".to_string(),\n                env: \"dev\".to_string(),\n            },\n            \"test-config\".to_string(),\n            BTreeMap::new(),\n        );\n\n        let response = client.read(request).await.unwrap();\n        assert!(response.success);\n    }\n\n    #[tokio::test]\n    async fn test_cluster_status() {\n        use tempfile::tempdir;\n        let temp_dir = tempdir().unwrap();\n        let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());\n        let client = RaftClient::new(store);\n\n        let status = client.get_cluster_status().await.unwrap();\n        assert_eq!(status.members, vec![1]);\n    }\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","client","types.rs"],"content":"use crate::raft::types::*;\nuse serde::{Deserialize, Serialize};\nuse std::collections::BTreeMap;\n\n/// Client write request wrapper\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ClientWriteRequest {\n    /// The command to execute\n    pub command: RaftCommand,\n    /// Optional request ID for idempotency\n    pub request_id: Option\u003cString\u003e,\n}\n\n/// Client read request wrapper\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ClientReadRequest {\n    /// The type of read operation\n    pub operation: ReadOperation,\n    /// Optional consistency level\n    pub consistency: Option\u003cReadConsistency\u003e,\n}\n\n/// Read operation types\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum ReadOperation {\n    /// Get configuration by namespace and name\n    GetConfig {\n        namespace: ConfigNamespace,\n        name: String,\n        /// Client labels for release targeting\n        client_labels: BTreeMap\u003cString, String\u003e,\n    },\n    /// Get configuration version\n    GetConfigVersion { config_id: u64, version_id: u64 },\n    /// List configurations in a namespace\n    ListConfigs {\n        namespace: ConfigNamespace,\n        /// Optional prefix filter\n        prefix: Option\u003cString\u003e,\n    },\n}\n\n/// Read consistency levels\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum ReadConsistency {\n    /// Read from any node (eventual consistency)\n    Eventual,\n    /// Read from leader only (strong consistency)\n    Strong,\n    /// Read with linearizable semantics\n    Linearizable,\n}\n\nimpl Default for ReadConsistency {\n    fn default() -\u003e Self {\n        Self::Eventual\n    }\n}\n\n/// Client read response\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ClientReadResponse {\n    /// Whether the operation was successful\n    pub success: bool,\n    /// Response data (if any)\n    pub data: Option\u003cserde_json::Value\u003e,\n    /// Current leader ID\n    pub leader_id: Option\u003cNodeId\u003e,\n    /// Consistency level used for this read\n    pub consistency_level: ReadConsistency,\n}\n\n/// Cluster status information\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ClusterStatus {\n    /// Current leader ID\n    pub leader_id: Option\u003cNodeId\u003e,\n    /// List of cluster members\n    pub members: Vec\u003cNodeId\u003e,\n    /// Current term\n    pub term: u64,\n    /// Last log index\n    pub last_log_index: u64,\n    /// Commit index\n    pub commit_index: u64,\n    /// Applied index\n    pub applied_index: u64,\n}\n","traces":[{"line":55,"address":[],"length":0,"stats":{"Line":2}},{"line":56,"address":[],"length":0,"stats":{"Line":2}}],"covered":2,"coverable":2},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","mod.rs"],"content":"pub mod client;\npub mod network;\npub mod node;\npub mod store;\npub mod types;\n\n\npub use client::{RaftClient, ClientWriteRequest, ClientReadRequest, ClientReadResponse, ClusterStatus};\npub use network::{ConfluxNetwork, ConfluxNetworkFactory, NetworkConfig};\npub use node::{create_node_config, NodeConfig, RaftNode};\npub use store::Store;\npub use types::*;\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","network.rs"],"content":"use crate::raft::types::*;\nuse openraft::{\n    error::{\n        Fatal, InstallSnapshotError, NetworkError, RPCError, RaftError, ReplicationClosed,\n        StreamingError,\n    },\n    network::{RPCOption, RaftNetwork, RaftNetworkFactory},\n    raft::{\n        AppendEntriesRequest, AppendEntriesResponse, InstallSnapshotRequest,\n        InstallSnapshotResponse, SnapshotResponse, VoteRequest, VoteResponse,\n    },\n    storage::Snapshot,\n    BasicNode, RPCTypes, Vote,\n};\nuse reqwest::Client;\n\nuse std::collections::HashMap;\nuse std::sync::Arc;\nuse std::time::Duration;\nuse tokio::sync::RwLock;\nuse tracing::{debug, error};\n\n/// Network configuration for Raft communication\n#[derive(Debug, Clone)]\npub struct NetworkConfig {\n    /// HTTP client timeout in seconds\n    pub timeout_secs: u64,\n    /// Node ID to address mapping\n    pub node_addresses: Arc\u003cRwLock\u003cHashMap\u003cNodeId, String\u003e\u003e\u003e,\n}\n\nimpl Default for NetworkConfig {\n    fn default() -\u003e Self {\n        Self {\n            timeout_secs: 10,\n            node_addresses: Arc::new(RwLock::new(HashMap::new())),\n        }\n    }\n}\n\nimpl NetworkConfig {\n    /// Create a new network config with node addresses\n    pub fn new(node_addresses: HashMap\u003cNodeId, String\u003e) -\u003e Self {\n        Self {\n            timeout_secs: 10,\n            node_addresses: Arc::new(RwLock::new(node_addresses)),\n        }\n    }\n\n    /// Add a node address\n    pub async fn add_node(\u0026self, node_id: NodeId, address: String) {\n        self.node_addresses.write().await.insert(node_id, address);\n    }\n\n    /// Get node address\n    pub async fn get_node_address(\u0026self, node_id: NodeId) -\u003e Option\u003cString\u003e {\n        self.node_addresses.read().await.get(\u0026node_id).cloned()\n    }\n}\n\n/// HTTP-based network implementation for Raft communication\n#[derive(Clone)]\npub struct ConfluxNetwork {\n    /// Network configuration\n    config: NetworkConfig,\n    /// HTTP client for making requests\n    client: Client,\n    /// Target node ID\n    target_node_id: NodeId,\n}\n\nimpl ConfluxNetwork {\n    /// Create a new network instance\n    pub fn new(config: NetworkConfig, target_node_id: NodeId) -\u003e Self {\n        let client = Client::builder()\n            .timeout(Duration::from_secs(config.timeout_secs))\n            .build()\n            .expect(\"Failed to create HTTP client\");\n\n        Self {\n            config,\n            client,\n            target_node_id,\n        }\n    }\n\n    /// Get the target node's address\n    async fn get_target_address(\u0026self) -\u003e Result\u003cString, NetworkError\u003e {\n        self.config\n            .get_node_address(self.target_node_id)\n            .await\n            .ok_or_else(|| {\n                let err = std::io::Error::new(\n                    std::io::ErrorKind::NotFound,\n                    format!(\"Address not found for node {}\", self.target_node_id),\n                );\n                NetworkError::new(\u0026err)\n            })\n    }\n\n    /// Send request with retry mechanism\n    async fn send_with_retry\u003cT, R\u003e(\u0026self, url: \u0026str, request: \u0026T) -\u003e Result\u003cR, NetworkError\u003e\n    where\n        T: serde::Serialize,\n        R: serde::de::DeserializeOwned,\n    {\n        let max_attempts = 3;\n        let mut delay = Duration::from_millis(100);\n\n        for attempt in 1..=max_attempts {\n            match self.client.post(url).json(request).send().await {\n                Ok(response) =\u003e match response.json::\u003cR\u003e().await {\n                    Ok(data) =\u003e return Ok(data),\n                    Err(e) =\u003e {\n                        error!(\"Failed to parse response (attempt {}/{}): {}\", attempt, max_attempts, e);\n                        if attempt == max_attempts {\n                            return Err(NetworkError::new(\u0026e));\n                        }\n                    }\n                },\n                Err(e) =\u003e {\n                    error!(\"Failed to send request (attempt {}/{}): {}\", attempt, max_attempts, e);\n                    if attempt == max_attempts {\n                        return Err(NetworkError::new(\u0026e));\n                    }\n                }\n            }\n\n            // Exponential backoff\n            tokio::time::sleep(delay).await;\n            delay *= 2;\n        }\n\n        unreachable!()\n    }\n\n    /// Check if target node is reachable\n    pub async fn is_reachable(\u0026self) -\u003e bool {\n        if let Ok(address) = self.get_target_address().await {\n            let url = format!(\"http://{}/health\", address);\n            match self.client.get(\u0026url).send().await {\n                Ok(response) =\u003e response.status().is_success(),\n                Err(_) =\u003e false,\n            }\n        } else {\n            false\n        }\n    }\n\n    /// Get connection statistics\n    pub async fn get_connection_stats(\u0026self) -\u003e ConnectionStats {\n        ConnectionStats {\n            target_node_id: self.target_node_id,\n            is_reachable: self.is_reachable().await,\n            timeout_secs: self.config.timeout_secs,\n        }\n    }\n}\n\n/// Connection statistics\n#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]\npub struct ConnectionStats {\n    pub target_node_id: NodeId,\n    pub is_reachable: bool,\n    pub timeout_secs: u64,\n}\n\nimpl RaftNetwork\u003cTypeConfig\u003e for ConfluxNetwork {\n    async fn append_entries(\n        \u0026mut self,\n        rpc: AppendEntriesRequest\u003cTypeConfig\u003e,\n        _option: RPCOption,\n    ) -\u003e Result\u003cAppendEntriesResponse\u003cNodeId\u003e, RPCError\u003cNodeId, Node, RaftError\u003cNodeId\u003e\u003e\u003e {\n        debug!(\"Sending AppendEntries to node {}\", self.target_node_id);\n\n        let address = self.get_target_address().await.map_err(RPCError::Network)?;\n\n        let url = format!(\"http://{}/raft/append_entries\", address);\n\n        match self.client.post(\u0026url).json(\u0026rpc).send().await {\n            Ok(response) =\u003e match response.json::\u003cAppendEntriesResponse\u003cNodeId\u003e\u003e().await {\n                Ok(resp) =\u003e {\n                    debug!(\n                        \"AppendEntries response received from node {}\",\n                        self.target_node_id\n                    );\n                    Ok(resp)\n                }\n                Err(e) =\u003e {\n                    error!(\"Failed to parse AppendEntries response: {}\", e);\n                    Err(RPCError::Network(NetworkError::new(\u0026e)))\n                }\n            },\n            Err(e) =\u003e {\n                error!(\n                    \"Failed to send AppendEntries to node {}: {}\",\n                    self.target_node_id, e\n                );\n                Err(RPCError::Network(NetworkError::new(\u0026e)))\n            }\n        }\n    }\n\n    async fn vote(\n        \u0026mut self,\n        rpc: VoteRequest\u003cNodeId\u003e,\n        _option: RPCOption,\n    ) -\u003e Result\u003cVoteResponse\u003cNodeId\u003e, RPCError\u003cNodeId, Node, RaftError\u003cNodeId\u003e\u003e\u003e {\n        debug!(\"Sending Vote to node {}\", self.target_node_id);\n\n        let address = self.get_target_address().await.map_err(RPCError::Network)?;\n\n        let url = format!(\"http://{}/raft/vote\", address);\n\n        match self.client.post(\u0026url).json(\u0026rpc).send().await {\n            Ok(response) =\u003e match response.json::\u003cVoteResponse\u003cNodeId\u003e\u003e().await {\n                Ok(resp) =\u003e {\n                    debug!(\"Vote response received from node {}\", self.target_node_id);\n                    Ok(resp)\n                }\n                Err(e) =\u003e {\n                    error!(\"Failed to parse Vote response: {}\", e);\n                    Err(RPCError::Network(NetworkError::new(\u0026e)))\n                }\n            },\n            Err(e) =\u003e {\n                error!(\"Failed to send Vote to node {}: {}\", self.target_node_id, e);\n                Err(RPCError::Network(NetworkError::new(\u0026e)))\n            }\n        }\n    }\n\n    async fn install_snapshot(\n        \u0026mut self,\n        rpc: InstallSnapshotRequest\u003cTypeConfig\u003e,\n        _option: RPCOption,\n    ) -\u003e Result\u003c\n        InstallSnapshotResponse\u003cNodeId\u003e,\n        RPCError\u003cNodeId, Node, RaftError\u003cNodeId, InstallSnapshotError\u003e\u003e,\n    \u003e {\n        debug!(\"Sending InstallSnapshot to node {}\", self.target_node_id);\n\n        let address = self.get_target_address().await.map_err(RPCError::Network)?;\n        let url = format!(\"http://{}/raft/install_snapshot\", address);\n\n        // Send the snapshot installation request\n        match self.client.post(\u0026url).json(\u0026rpc).send().await {\n            Ok(response) =\u003e match response.json::\u003cInstallSnapshotResponse\u003cNodeId\u003e\u003e().await {\n                Ok(resp) =\u003e {\n                    debug!(\"InstallSnapshot response received from node {}\", self.target_node_id);\n                    Ok(resp)\n                }\n                Err(e) =\u003e {\n                    error!(\"Failed to parse InstallSnapshot response: {}\", e);\n                    Err(RPCError::Network(NetworkError::new(\u0026e)))\n                }\n            },\n            Err(e) =\u003e {\n                error!(\"Failed to send InstallSnapshot to node {}: {}\", self.target_node_id, e);\n                Err(RPCError::Network(NetworkError::new(\u0026e)))\n            }\n        }\n    }\n\n    async fn full_snapshot(\n        \u0026mut self,\n        _vote: Vote\u003cNodeId\u003e,\n        _snapshot: Snapshot\u003cTypeConfig\u003e,\n        _cancel: impl std::future::Future\u003cOutput = ReplicationClosed\u003e + Send + 'static,\n        _option: RPCOption,\n    ) -\u003e Result\u003cSnapshotResponse\u003cNodeId\u003e, StreamingError\u003cTypeConfig, Fatal\u003cNodeId\u003e\u003e\u003e {\n        debug!(\"Sending full snapshot\");\n        // For now, return a simple error\n        Err(StreamingError::Timeout(openraft::error::Timeout {\n            action: RPCTypes::InstallSnapshot,\n            target: 0, // dummy target\n            id: 0,     // dummy id\n            timeout: Duration::from_secs(10),\n        }))\n    }\n}\n\n/// Network factory for creating network instances\n#[derive(Clone)]\npub struct ConfluxNetworkFactory {\n    config: NetworkConfig,\n}\n\nimpl ConfluxNetworkFactory {\n    pub fn new(config: NetworkConfig) -\u003e Self {\n        Self { config }\n    }\n}\n\nimpl RaftNetworkFactory\u003cTypeConfig\u003e for ConfluxNetworkFactory {\n    type Network = ConfluxNetwork;\n\n    async fn new_client(\u0026mut self, target: NodeId, _node: \u0026BasicNode) -\u003e Self::Network {\n        ConfluxNetwork::new(self.config.clone(), target)\n    }\n}\n","traces":[{"line":33,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":43,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":0}},{"line":52,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":90,"address":[],"length":0,"stats":{"Line":0}},{"line":91,"address":[],"length":0,"stats":{"Line":0}},{"line":92,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":95,"address":[],"length":0,"stats":{"Line":0}},{"line":97,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":107,"address":[],"length":0,"stats":{"Line":0}},{"line":108,"address":[],"length":0,"stats":{"Line":0}},{"line":110,"address":[],"length":0,"stats":{"Line":0}},{"line":111,"address":[],"length":0,"stats":{"Line":0}},{"line":112,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":0}},{"line":115,"address":[],"length":0,"stats":{"Line":0}},{"line":116,"address":[],"length":0,"stats":{"Line":0}},{"line":117,"address":[],"length":0,"stats":{"Line":0}},{"line":121,"address":[],"length":0,"stats":{"Line":0}},{"line":122,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":0}},{"line":124,"address":[],"length":0,"stats":{"Line":0}},{"line":130,"address":[],"length":0,"stats":{"Line":0}},{"line":131,"address":[],"length":0,"stats":{"Line":0}},{"line":138,"address":[],"length":0,"stats":{"Line":0}},{"line":139,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":141,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":151,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":0}},{"line":154,"address":[],"length":0,"stats":{"Line":0}},{"line":155,"address":[],"length":0,"stats":{"Line":0}},{"line":169,"address":[],"length":0,"stats":{"Line":0}},{"line":174,"address":[],"length":0,"stats":{"Line":0}},{"line":176,"address":[],"length":0,"stats":{"Line":0}},{"line":178,"address":[],"length":0,"stats":{"Line":0}},{"line":180,"address":[],"length":0,"stats":{"Line":0}},{"line":181,"address":[],"length":0,"stats":{"Line":0}},{"line":182,"address":[],"length":0,"stats":{"Line":0}},{"line":183,"address":[],"length":0,"stats":{"Line":0}},{"line":184,"address":[],"length":0,"stats":{"Line":0}},{"line":187,"address":[],"length":0,"stats":{"Line":0}},{"line":189,"address":[],"length":0,"stats":{"Line":0}},{"line":190,"address":[],"length":0,"stats":{"Line":0}},{"line":191,"address":[],"length":0,"stats":{"Line":0}},{"line":194,"address":[],"length":0,"stats":{"Line":0}},{"line":195,"address":[],"length":0,"stats":{"Line":0}},{"line":196,"address":[],"length":0,"stats":{"Line":0}},{"line":199,"address":[],"length":0,"stats":{"Line":0}},{"line":204,"address":[],"length":0,"stats":{"Line":0}},{"line":209,"address":[],"length":0,"stats":{"Line":0}},{"line":211,"address":[],"length":0,"stats":{"Line":0}},{"line":213,"address":[],"length":0,"stats":{"Line":0}},{"line":215,"address":[],"length":0,"stats":{"Line":0}},{"line":216,"address":[],"length":0,"stats":{"Line":0}},{"line":217,"address":[],"length":0,"stats":{"Line":0}},{"line":218,"address":[],"length":0,"stats":{"Line":0}},{"line":219,"address":[],"length":0,"stats":{"Line":0}},{"line":221,"address":[],"length":0,"stats":{"Line":0}},{"line":222,"address":[],"length":0,"stats":{"Line":0}},{"line":223,"address":[],"length":0,"stats":{"Line":0}},{"line":226,"address":[],"length":0,"stats":{"Line":0}},{"line":227,"address":[],"length":0,"stats":{"Line":0}},{"line":228,"address":[],"length":0,"stats":{"Line":0}},{"line":233,"address":[],"length":0,"stats":{"Line":0}},{"line":241,"address":[],"length":0,"stats":{"Line":0}},{"line":243,"address":[],"length":0,"stats":{"Line":0}},{"line":244,"address":[],"length":0,"stats":{"Line":0}},{"line":247,"address":[],"length":0,"stats":{"Line":0}},{"line":248,"address":[],"length":0,"stats":{"Line":0}},{"line":249,"address":[],"length":0,"stats":{"Line":0}},{"line":250,"address":[],"length":0,"stats":{"Line":0}},{"line":251,"address":[],"length":0,"stats":{"Line":0}},{"line":253,"address":[],"length":0,"stats":{"Line":0}},{"line":254,"address":[],"length":0,"stats":{"Line":0}},{"line":255,"address":[],"length":0,"stats":{"Line":0}},{"line":258,"address":[],"length":0,"stats":{"Line":0}},{"line":259,"address":[],"length":0,"stats":{"Line":0}},{"line":260,"address":[],"length":0,"stats":{"Line":0}},{"line":265,"address":[],"length":0,"stats":{"Line":0}},{"line":272,"address":[],"length":0,"stats":{"Line":0}},{"line":274,"address":[],"length":0,"stats":{"Line":0}},{"line":275,"address":[],"length":0,"stats":{"Line":0}},{"line":276,"address":[],"length":0,"stats":{"Line":0}},{"line":277,"address":[],"length":0,"stats":{"Line":0}},{"line":278,"address":[],"length":0,"stats":{"Line":0}},{"line":290,"address":[],"length":0,"stats":{"Line":0}},{"line":298,"address":[],"length":0,"stats":{"Line":0}},{"line":299,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":105},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","node.rs"],"content":"use crate::config::AppConfig;\nuse crate::error::Result;\nuse crate::raft::{\n    network::{ConfluxNetworkFactory, NetworkConfig},\n    store::Store,\n    types::*,\n};\nuse openraft::Config as RaftConfig;\nuse std::collections::BTreeSet;\nuse std::sync::Arc;\nuse std::time::Duration;\nuse tokio::sync::RwLock;\nuse tracing::{debug, info};\n\n/// Raft node configuration\n#[derive(Debug, Clone)]\npub struct NodeConfig {\n    /// Node ID\n    pub node_id: NodeId,\n    /// Node address for network communication\n    pub address: String,\n    /// Raft configuration\n    pub raft_config: RaftConfig,\n    /// Network configuration\n    pub network_config: NetworkConfig,\n}\n\nimpl Default for NodeConfig {\n    fn default() -\u003e Self {\n        Self {\n            node_id: 1,\n            address: \"127.0.0.1:8080\".to_string(),\n            raft_config: RaftConfig::default(),\n            network_config: NetworkConfig::default(),\n        }\n    }\n}\n\n/// Raft node implementation with integrated openraft::Raft instance\npub struct RaftNode {\n    /// Node configuration\n    config: NodeConfig,\n    /// Storage instance\n    store: Arc\u003cStore\u003e,\n    /// Network factory\n    network_factory: Arc\u003cRwLock\u003cConfluxNetworkFactory\u003e\u003e,\n    /// Current cluster members\n    members: Arc\u003cRwLock\u003cBTreeSet\u003cNodeId\u003e\u003e\u003e,\n    /// The actual Raft instance\n    raft: Option\u003cConfluxRaft\u003e,\n}\n\nimpl RaftNode {\n    /// Create a new Raft node\n    pub async fn new(config: NodeConfig, app_config: \u0026AppConfig) -\u003e Result\u003cSelf\u003e {\n        info!(\n            \"Creating Raft node {} at {}\",\n            config.node_id, config.address\n        );\n\n        // Create storage\n        let store = Arc::new(Store::new(\u0026app_config.storage.data_dir).await?);\n\n        // Create network factory\n        let network_factory = Arc::new(RwLock::new(ConfluxNetworkFactory::new(\n            config.network_config.clone(),\n        )));\n\n        // Initialize members with self\n        let mut members = BTreeSet::new();\n        members.insert(config.node_id);\n\n        Ok(Self {\n            config,\n            store,\n            network_factory,\n            members: Arc::new(RwLock::new(members)),\n            raft: None, // Will be initialized in start()\n        })\n    }\n\n    /// Get node ID\n    pub fn node_id(\u0026self) -\u003e NodeId {\n        self.config.node_id\n    }\n\n    /// Get node address\n    pub fn address(\u0026self) -\u003e \u0026str {\n        \u0026self.config.address\n    }\n\n    /// Get storage instance\n    pub fn store(\u0026self) -\u003e Arc\u003cStore\u003e {\n        self.store.clone()\n    }\n\n    /// Start the node and initialize Raft instance\n    pub async fn start(\u0026mut self) -\u003e Result\u003c()\u003e {\n        info!(\"Starting Raft node {}\", self.config.node_id);\n\n        // TODO: Initialize Raft instance\n        // This requires implementing RaftLogStorage trait for Store\n        // For now, we keep the placeholder implementation\n\n        // Create storage adaptor for openraft\n        // let storage = openraft::storage::Adaptor::new(self.store.clone());\n\n        // Create network factory\n        // let network_factory = self.network_factory.read().await.clone();\n\n        // Initialize Raft instance\n        // let raft = openraft::Raft::new(\n        //     self.config.node_id,\n        //     Arc::new(self.config.raft_config.clone()),\n        //     network_factory,\n        //     log_store,\n        //     state_machine,\n        // ).await.map_err(|e| {\n        //     crate::error::ConfluxError::raft(format!(\"Failed to initialize Raft: {}\", e))\n        // })?;\n\n        // self.raft = Some(raft);\n\n        // Initialize single-node cluster if needed\n        // if self.is_single_node_cluster().await {\n        //     self.initialize_cluster().await?;\n        // }\n\n        info!(\"Raft node {} started successfully\", self.config.node_id);\n        Ok(())\n    }\n\n    /// Get the Raft instance (if available)\n    pub fn get_raft(\u0026self) -\u003e Option\u003c\u0026ConfluxRaft\u003e {\n        self.raft.as_ref()\n    }\n\n    /// Submit a client write request through Raft\n    pub async fn client_write(\u0026self, request: ClientRequest) -\u003e Result\u003cClientWriteResponse\u003e {\n        // For MVP, directly apply to store\n        // TODO: Route through Raft consensus when properly initialized\n        info!(\n            \"Processing client write through Raft node {}\",\n            self.config.node_id\n        );\n        self.store.apply_command(\u0026request.command).await\n    }\n\n    /// Stop the node (placeholder implementation)\n    pub async fn stop(\u0026self) -\u003e Result\u003c()\u003e {\n        info!(\"Stopping Raft node {}\", self.config.node_id);\n        debug!(\"Raft node {} stopped successfully\", self.config.node_id);\n        Ok(())\n    }\n\n    /// Get current cluster members\n    pub async fn get_members(\u0026self) -\u003e BTreeSet\u003cNodeId\u003e {\n        self.members.read().await.clone()\n    }\n\n    /// Add a new node to the cluster (placeholder implementation)\n    pub async fn add_node(\u0026self, node_id: NodeId, _address: String) -\u003e Result\u003c()\u003e {\n        info!(\"Adding node {} to cluster\", node_id);\n\n        // Add to members\n        {\n            let mut members = self.members.write().await;\n            members.insert(node_id);\n        }\n\n        info!(\"Node {} added to cluster successfully\", node_id);\n        Ok(())\n    }\n\n    /// Remove a node from the cluster (placeholder implementation)\n    pub async fn remove_node(\u0026self, node_id: NodeId) -\u003e Result\u003c()\u003e {\n        info!(\"Removing node {} from cluster\", node_id);\n\n        // Remove from members\n        {\n            let mut members = self.members.write().await;\n            members.remove(\u0026node_id);\n        }\n\n        // Check if cluster is empty\n        let members = self.members.read().await;\n        if members.is_empty() {\n            return Err(crate::error::ConfluxError::raft(\n                \"Cannot remove last node from cluster\",\n            ));\n        }\n\n        info!(\"Node {} removed from cluster successfully\", node_id);\n        Ok(())\n    }\n\n    /// Check if this node is the leader\n    pub async fn is_leader(\u0026self) -\u003e bool {\n        if let Some(ref _raft) = self.raft {\n            // TODO: Use real Raft instance when available\n            // raft.is_leader().await\n            \n            // For now, use simple logic based on node ID\n            let members = self.members.read().await;\n            members.iter().next() == Some(\u0026self.config.node_id)\n        } else {\n            false\n        }\n    }\n\n    /// Get current leader ID\n    pub async fn get_leader(\u0026self) -\u003e Option\u003cNodeId\u003e {\n        if let Some(ref _raft) = self.raft {\n            // TODO: Use real Raft instance when available\n            // raft.current_leader().await\n            \n            // For now, return the first node as leader\n            let members = self.members.read().await;\n            members.iter().next().copied()\n        } else {\n            None\n        }\n    }\n\n    /// Get current Raft metrics\n    pub async fn get_metrics(\u0026self) -\u003e Result\u003cRaftMetrics\u003e {\n        if let Some(ref _raft) = self.raft {\n            // TODO: Get real metrics from Raft instance\n            // raft.metrics().borrow().clone()\n            \n            // For now, return placeholder metrics\n            Ok(RaftMetrics {\n                node_id: self.config.node_id,\n                current_term: 1,\n                last_log_index: 0,\n                last_applied: 0,\n                leader_id: self.get_leader().await,\n                membership: self.get_members().await,\n                is_leader: self.is_leader().await,\n            })\n        } else {\n            Err(crate::error::ConfluxError::raft(\"Raft not initialized\"))\n        }\n    }\n\n    /// Wait for leadership\n    pub async fn wait_for_leadership(\u0026self, timeout: Duration) -\u003e Result\u003c()\u003e {\n        let start = std::time::Instant::now();\n        \n        while start.elapsed() \u003c timeout {\n            if self.is_leader().await {\n                return Ok(());\n            }\n            tokio::time::sleep(Duration::from_millis(100)).await;\n        }\n        \n        Err(crate::error::ConfluxError::raft(\"Timeout waiting for leadership\"))\n    }\n\n    /// Change membership (add/remove nodes)\n    pub async fn change_membership(\u0026self, new_members: BTreeSet\u003cNodeId\u003e) -\u003e Result\u003c()\u003e {\n        if !self.is_leader().await {\n            return Err(crate::error::ConfluxError::raft(\"Only leader can change membership\"));\n        }\n\n        info!(\"Changing cluster membership to: {:?}\", new_members);\n\n        // Update local membership\n        {\n            let mut members = self.members.write().await;\n            *members = new_members;\n        }\n\n        // TODO: Use real Raft membership change when available\n        // if let Some(ref raft) = self.raft {\n        //     raft.change_membership(new_members, false).await?;\n        // }\n\n        info!(\"Membership change completed\");\n        Ok(())\n    }\n\n    /// Check if this is a single-node cluster\n    async fn is_single_node_cluster(\u0026self) -\u003e bool {\n        let members = self.members.read().await;\n        members.len() == 1 \u0026\u0026 members.contains(\u0026self.config.node_id)\n    }\n\n    /// Initialize a single-node cluster\n    async fn initialize_cluster(\u0026self) -\u003e Result\u003c()\u003e {\n        if let Some(ref raft) = self.raft {\n            info!(\"Initializing single-node cluster for node {}\", self.config.node_id);\n\n            let mut members = BTreeSet::new();\n            members.insert(self.config.node_id);\n\n            raft.initialize(members).await.map_err(|e| {\n                crate::error::ConfluxError::raft(format!(\"Failed to initialize cluster: {}\", e))\n            })?;\n\n            info!(\"Single-node cluster initialized successfully\");\n        }\n        Ok(())\n    }\n}\n\n/// Helper function to create a basic node configuration\npub fn create_node_config(node_id: NodeId, address: String) -\u003e NodeConfig {\n    NodeConfig {\n        node_id,\n        address,\n        raft_config: RaftConfig::default(),\n        network_config: NetworkConfig::default(),\n    }\n}\n","traces":[{"line":29,"address":[],"length":0,"stats":{"Line":0}},{"line":32,"address":[],"length":0,"stats":{"Line":0}},{"line":33,"address":[],"length":0,"stats":{"Line":0}},{"line":34,"address":[],"length":0,"stats":{"Line":0}},{"line":55,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":66,"address":[],"length":0,"stats":{"Line":0}},{"line":70,"address":[],"length":0,"stats":{"Line":0}},{"line":71,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":77,"address":[],"length":0,"stats":{"Line":0}},{"line":78,"address":[],"length":0,"stats":{"Line":0}},{"line":83,"address":[],"length":0,"stats":{"Line":0}},{"line":84,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":99,"address":[],"length":0,"stats":{"Line":0}},{"line":129,"address":[],"length":0,"stats":{"Line":0}},{"line":130,"address":[],"length":0,"stats":{"Line":0}},{"line":134,"address":[],"length":0,"stats":{"Line":0}},{"line":135,"address":[],"length":0,"stats":{"Line":0}},{"line":139,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":150,"address":[],"length":0,"stats":{"Line":0}},{"line":151,"address":[],"length":0,"stats":{"Line":0}},{"line":152,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":0}},{"line":157,"address":[],"length":0,"stats":{"Line":0}},{"line":158,"address":[],"length":0,"stats":{"Line":0}},{"line":162,"address":[],"length":0,"stats":{"Line":0}},{"line":163,"address":[],"length":0,"stats":{"Line":0}},{"line":167,"address":[],"length":0,"stats":{"Line":0}},{"line":168,"address":[],"length":0,"stats":{"Line":0}},{"line":171,"address":[],"length":0,"stats":{"Line":0}},{"line":172,"address":[],"length":0,"stats":{"Line":0}},{"line":176,"address":[],"length":0,"stats":{"Line":0}},{"line":177,"address":[],"length":0,"stats":{"Line":0}},{"line":181,"address":[],"length":0,"stats":{"Line":0}},{"line":182,"address":[],"length":0,"stats":{"Line":0}},{"line":186,"address":[],"length":0,"stats":{"Line":0}},{"line":187,"address":[],"length":0,"stats":{"Line":0}},{"line":188,"address":[],"length":0,"stats":{"Line":0}},{"line":189,"address":[],"length":0,"stats":{"Line":0}},{"line":193,"address":[],"length":0,"stats":{"Line":0}},{"line":194,"address":[],"length":0,"stats":{"Line":0}},{"line":198,"address":[],"length":0,"stats":{"Line":0}},{"line":199,"address":[],"length":0,"stats":{"Line":0}},{"line":204,"address":[],"length":0,"stats":{"Line":0}},{"line":205,"address":[],"length":0,"stats":{"Line":0}},{"line":207,"address":[],"length":0,"stats":{"Line":0}},{"line":212,"address":[],"length":0,"stats":{"Line":0}},{"line":213,"address":[],"length":0,"stats":{"Line":0}},{"line":218,"address":[],"length":0,"stats":{"Line":0}},{"line":219,"address":[],"length":0,"stats":{"Line":0}},{"line":221,"address":[],"length":0,"stats":{"Line":0}},{"line":226,"address":[],"length":0,"stats":{"Line":0}},{"line":227,"address":[],"length":0,"stats":{"Line":0}},{"line":233,"address":[],"length":0,"stats":{"Line":0}},{"line":234,"address":[],"length":0,"stats":{"Line":0}},{"line":235,"address":[],"length":0,"stats":{"Line":0}},{"line":236,"address":[],"length":0,"stats":{"Line":0}},{"line":237,"address":[],"length":0,"stats":{"Line":0}},{"line":238,"address":[],"length":0,"stats":{"Line":0}},{"line":239,"address":[],"length":0,"stats":{"Line":0}},{"line":242,"address":[],"length":0,"stats":{"Line":0}},{"line":247,"address":[],"length":0,"stats":{"Line":0}},{"line":248,"address":[],"length":0,"stats":{"Line":0}},{"line":250,"address":[],"length":0,"stats":{"Line":0}},{"line":251,"address":[],"length":0,"stats":{"Line":0}},{"line":252,"address":[],"length":0,"stats":{"Line":0}},{"line":254,"address":[],"length":0,"stats":{"Line":0}},{"line":257,"address":[],"length":0,"stats":{"Line":0}},{"line":261,"address":[],"length":0,"stats":{"Line":0}},{"line":262,"address":[],"length":0,"stats":{"Line":0}},{"line":263,"address":[],"length":0,"stats":{"Line":0}},{"line":266,"address":[],"length":0,"stats":{"Line":0}},{"line":270,"address":[],"length":0,"stats":{"Line":0}},{"line":271,"address":[],"length":0,"stats":{"Line":0}},{"line":279,"address":[],"length":0,"stats":{"Line":0}},{"line":280,"address":[],"length":0,"stats":{"Line":0}},{"line":284,"address":[],"length":0,"stats":{"Line":0}},{"line":285,"address":[],"length":0,"stats":{"Line":0}},{"line":286,"address":[],"length":0,"stats":{"Line":0}},{"line":290,"address":[],"length":0,"stats":{"Line":0}},{"line":291,"address":[],"length":0,"stats":{"Line":0}},{"line":292,"address":[],"length":0,"stats":{"Line":0}},{"line":294,"address":[],"length":0,"stats":{"Line":0}},{"line":295,"address":[],"length":0,"stats":{"Line":0}},{"line":297,"address":[],"length":0,"stats":{"Line":0}},{"line":298,"address":[],"length":0,"stats":{"Line":0}},{"line":301,"address":[],"length":0,"stats":{"Line":0}},{"line":303,"address":[],"length":0,"stats":{"Line":0}},{"line":308,"address":[],"length":0,"stats":{"Line":0}},{"line":312,"address":[],"length":0,"stats":{"Line":0}},{"line":313,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":106},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","commands","mod.rs"],"content":"// 命令处理模块\npub mod version_commands;\npub mod release_commands;\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","commands","release_commands.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse super::super::types::{Store, ConfigChangeEvent, ConfigChangeType};\n\nimpl Store {\n    /// Handle update release rules command\n    pub(crate) async fn handle_update_release_rules(\n        \u0026self,\n        config_id: \u0026u64,\n        releases: \u0026[Release],\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Find the config by ID using the new helper method\n        let (config_key, config) = match self.find_config_by_id(*config_id).await {\n            Ok((key, config)) =\u003e (key, config),\n            Err(_) =\u003e {\n                return Ok(Self::create_error_response(format!(\n                    \"Configuration with ID {} not found\",\n                    config_id\n                )));\n            }\n        };\n\n        // Validate release rules - check if all referenced versions exist\n        for release in releases {\n            if let Err(_) = self.validate_version_exists(*config_id, release.version_id).await {\n                return Ok(Self::create_error_response(format!(\n                    \"Version {} does not exist for config {}\",\n                    release.version_id, config_id\n                )));\n            }\n        }\n\n        // Update the config's release rules\n        {\n            let mut configs = self.configurations.write().await;\n            if let Some(config) = configs.get_mut(\u0026config_key) {\n                config.releases = releases.to_vec();\n                config.updated_at = chrono::Utc::now();\n                // Persist the updated config to RocksDB\n                if let Err(e) = self.persist_config(\u0026config_key, config).await {\n                    return Ok(Self::create_error_response(format!(\n                        \"Failed to persist config update: {}\", e\n                    )));\n                }\n            }\n        }\n\n        // Send notification using config info we already have\n        let _ = self.change_notifier.send(ConfigChangeEvent {\n            config_id: *config_id,\n            namespace: config.namespace.clone(),\n            name: config.name.clone(),\n            version_id: 0, // No specific version for release rule updates\n            change_type: ConfigChangeType::ReleaseUpdated,\n        });\n\n        Ok(Self::create_success_response(\n            \"Release rules updated successfully\".to_string(),\n            Some(serde_json::json!({\n                \"config_id\": config_id,\n                \"release_count\": releases.len()\n            })),\n        ))\n    }\n}\n","traces":[{"line":7,"address":[],"length":0,"stats":{"Line":10}},{"line":13,"address":[],"length":0,"stats":{"Line":18}},{"line":16,"address":[],"length":0,"stats":{"Line":2}},{"line":17,"address":[],"length":0,"stats":{"Line":2}},{"line":18,"address":[],"length":0,"stats":{"Line":2}},{"line":24,"address":[],"length":0,"stats":{"Line":34}},{"line":25,"address":[],"length":0,"stats":{"Line":14}},{"line":26,"address":[],"length":0,"stats":{"Line":2}},{"line":27,"address":[],"length":0,"stats":{"Line":2}},{"line":28,"address":[],"length":0,"stats":{"Line":2}},{"line":35,"address":[],"length":0,"stats":{"Line":12}},{"line":36,"address":[],"length":0,"stats":{"Line":12}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":6}},{"line":50,"address":[],"length":0,"stats":{"Line":6}},{"line":51,"address":[],"length":0,"stats":{"Line":6}},{"line":52,"address":[],"length":0,"stats":{"Line":6}},{"line":53,"address":[],"length":0,"stats":{"Line":6}},{"line":54,"address":[],"length":0,"stats":{"Line":6}},{"line":57,"address":[],"length":0,"stats":{"Line":6}},{"line":58,"address":[],"length":0,"stats":{"Line":6}},{"line":59,"address":[],"length":0,"stats":{"Line":6}},{"line":60,"address":[],"length":0,"stats":{"Line":6}},{"line":61,"address":[],"length":0,"stats":{"Line":6}}],"covered":23,"coverable":24},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","commands","version_commands.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse super::super::types::{Store, ConfigChangeEvent, ConfigChangeType};\nuse std::collections::BTreeMap;\n\nimpl Store {\n    /// Handle create version command\n    pub(crate) async fn handle_create_version(\n        \u0026self,\n        config_id: \u0026u64,\n        content: \u0026[u8],\n        format: \u0026Option\u003cConfigFormat\u003e,\n        creator_id: \u0026u64,\n        description: \u0026str,\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Check if config exists using the new helper method\n        let (config_key, existing_config) = match self.find_config_by_id(*config_id).await {\n            Ok((key, config)) =\u003e (key, config),\n            Err(_) =\u003e {\n                return Ok(Self::create_error_response(format!(\n                    \"Configuration with ID {} not found\",\n                    config_id\n                )));\n            }\n        };\n\n        // Generate new version ID\n        let version_id = {\n            let versions = self.versions.read().await;\n            let empty_map = BTreeMap::new();\n            let config_versions = versions.get(config_id).unwrap_or(\u0026empty_map);\n            config_versions.keys().max().copied().unwrap_or(0) + 1\n        };\n\n        // Determine format for new version\n        let version_format = if let Some(fmt) = format {\n            fmt.clone()\n        } else {\n            // Use the format from the config's latest version or default to JSON\n            let versions = self.versions.read().await;\n            let default_format = versions\n                .get(config_id)\n                .and_then(|config_versions| {\n                    config_versions.get(\u0026existing_config.latest_version_id)\n                })\n                .map(|v| v.format.clone())\n                .unwrap_or(ConfigFormat::Json);\n            default_format\n        };\n\n        // Create new version\n        let version = ConfigVersion::new(\n            version_id,\n            *config_id,\n            content.to_vec(),\n            version_format,\n            *creator_id,\n            description.to_string(),\n        );\n\n        // Persist version and update config's latest_version_id\n        if let Err(e) = self.persist_version(\u0026version).await {\n            return Ok(Self::create_error_response(format!(\n                \"Failed to persist version: {}\", e\n            )));\n        }\n\n        {\n            let mut configs = self.configurations.write().await;\n            if let Some(config) = configs.get_mut(\u0026config_key) {\n                config.latest_version_id = version_id;\n                config.updated_at = chrono::Utc::now();\n                // Persist updated config\n                if let Err(e) = self.persist_config(\u0026config_key, config).await {\n                    return Ok(Self::create_error_response(format!(\n                        \"Failed to persist config update: {}\", e\n                    )));\n                }\n            }\n        }\n\n        // Store the new version in memory\n        {\n            let mut versions = self.versions.write().await;\n            versions\n                .entry(*config_id)\n                .or_insert_with(BTreeMap::new)\n                .insert(version_id, version);\n        }\n\n        // Send notification using config info we already have\n        let _ = self.change_notifier.send(ConfigChangeEvent {\n            config_id: *config_id,\n            namespace: existing_config.namespace.clone(),\n            name: existing_config.name.clone(),\n            version_id,\n            change_type: ConfigChangeType::Updated,\n        });\n\n        Ok(Self::create_success_response(\n            \"Configuration version created successfully\".to_string(),\n            Some(serde_json::json!({\n                \"config_id\": config_id,\n                \"version_id\": version_id\n            })),\n        ))\n    }\n}\n","traces":[{"line":8,"address":[],"length":0,"stats":{"Line":8}},{"line":17,"address":[],"length":0,"stats":{"Line":16}},{"line":20,"address":[],"length":0,"stats":{"Line":0}},{"line":21,"address":[],"length":0,"stats":{"Line":0}},{"line":22,"address":[],"length":0,"stats":{"Line":0}},{"line":28,"address":[],"length":0,"stats":{"Line":8}},{"line":29,"address":[],"length":0,"stats":{"Line":8}},{"line":30,"address":[],"length":0,"stats":{"Line":8}},{"line":31,"address":[],"length":0,"stats":{"Line":8}},{"line":32,"address":[],"length":0,"stats":{"Line":8}},{"line":36,"address":[],"length":0,"stats":{"Line":24}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":43,"address":[],"length":0,"stats":{"Line":0}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":48,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":69,"address":[],"length":0,"stats":{"Line":16}},{"line":70,"address":[],"length":0,"stats":{"Line":16}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":84,"address":[],"length":0,"stats":{"Line":16}},{"line":85,"address":[],"length":0,"stats":{"Line":8}},{"line":86,"address":[],"length":0,"stats":{"Line":8}},{"line":87,"address":[],"length":0,"stats":{"Line":8}},{"line":88,"address":[],"length":0,"stats":{"Line":8}},{"line":92,"address":[],"length":0,"stats":{"Line":8}},{"line":93,"address":[],"length":0,"stats":{"Line":8}},{"line":94,"address":[],"length":0,"stats":{"Line":8}},{"line":95,"address":[],"length":0,"stats":{"Line":8}},{"line":96,"address":[],"length":0,"stats":{"Line":8}},{"line":97,"address":[],"length":0,"stats":{"Line":8}},{"line":100,"address":[],"length":0,"stats":{"Line":8}},{"line":101,"address":[],"length":0,"stats":{"Line":8}},{"line":102,"address":[],"length":0,"stats":{"Line":8}},{"line":103,"address":[],"length":0,"stats":{"Line":8}},{"line":104,"address":[],"length":0,"stats":{"Line":8}}],"covered":26,"coverable":39},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","config_ops.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse super::types::{Store, ConfigChangeEvent, ConfigChangeType};\nuse sha2::Digest;\nuse std::collections::BTreeMap;\nuse tokio::sync::broadcast;\n\nimpl Store {\n    /// Subscribe to configuration changes\n    pub fn subscribe_changes(\u0026self) -\u003e broadcast::Receiver\u003cConfigChangeEvent\u003e {\n        self.change_notifier.subscribe()\n    }\n\n    /// Get configuration by namespace and name\n    pub async fn get_config(\u0026self, namespace: \u0026ConfigNamespace, name: \u0026str) -\u003e Option\u003cConfig\u003e {\n        let key = make_config_key(namespace, name);\n        self.configurations.read().await.get(\u0026key).cloned()\n    }\n\n    /// Get configuration version\n    pub async fn get_config_version(\n        \u0026self,\n        config_id: u64,\n        version_id: u64,\n    ) -\u003e Option\u003cConfigVersion\u003e {\n        self.versions\n            .read()\n            .await\n            .get(\u0026config_id)?\n            .get(\u0026version_id)\n            .cloned()\n    }\n\n    /// Get published configuration based on client labels\n    pub async fn get_published_config(\n        \u0026self,\n        namespace: \u0026ConfigNamespace,\n        name: \u0026str,\n        client_labels: \u0026BTreeMap\u003cString, String\u003e,\n    ) -\u003e Option\u003c(Config, ConfigVersion)\u003e {\n        let config = self.get_config(namespace, name).await?;\n\n        // Find matching release rule using the new method\n        let version_id = config\n            .find_matching_release(client_labels)\n            .map(|r| r.version_id)\n            .or_else(|| config.get_default_release().map(|r| r.version_id))\n            .unwrap_or(config.latest_version_id);\n\n        let version = self.get_config_version(config.id, version_id).await?;\n        Some((config, version))\n    }\n\n    /// Get configuration metadata by ID\n    pub async fn get_config_meta(\u0026self, config_id: u64) -\u003e Option\u003cConfig\u003e {\n        let configs = self.configurations.read().await;\n        configs\n            .values()\n            .find(|config| config.id == config_id)\n            .cloned()\n    }\n\n    /// List all versions for a configuration\n    pub async fn list_config_versions(\u0026self, config_id: u64) -\u003e Vec\u003cConfigVersion\u003e {\n        let versions = self.versions.read().await;\n        versions\n            .get(\u0026config_id)\n            .map(|config_versions| config_versions.values().cloned().collect())\n            .unwrap_or_default()\n    }\n\n    /// Get the latest version of a configuration\n    pub async fn get_latest_version(\u0026self, config_id: u64) -\u003e Option\u003cConfigVersion\u003e {\n        let config = self.get_config_meta(config_id).await?;\n        self.get_config_version(config_id, config.latest_version_id)\n            .await\n    }\n\n    /// Check if a configuration exists\n    pub async fn config_exists(\u0026self, namespace: \u0026ConfigNamespace, name: \u0026str) -\u003e bool {\n        self.get_config(namespace, name).await.is_some()\n    }\n\n    /// Get all configurations in a namespace\n    pub async fn list_configs_in_namespace(\u0026self, namespace: \u0026ConfigNamespace) -\u003e Vec\u003cConfig\u003e {\n        let configs = self.configurations.read().await;\n        configs\n            .values()\n            .filter(|config| config.namespace == *namespace)\n            .cloned()\n            .collect()\n    }\n\n    /// Apply a command to the store (for testing)\n    pub async fn apply_command(\u0026self, command: \u0026RaftCommand) -\u003e Result\u003cClientWriteResponse\u003e {\n        match command {\n            RaftCommand::CreateConfig {\n                namespace,\n                name,\n                content,\n                format,\n                schema,\n                creator_id,\n                description,\n            } =\u003e {\n                self.handle_create_config(\n                    namespace, name, content, format, schema, creator_id, description,\n                )\n                .await\n            }\n            RaftCommand::UpdateConfig {\n                config_id,\n                namespace,\n                name,\n                content,\n                format,\n                schema,\n                description,\n            } =\u003e {\n                self.handle_update_config(\n                    config_id, namespace, name, content, format, schema, description,\n                )\n                .await\n            }\n            RaftCommand::CreateVersion {\n                config_id,\n                content,\n                format,\n                creator_id,\n                description,\n            } =\u003e {\n                self.handle_create_version(config_id, content, format, creator_id, description)\n                    .await\n            }\n            RaftCommand::ReleaseVersion { config_id, version_id } =\u003e {\n                self.handle_release_version(config_id, version_id).await\n            }\n            RaftCommand::UpdateReleaseRules {\n                config_id,\n                releases,\n            } =\u003e self.handle_update_release_rules(config_id, releases).await,\n            RaftCommand::DeleteConfig { config_id } =\u003e {\n                self.handle_delete_config(config_id).await\n            }\n            RaftCommand::DeleteVersions {\n                config_id,\n                version_ids,\n            } =\u003e self.handle_delete_versions(config_id, version_ids).await,\n        }\n    }\n\n    /// Handle create config command\n    async fn handle_create_config(\n        \u0026self,\n        namespace: \u0026ConfigNamespace,\n        name: \u0026str,\n        content: \u0026[u8],\n        format: \u0026ConfigFormat,\n        schema: \u0026Option\u003cString\u003e,\n        creator_id: \u0026u64,\n        description: \u0026str,\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        let config_id = {\n            let mut next_id = self.next_config_id.write().await;\n            let id = *next_id;\n            *next_id += 1;\n            id\n        };\n\n        let version_id = 1;\n        let now = chrono::Utc::now();\n\n        // Create config\n        let config = Config {\n            id: config_id,\n            namespace: namespace.clone(),\n            name: name.to_string(),\n            latest_version_id: version_id,\n            releases: vec![Release {\n                labels: BTreeMap::new(), // Default release\n                version_id,\n                priority: 0,\n            }],\n            schema: schema.clone(),\n            created_at: now,\n            updated_at: now,\n        };\n\n        // Create version\n        let version = ConfigVersion {\n            id: version_id,\n            config_id,\n            content: content.to_vec(),\n            content_hash: format!(\"{:x}\", sha2::Sha256::digest(content)),\n            format: format.clone(),\n            creator_id: *creator_id,\n            created_at: now,\n            description: description.to_string(),\n        };\n\n        // Persist to RocksDB and update in-memory state\n        let config_name_key = make_config_key(namespace, name);\n        self.persist_config(\u0026config_name_key, \u0026config).await?;\n        self.persist_version(\u0026version).await?;\n\n        self.configurations\n            .write()\n            .await\n            .insert(config_name_key.clone(), config.clone());\n        self.versions\n            .write()\n            .await\n            .entry(config_id)\n            .or_insert_with(BTreeMap::new)\n            .insert(version_id, version);\n        self.name_index\n            .write()\n            .await\n            .insert(config_name_key, config_id);\n\n        // Send notification\n        let _ = self.change_notifier.send(ConfigChangeEvent {\n            config_id,\n            namespace: namespace.clone(),\n            name: name.to_string(),\n            version_id,\n            change_type: ConfigChangeType::Created,\n        });\n\n        Ok(ClientWriteResponse {\n            config_id: Some(config_id),\n            success: true,\n            message: \"Configuration created successfully\".to_string(),\n            data: Some(serde_json::json!({\n                \"config_id\": config_id,\n                \"version_id\": version_id\n            })),\n        })\n    }\n\n    /// Handle update config command\n    async fn handle_update_config(\n        \u0026self,\n        config_id: \u0026u64,\n        namespace: \u0026ConfigNamespace,\n        name: \u0026str,\n        content: \u0026[u8],\n        format: \u0026ConfigFormat,\n        schema: \u0026Option\u003cString\u003e,\n        description: \u0026str,\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Find the existing config by ID\n        let (config_key, mut existing_config) = match self.find_config_by_id(*config_id).await {\n            Ok((key, config)) =\u003e (key, config),\n            Err(_) =\u003e {\n                return Ok(Self::create_error_response(format!(\n                    \"Configuration with ID {} not found\",\n                    config_id\n                )));\n            }\n        };\n\n        // Generate new version ID for the updated content\n        let version_id = {\n            let versions = self.versions.read().await;\n            let empty_map = BTreeMap::new();\n            let config_versions = versions.get(config_id).unwrap_or(\u0026empty_map);\n            config_versions.keys().max().copied().unwrap_or(0) + 1\n        };\n\n        let now = chrono::Utc::now();\n\n        // Update config metadata\n        let old_config_key = config_key.clone();\n        let new_config_key = make_config_key(namespace, name);\n\n        existing_config.namespace = namespace.clone();\n        existing_config.name = name.to_string();\n        existing_config.latest_version_id = version_id;\n        existing_config.schema = schema.clone();\n        existing_config.updated_at = now;\n\n        // Create new version with updated content\n        let version = ConfigVersion {\n            id: version_id,\n            config_id: *config_id,\n            content: content.to_vec(),\n            content_hash: format!(\"{:x}\", sha2::Sha256::digest(content)),\n            format: format.clone(),\n            creator_id: 0, // UpdateConfig doesn't have creator_id, using 0 as system\n            created_at: now,\n            description: description.to_string(),\n        };\n\n        // Persist to RocksDB and update in-memory state\n        self.persist_config(\u0026new_config_key, \u0026existing_config).await?;\n        self.persist_version(\u0026version).await?;\n\n        // Update in-memory structures\n        {\n            let mut configs = self.configurations.write().await;\n            // Remove old key if it's different from new key\n            if old_config_key != new_config_key {\n                configs.remove(\u0026old_config_key);\n            }\n            configs.insert(new_config_key.clone(), existing_config.clone());\n        }\n\n        {\n            let mut versions = self.versions.write().await;\n            versions\n                .entry(*config_id)\n                .or_insert_with(BTreeMap::new)\n                .insert(version_id, version);\n        }\n\n        {\n            let mut name_index = self.name_index.write().await;\n            // Remove old key if it's different from new key\n            if old_config_key != new_config_key {\n                name_index.remove(\u0026old_config_key);\n            }\n            name_index.insert(new_config_key, *config_id);\n        }\n\n        // Send notification\n        let _ = self.change_notifier.send(ConfigChangeEvent {\n            config_id: *config_id,\n            namespace: namespace.clone(),\n            name: name.to_string(),\n            version_id,\n            change_type: ConfigChangeType::Updated,\n        });\n\n        Ok(ClientWriteResponse {\n            config_id: Some(*config_id),\n            success: true,\n            message: \"Configuration updated successfully\".to_string(),\n            data: Some(serde_json::json!({\n                \"config_id\": config_id,\n                \"version_id\": version_id\n            })),\n        })\n    }\n\n    /// Handle release version command\n    async fn handle_release_version(\n        \u0026self,\n        config_id: \u0026u64,\n        version_id: \u0026u64,\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Find the config by ID\n        let (config_key, config) = match self.find_config_by_id(*config_id).await {\n            Ok((key, config)) =\u003e (key, config),\n            Err(_) =\u003e {\n                return Ok(Self::create_error_response(format!(\n                    \"Configuration with ID {} not found\",\n                    config_id\n                )));\n            }\n        };\n\n        // Validate that the version exists\n        if let Err(_) = self.validate_version_exists(*config_id, *version_id).await {\n            return Ok(Self::create_error_response(format!(\n                \"Version {} does not exist for config {}\",\n                version_id, config_id\n            )));\n        }\n\n        // Update the config's release rules to include this version as the default\n        {\n            let mut configs = self.configurations.write().await;\n            if let Some(config) = configs.get_mut(\u0026config_key) {\n                // Add or update the default release to point to this version\n                let mut found_default = false;\n                for release in \u0026mut config.releases {\n                    if release.labels.is_empty() {\n                        // This is the default release\n                        release.version_id = *version_id;\n                        found_default = true;\n                        break;\n                    }\n                }\n\n                // If no default release exists, create one\n                if !found_default {\n                    config.releases.push(Release {\n                        labels: BTreeMap::new(),\n                        version_id: *version_id,\n                        priority: 0,\n                    });\n                }\n\n                config.updated_at = chrono::Utc::now();\n\n                // Persist the updated config to RocksDB\n                if let Err(e) = self.persist_config(\u0026config_key, config).await {\n                    return Ok(Self::create_error_response(format!(\n                        \"Failed to persist config update: {}\", e\n                    )));\n                }\n            }\n        }\n\n        // Send notification\n        let _ = self.change_notifier.send(ConfigChangeEvent {\n            config_id: *config_id,\n            namespace: config.namespace.clone(),\n            name: config.name.clone(),\n            version_id: *version_id,\n            change_type: ConfigChangeType::Updated,\n        });\n\n        Ok(ClientWriteResponse {\n            config_id: Some(*config_id),\n            success: true,\n            message: format!(\"Version {} released successfully\", version_id),\n            data: Some(serde_json::json!({\n                \"config_id\": config_id,\n                \"version_id\": version_id\n            })),\n        })\n    }\n}\n","traces":[{"line":10,"address":[],"length":0,"stats":{"Line":0}},{"line":11,"address":[],"length":0,"stats":{"Line":0}},{"line":15,"address":[],"length":0,"stats":{"Line":36}},{"line":16,"address":[],"length":0,"stats":{"Line":18}},{"line":17,"address":[],"length":0,"stats":{"Line":36}},{"line":21,"address":[],"length":0,"stats":{"Line":6}},{"line":26,"address":[],"length":0,"stats":{"Line":6}},{"line":28,"address":[],"length":0,"stats":{"Line":6}},{"line":29,"address":[],"length":0,"stats":{"Line":6}},{"line":30,"address":[],"length":0,"stats":{"Line":6}},{"line":35,"address":[],"length":0,"stats":{"Line":6}},{"line":41,"address":[],"length":0,"stats":{"Line":12}},{"line":46,"address":[],"length":0,"stats":{"Line":4}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":4}},{"line":55,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":59,"address":[],"length":0,"stats":{"Line":0}},{"line":64,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":66,"address":[],"length":0,"stats":{"Line":0}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":68,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":80,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":85,"address":[],"length":0,"stats":{"Line":0}},{"line":86,"address":[],"length":0,"stats":{"Line":0}},{"line":87,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":95,"address":[],"length":0,"stats":{"Line":64}},{"line":96,"address":[],"length":0,"stats":{"Line":32}},{"line":98,"address":[],"length":0,"stats":{"Line":14}},{"line":99,"address":[],"length":0,"stats":{"Line":14}},{"line":100,"address":[],"length":0,"stats":{"Line":14}},{"line":101,"address":[],"length":0,"stats":{"Line":14}},{"line":102,"address":[],"length":0,"stats":{"Line":14}},{"line":103,"address":[],"length":0,"stats":{"Line":14}},{"line":104,"address":[],"length":0,"stats":{"Line":14}},{"line":105,"address":[],"length":0,"stats":{"Line":14}},{"line":106,"address":[],"length":0,"stats":{"Line":14}},{"line":107,"address":[],"length":0,"stats":{"Line":14}},{"line":109,"address":[],"length":0,"stats":{"Line":14}},{"line":112,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":0}},{"line":115,"address":[],"length":0,"stats":{"Line":0}},{"line":116,"address":[],"length":0,"stats":{"Line":0}},{"line":117,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":0}},{"line":119,"address":[],"length":0,"stats":{"Line":0}},{"line":120,"address":[],"length":0,"stats":{"Line":0}},{"line":121,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":0}},{"line":126,"address":[],"length":0,"stats":{"Line":8}},{"line":127,"address":[],"length":0,"stats":{"Line":8}},{"line":128,"address":[],"length":0,"stats":{"Line":8}},{"line":129,"address":[],"length":0,"stats":{"Line":8}},{"line":130,"address":[],"length":0,"stats":{"Line":8}},{"line":131,"address":[],"length":0,"stats":{"Line":8}},{"line":132,"address":[],"length":0,"stats":{"Line":8}},{"line":133,"address":[],"length":0,"stats":{"Line":8}},{"line":135,"address":[],"length":0,"stats":{"Line":0}},{"line":136,"address":[],"length":0,"stats":{"Line":0}},{"line":139,"address":[],"length":0,"stats":{"Line":10}},{"line":140,"address":[],"length":0,"stats":{"Line":10}},{"line":141,"address":[],"length":0,"stats":{"Line":10}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":146,"address":[],"length":0,"stats":{"Line":0}},{"line":147,"address":[],"length":0,"stats":{"Line":0}},{"line":148,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":14}},{"line":163,"address":[],"length":0,"stats":{"Line":14}},{"line":164,"address":[],"length":0,"stats":{"Line":28}},{"line":165,"address":[],"length":0,"stats":{"Line":14}},{"line":166,"address":[],"length":0,"stats":{"Line":14}},{"line":167,"address":[],"length":0,"stats":{"Line":14}},{"line":170,"address":[],"length":0,"stats":{"Line":14}},{"line":171,"address":[],"length":0,"stats":{"Line":14}},{"line":176,"address":[],"length":0,"stats":{"Line":14}},{"line":177,"address":[],"length":0,"stats":{"Line":14}},{"line":179,"address":[],"length":0,"stats":{"Line":14}},{"line":184,"address":[],"length":0,"stats":{"Line":14}},{"line":193,"address":[],"length":0,"stats":{"Line":14}},{"line":194,"address":[],"length":0,"stats":{"Line":14}},{"line":195,"address":[],"length":0,"stats":{"Line":14}},{"line":196,"address":[],"length":0,"stats":{"Line":14}},{"line":198,"address":[],"length":0,"stats":{"Line":14}},{"line":202,"address":[],"length":0,"stats":{"Line":14}},{"line":203,"address":[],"length":0,"stats":{"Line":14}},{"line":204,"address":[],"length":0,"stats":{"Line":14}},{"line":206,"address":[],"length":0,"stats":{"Line":14}},{"line":208,"address":[],"length":0,"stats":{"Line":14}},{"line":209,"address":[],"length":0,"stats":{"Line":14}},{"line":210,"address":[],"length":0,"stats":{"Line":14}},{"line":212,"address":[],"length":0,"stats":{"Line":14}},{"line":213,"address":[],"length":0,"stats":{"Line":14}},{"line":214,"address":[],"length":0,"stats":{"Line":14}},{"line":215,"address":[],"length":0,"stats":{"Line":14}},{"line":216,"address":[],"length":0,"stats":{"Line":14}},{"line":218,"address":[],"length":0,"stats":{"Line":14}},{"line":219,"address":[],"length":0,"stats":{"Line":14}},{"line":222,"address":[],"length":0,"stats":{"Line":14}},{"line":223,"address":[],"length":0,"stats":{"Line":14}},{"line":224,"address":[],"length":0,"stats":{"Line":14}},{"line":225,"address":[],"length":0,"stats":{"Line":14}},{"line":226,"address":[],"length":0,"stats":{"Line":14}},{"line":227,"address":[],"length":0,"stats":{"Line":14}},{"line":230,"address":[],"length":0,"stats":{"Line":14}},{"line":231,"address":[],"length":0,"stats":{"Line":14}},{"line":232,"address":[],"length":0,"stats":{"Line":14}},{"line":233,"address":[],"length":0,"stats":{"Line":14}},{"line":234,"address":[],"length":0,"stats":{"Line":14}},{"line":235,"address":[],"length":0,"stats":{"Line":14}},{"line":236,"address":[],"length":0,"stats":{"Line":14}},{"line":242,"address":[],"length":0,"stats":{"Line":0}},{"line":253,"address":[],"length":0,"stats":{"Line":0}},{"line":256,"address":[],"length":0,"stats":{"Line":0}},{"line":257,"address":[],"length":0,"stats":{"Line":0}},{"line":258,"address":[],"length":0,"stats":{"Line":0}},{"line":264,"address":[],"length":0,"stats":{"Line":0}},{"line":265,"address":[],"length":0,"stats":{"Line":0}},{"line":266,"address":[],"length":0,"stats":{"Line":0}},{"line":267,"address":[],"length":0,"stats":{"Line":0}},{"line":268,"address":[],"length":0,"stats":{"Line":0}},{"line":271,"address":[],"length":0,"stats":{"Line":0}},{"line":274,"address":[],"length":0,"stats":{"Line":0}},{"line":275,"address":[],"length":0,"stats":{"Line":0}},{"line":277,"address":[],"length":0,"stats":{"Line":0}},{"line":278,"address":[],"length":0,"stats":{"Line":0}},{"line":279,"address":[],"length":0,"stats":{"Line":0}},{"line":280,"address":[],"length":0,"stats":{"Line":0}},{"line":281,"address":[],"length":0,"stats":{"Line":0}},{"line":286,"address":[],"length":0,"stats":{"Line":0}},{"line":287,"address":[],"length":0,"stats":{"Line":0}},{"line":288,"address":[],"length":0,"stats":{"Line":0}},{"line":289,"address":[],"length":0,"stats":{"Line":0}},{"line":292,"address":[],"length":0,"stats":{"Line":0}},{"line":296,"address":[],"length":0,"stats":{"Line":0}},{"line":297,"address":[],"length":0,"stats":{"Line":0}},{"line":301,"address":[],"length":0,"stats":{"Line":0}},{"line":303,"address":[],"length":0,"stats":{"Line":0}},{"line":304,"address":[],"length":0,"stats":{"Line":0}},{"line":306,"address":[],"length":0,"stats":{"Line":0}},{"line":310,"address":[],"length":0,"stats":{"Line":0}},{"line":311,"address":[],"length":0,"stats":{"Line":0}},{"line":312,"address":[],"length":0,"stats":{"Line":0}},{"line":313,"address":[],"length":0,"stats":{"Line":0}},{"line":314,"address":[],"length":0,"stats":{"Line":0}},{"line":318,"address":[],"length":0,"stats":{"Line":0}},{"line":320,"address":[],"length":0,"stats":{"Line":0}},{"line":321,"address":[],"length":0,"stats":{"Line":0}},{"line":323,"address":[],"length":0,"stats":{"Line":0}},{"line":327,"address":[],"length":0,"stats":{"Line":0}},{"line":328,"address":[],"length":0,"stats":{"Line":0}},{"line":329,"address":[],"length":0,"stats":{"Line":0}},{"line":330,"address":[],"length":0,"stats":{"Line":0}},{"line":331,"address":[],"length":0,"stats":{"Line":0}},{"line":332,"address":[],"length":0,"stats":{"Line":0}},{"line":335,"address":[],"length":0,"stats":{"Line":0}},{"line":336,"address":[],"length":0,"stats":{"Line":0}},{"line":337,"address":[],"length":0,"stats":{"Line":0}},{"line":338,"address":[],"length":0,"stats":{"Line":0}},{"line":339,"address":[],"length":0,"stats":{"Line":0}},{"line":340,"address":[],"length":0,"stats":{"Line":0}},{"line":341,"address":[],"length":0,"stats":{"Line":0}},{"line":347,"address":[],"length":0,"stats":{"Line":0}},{"line":353,"address":[],"length":0,"stats":{"Line":0}},{"line":356,"address":[],"length":0,"stats":{"Line":0}},{"line":357,"address":[],"length":0,"stats":{"Line":0}},{"line":358,"address":[],"length":0,"stats":{"Line":0}},{"line":365,"address":[],"length":0,"stats":{"Line":0}},{"line":366,"address":[],"length":0,"stats":{"Line":0}},{"line":367,"address":[],"length":0,"stats":{"Line":0}},{"line":373,"address":[],"length":0,"stats":{"Line":0}},{"line":374,"address":[],"length":0,"stats":{"Line":0}},{"line":377,"address":[],"length":0,"stats":{"Line":0}},{"line":378,"address":[],"length":0,"stats":{"Line":0}},{"line":380,"address":[],"length":0,"stats":{"Line":0}},{"line":381,"address":[],"length":0,"stats":{"Line":0}},{"line":382,"address":[],"length":0,"stats":{"Line":0}},{"line":387,"address":[],"length":0,"stats":{"Line":0}},{"line":388,"address":[],"length":0,"stats":{"Line":0}},{"line":389,"address":[],"length":0,"stats":{"Line":0}},{"line":390,"address":[],"length":0,"stats":{"Line":0}},{"line":391,"address":[],"length":0,"stats":{"Line":0}},{"line":398,"address":[],"length":0,"stats":{"Line":0}},{"line":407,"address":[],"length":0,"stats":{"Line":0}},{"line":408,"address":[],"length":0,"stats":{"Line":0}},{"line":409,"address":[],"length":0,"stats":{"Line":0}},{"line":410,"address":[],"length":0,"stats":{"Line":0}},{"line":411,"address":[],"length":0,"stats":{"Line":0}},{"line":412,"address":[],"length":0,"stats":{"Line":0}},{"line":415,"address":[],"length":0,"stats":{"Line":0}},{"line":416,"address":[],"length":0,"stats":{"Line":0}},{"line":417,"address":[],"length":0,"stats":{"Line":0}},{"line":418,"address":[],"length":0,"stats":{"Line":0}},{"line":419,"address":[],"length":0,"stats":{"Line":0}},{"line":420,"address":[],"length":0,"stats":{"Line":0}},{"line":421,"address":[],"length":0,"stats":{"Line":0}}],"covered":80,"coverable":205},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","constants.rs"],"content":"/// RocksDB column family names\npub const CF_CONFIGS: \u0026str = \"configs\";\npub const CF_VERSIONS: \u0026str = \"versions\";\npub const CF_LOGS: \u0026str = \"logs\";\npub const CF_META: \u0026str = \"meta\";\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","delete_handlers.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse super::types::{Store, ConfigChangeEvent, ConfigChangeType};\n\nimpl Store {\n    /// Handle delete config command\n    pub(crate) async fn handle_delete_config(\n        \u0026self,\n        config_id: \u0026u64,\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Find the config by ID using the new helper method\n        let (config_key, config) = match self.find_config_by_id(*config_id).await {\n            Ok((key, config)) =\u003e (key, config),\n            Err(_) =\u003e {\n                return Ok(Self::create_error_response(format!(\n                    \"Configuration with ID {} not found\",\n                    config_id\n                )));\n            }\n        };\n\n        // Remove config and all its versions\n        {\n            let mut configs = self.configurations.write().await;\n            configs.remove(\u0026config_key);\n        }\n        {\n            let mut versions = self.versions.write().await;\n            versions.remove(config_id);\n        }\n        {\n            let mut name_index = self.name_index.write().await;\n            name_index.remove(\u0026config_key);\n        }\n\n        // Send notification using config info we already have\n        let _ = self.change_notifier.send(ConfigChangeEvent {\n            config_id: *config_id,\n            namespace: config.namespace.clone(),\n            name: config.name.clone(),\n            version_id: 0,\n            change_type: ConfigChangeType::Deleted,\n        });\n\n        Ok(Self::create_success_response(\n            \"Configuration deleted successfully\".to_string(),\n            Some(serde_json::json!({\n                \"config_id\": config_id\n            })),\n        ))\n    }\n\n    /// Handle delete versions command\n    pub(crate) async fn handle_delete_versions(\n        \u0026self,\n        config_id: \u0026u64,\n        version_ids: \u0026[u64],\n    ) -\u003e Result\u003cClientWriteResponse\u003e {\n        // Check if config exists using the new helper method\n        let _config = match self.find_config_by_id(*config_id).await {\n            Ok((_, config)) =\u003e config,\n            Err(_) =\u003e {\n                return Ok(Self::create_error_response(format!(\n                    \"Configuration with ID {} not found\",\n                    config_id\n                )));\n            }\n        };\n\n        // Remove specified versions\n        let mut deleted_count = 0;\n        {\n            let mut versions = self.versions.write().await;\n            if let Some(config_versions) = versions.get_mut(config_id) {\n                for version_id in version_ids {\n                    if config_versions.remove(version_id).is_some() {\n                        deleted_count += 1;\n                    }\n                }\n            }\n        }\n\n        Ok(Self::create_success_response(\n            format!(\"Deleted {} versions successfully\", deleted_count),\n            Some(serde_json::json!({\n                \"config_id\": config_id,\n                \"deleted_count\": deleted_count\n            })),\n        ))\n    }\n}\n","traces":[{"line":7,"address":[],"length":0,"stats":{"Line":0}},{"line":12,"address":[],"length":0,"stats":{"Line":0}},{"line":15,"address":[],"length":0,"stats":{"Line":0}},{"line":16,"address":[],"length":0,"stats":{"Line":0}},{"line":17,"address":[],"length":0,"stats":{"Line":0}},{"line":24,"address":[],"length":0,"stats":{"Line":0}},{"line":25,"address":[],"length":0,"stats":{"Line":0}},{"line":28,"address":[],"length":0,"stats":{"Line":0}},{"line":29,"address":[],"length":0,"stats":{"Line":0}},{"line":32,"address":[],"length":0,"stats":{"Line":0}},{"line":33,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":48,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":0}},{"line":60,"address":[],"length":0,"stats":{"Line":0}},{"line":63,"address":[],"length":0,"stats":{"Line":0}},{"line":64,"address":[],"length":0,"stats":{"Line":0}},{"line":65,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":77,"address":[],"length":0,"stats":{"Line":0}},{"line":83,"address":[],"length":0,"stats":{"Line":0}},{"line":84,"address":[],"length":0,"stats":{"Line":0}},{"line":85,"address":[],"length":0,"stats":{"Line":0}},{"line":86,"address":[],"length":0,"stats":{"Line":0}},{"line":87,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":36},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","mod.rs"],"content":"// Module declarations\nmod constants;\nmod types;\nmod store;\nmod persistence;\nmod config_ops;\nmod commands;\nmod delete_handlers;\nmod raft_impl;\nmod raft_storage;\nmod transaction;\n\n// Re-export public types and functions\npub use types::{Store, ConfluxStateMachine, ConfluxSnapshot, ConfigChangeEvent, ConfigChangeType};\n\n// Tests module\n#[cfg(test)]\nmod tests;\n#[cfg(test)]\nmod test_fixes;\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","persistence.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse super::constants::*;\nuse super::types::Store;\nuse rocksdb::IteratorMode;\nuse std::collections::BTreeMap;\nuse tracing::{debug, info, warn};\n\nimpl Store {\n    /// Load all data from disk into memory cache\n    pub async fn load_from_disk(\u0026self) -\u003e Result\u003c()\u003e {\n        info!(\"Loading data from disk into memory cache\");\n        \n        // Load configurations\n        self.load_configurations().await?;\n        \n        // Load versions\n        self.load_versions().await?;\n        \n        // Load name index\n        self.load_name_index().await?;\n        \n        // Load metadata\n        self.load_metadata().await?;\n        \n        info!(\"Successfully loaded all data from disk\");\n        Ok(())\n    }\n\n    /// Load configurations from RocksDB\n    async fn load_configurations(\u0026self) -\u003e Result\u003c()\u003e {\n        debug!(\"Loading configurations from RocksDB\");\n        \n        let cf_configs = self.db.cf_handle(CF_CONFIGS).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Configurations column family not found\")\n        })?;\n\n        let mut configurations = self.configurations.write().await;\n        let mut count = 0;\n\n        for item in self.db.iterator_cf(cf_configs, IteratorMode::Start) {\n            let (key, value) = item.map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Failed to read config: {}\", e))\n            })?;\n\n            let config_key = String::from_utf8(key.to_vec()).map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Invalid config key: {}\", e))\n            })?;\n\n            let config: Config = serde_json::from_slice(\u0026value).map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Failed to deserialize config: {}\", e))\n            })?;\n\n            configurations.insert(config_key, config);\n            count += 1;\n        }\n\n        debug!(\"Loaded {} configurations\", count);\n        Ok(())\n    }\n\n    /// Load versions from RocksDB\n    async fn load_versions(\u0026self) -\u003e Result\u003c()\u003e {\n        debug!(\"Loading versions from RocksDB\");\n        \n        let cf_versions = self.db.cf_handle(CF_VERSIONS).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Versions column family not found\")\n        })?;\n\n        let mut versions = self.versions.write().await;\n        let mut count = 0;\n\n        for item in self.db.iterator_cf(cf_versions, IteratorMode::Start) {\n            let (key, value) = item.map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Failed to read version: {}\", e))\n            })?;\n\n            // Parse version key (config_id + version_id)\n            if key.len() \u003c 16 {\n                warn!(\"Invalid version key length: {}\", key.len());\n                continue;\n            }\n\n            let config_id = u64::from_be_bytes([\n                key[0], key[1], key[2], key[3], key[4], key[5], key[6], key[7],\n            ]);\n            let version_id = u64::from_be_bytes([\n                key[8], key[9], key[10], key[11], key[12], key[13], key[14], key[15],\n            ]);\n\n            let version: ConfigVersion = serde_json::from_slice(\u0026value).map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Failed to deserialize version: {}\", e))\n            })?;\n\n            versions\n                .entry(config_id)\n                .or_insert_with(BTreeMap::new)\n                .insert(version_id, version);\n            count += 1;\n        }\n\n        debug!(\"Loaded {} versions\", count);\n        Ok(())\n    }\n\n    /// Load name index from RocksDB\n    async fn load_name_index(\u0026self) -\u003e Result\u003c()\u003e {\n        debug!(\"Loading name index from RocksDB\");\n        \n        let cf_meta = self.db.cf_handle(CF_META).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Meta column family not found\")\n        })?;\n\n        let mut name_index = self.name_index.write().await;\n        let mut count = 0;\n\n        for item in self.db.iterator_cf(cf_meta, IteratorMode::Start) {\n            let (key, value) = item.map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Failed to read name index: {}\", e))\n            })?;\n\n            // Only process name index entries (prefix 0x04)\n            if key.len() == 0 || key[0] != 0x04 {\n                continue;\n            }\n\n            let name_key = String::from_utf8(key[1..].to_vec()).map_err(|e| {\n                crate::error::ConfluxError::storage(format!(\"Invalid name index key: {}\", e))\n            })?;\n\n            let config_id = u64::from_be_bytes([\n                value[0], value[1], value[2], value[3], \n                value[4], value[5], value[6], value[7],\n            ]);\n\n            name_index.insert(name_key, config_id);\n            count += 1;\n        }\n\n        debug!(\"Loaded {} name index entries\", count);\n        Ok(())\n    }\n\n    /// Load metadata from RocksDB\n    async fn load_metadata(\u0026self) -\u003e Result\u003c()\u003e {\n        debug!(\"Loading metadata from RocksDB\");\n        \n        let cf_meta = self.db.cf_handle(CF_META).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Meta column family not found\")\n        })?;\n\n        // Load next_config_id (key: 0x01)\n        let next_config_id_key = vec![0x01];\n        if let Some(value) = self.db.get_cf(cf_meta, \u0026next_config_id_key).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to read next_config_id: {}\", e))\n        })? {\n            if value.len() \u003e= 8 {\n                let next_id = u64::from_be_bytes([\n                    value[0], value[1], value[2], value[3], \n                    value[4], value[5], value[6], value[7],\n                ]);\n                *self.next_config_id.write().await = next_id;\n                debug!(\"Loaded next_config_id: {}\", next_id);\n            }\n        }\n\n        Ok(())\n    }\n\n    /// Persist a configuration to RocksDB\n    pub async fn persist_config(\u0026self, config_key: \u0026str, config: \u0026Config) -\u003e Result\u003c()\u003e {\n        debug!(\"Persisting config: {}\", config_key);\n        \n        let cf_configs = self.db.cf_handle(CF_CONFIGS).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Configurations column family not found\")\n        })?;\n\n        let cf_meta = self.db.cf_handle(CF_META).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Meta column family not found\")\n        })?;\n\n        // Serialize config\n        let config_data = serde_json::to_vec(config).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to serialize config: {}\", e))\n        })?;\n\n        // Store config\n        self.db.put_cf(cf_configs, config_key.as_bytes(), config_data).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to store config: {}\", e))\n        })?;\n\n        // Update name index\n        let name_index_key = make_name_index_key(\u0026config.namespace, \u0026config.name);\n        let config_id_bytes = config.id.to_be_bytes();\n        self.db.put_cf(cf_meta, \u0026name_index_key, \u0026config_id_bytes).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to update name index: {}\", e))\n        })?;\n\n        debug!(\"Successfully persisted config: {}\", config_key);\n        Ok(())\n    }\n\n    /// Persist a version to RocksDB\n    pub async fn persist_version(\u0026self, version: \u0026ConfigVersion) -\u003e Result\u003c()\u003e {\n        debug!(\"Persisting version: config_id={}, version_id={}\", version.config_id, version.id);\n        \n        let cf_versions = self.db.cf_handle(CF_VERSIONS).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Versions column family not found\")\n        })?;\n\n        // Create version key (config_id + version_id)\n        let version_key = make_version_key(version.config_id, version.id);\n\n        // Serialize version\n        let version_data = serde_json::to_vec(version).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to serialize version: {}\", e))\n        })?;\n\n        // Store version\n        self.db.put_cf(cf_versions, \u0026version_key, version_data).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to store version: {}\", e))\n        })?;\n\n        debug!(\"Successfully persisted version: config_id={}, version_id={}\", version.config_id, version.id);\n        Ok(())\n    }\n\n    /// Persist metadata to RocksDB\n    pub async fn persist_metadata(\u0026self) -\u003e Result\u003c()\u003e {\n        debug!(\"Persisting metadata\");\n        \n        let cf_meta = self.db.cf_handle(CF_META).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Meta column family not found\")\n        })?;\n\n        // Persist next_config_id\n        let next_config_id_key = vec![0x01];\n        let next_id = *self.next_config_id.read().await;\n        let next_id_bytes = next_id.to_be_bytes();\n        \n        self.db.put_cf(cf_meta, \u0026next_config_id_key, \u0026next_id_bytes).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to persist next_config_id: {}\", e))\n        })?;\n\n        debug!(\"Successfully persisted metadata\");\n        Ok(())\n    }\n\n    /// Delete a configuration from RocksDB\n    pub async fn delete_config_from_disk(\u0026self, config_key: \u0026str, config: \u0026Config) -\u003e Result\u003c()\u003e {\n        debug!(\"Deleting config from disk: {}\", config_key);\n        \n        let cf_configs = self.db.cf_handle(CF_CONFIGS).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Configurations column family not found\")\n        })?;\n\n        let cf_meta = self.db.cf_handle(CF_META).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Meta column family not found\")\n        })?;\n\n        // Delete config\n        self.db.delete_cf(cf_configs, config_key.as_bytes()).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to delete config: {}\", e))\n        })?;\n\n        // Delete name index\n        let name_index_key = make_name_index_key(\u0026config.namespace, \u0026config.name);\n        self.db.delete_cf(cf_meta, \u0026name_index_key).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to delete name index: {}\", e))\n        })?;\n\n        debug!(\"Successfully deleted config from disk: {}\", config_key);\n        Ok(())\n    }\n\n    /// Delete a version from RocksDB\n    pub async fn delete_version_from_disk(\u0026self, config_id: u64, version_id: u64) -\u003e Result\u003c()\u003e {\n        debug!(\"Deleting version from disk: config_id={}, version_id={}\", config_id, version_id);\n        \n        let cf_versions = self.db.cf_handle(CF_VERSIONS).ok_or_else(|| {\n            crate::error::ConfluxError::storage(\"Versions column family not found\")\n        })?;\n\n        // Create version key\n        let version_key = make_version_key(config_id, version_id);\n\n        // Delete version\n        self.db.delete_cf(cf_versions, \u0026version_key).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to delete version: {}\", e))\n        })?;\n\n        debug!(\"Successfully deleted version from disk: config_id={}, version_id={}\", config_id, version_id);\n        Ok(())\n    }\n\n    /// Force flush all data to disk\n    pub async fn flush_to_disk(\u0026self) -\u003e Result\u003c()\u003e {\n        debug!(\"Flushing all data to disk\");\n        \n        self.db.flush().map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to flush to disk: {}\", e))\n        })?;\n\n        debug!(\"Successfully flushed all data to disk\");\n        Ok(())\n    }\n\n    /// Get database statistics\n    pub async fn get_storage_stats(\u0026self) -\u003e Result\u003cStorageStats\u003e {\n        debug!(\"Getting storage statistics\");\n        \n        let configs_count = self.configurations.read().await.len();\n        let versions_count = self.versions.read().await.values().map(|v| v.len()).sum();\n        let name_index_count = self.name_index.read().await.len();\n        let next_config_id = *self.next_config_id.read().await;\n\n        Ok(StorageStats {\n            configs_count,\n            versions_count,\n            name_index_count,\n            next_config_id,\n        })\n    }\n}\n\n/// Storage statistics\n#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]\npub struct StorageStats {\n    pub configs_count: usize,\n    pub versions_count: usize,\n    pub name_index_count: usize,\n    pub next_config_id: u64,\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use tempfile::TempDir;\n\n    #[tokio::test]\n    async fn test_load_from_disk() {\n        let temp_dir = TempDir::new().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        // Test loading from empty database\n        let result = store.load_from_disk().await;\n        assert!(result.is_ok());\n    }\n\n    #[tokio::test]\n    async fn test_persist_and_load_config() {\n        let temp_dir = TempDir::new().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        // Create test config\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"app\".to_string(),\n            env: \"dev\".to_string(),\n        };\n        let config = Config {\n            id: 1,\n            namespace: namespace.clone(),\n            name: \"test-config\".to_string(),\n            latest_version_id: 1,\n            releases: vec![],\n            schema: None,\n            created_at: chrono::Utc::now(),\n            updated_at: chrono::Utc::now(),\n        };\n\n        let config_key = make_config_key(\u0026namespace, \"test-config\");\n\n        // Persist config\n        let result = store.persist_config(\u0026config_key, \u0026config).await;\n        assert!(result.is_ok());\n\n        // Clear memory cache and reload from disk using the same store instance\n        {\n            let mut configs = store.configurations.write().await;\n            configs.clear();\n        }\n        \n        // Reload from disk\n        let load_result = store.load_from_disk().await;\n        assert!(load_result.is_ok());\n        \n        // Check if config was loaded\n        let loaded_config = store.get_config(\u0026namespace, \"test-config\").await;\n        assert!(loaded_config.is_some());\n        assert_eq!(loaded_config.unwrap().id, 1);\n    }\n\n    #[tokio::test]\n    async fn test_storage_stats() {\n        let temp_dir = TempDir::new().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        let stats = store.get_storage_stats().await.unwrap();\n        assert_eq!(stats.configs_count, 0);\n        assert_eq!(stats.versions_count, 0);\n        assert_eq!(stats.name_index_count, 0);\n        assert_eq!(stats.next_config_id, 1);\n    }\n}\n","traces":[{"line":11,"address":[],"length":0,"stats":{"Line":60}},{"line":12,"address":[],"length":0,"stats":{"Line":30}},{"line":15,"address":[],"length":0,"stats":{"Line":30}},{"line":18,"address":[],"length":0,"stats":{"Line":30}},{"line":21,"address":[],"length":0,"stats":{"Line":30}},{"line":24,"address":[],"length":0,"stats":{"Line":30}},{"line":26,"address":[],"length":0,"stats":{"Line":30}},{"line":31,"address":[],"length":0,"stats":{"Line":60}},{"line":32,"address":[],"length":0,"stats":{"Line":30}},{"line":34,"address":[],"length":0,"stats":{"Line":60}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":30}},{"line":39,"address":[],"length":0,"stats":{"Line":30}},{"line":41,"address":[],"length":0,"stats":{"Line":34}},{"line":42,"address":[],"length":0,"stats":{"Line":8}},{"line":43,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":4}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":4}},{"line":51,"address":[],"length":0,"stats":{"Line":0}},{"line":58,"address":[],"length":0,"stats":{"Line":30}},{"line":63,"address":[],"length":0,"stats":{"Line":60}},{"line":64,"address":[],"length":0,"stats":{"Line":30}},{"line":66,"address":[],"length":0,"stats":{"Line":60}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":70,"address":[],"length":0,"stats":{"Line":30}},{"line":71,"address":[],"length":0,"stats":{"Line":30}},{"line":73,"address":[],"length":0,"stats":{"Line":34}},{"line":74,"address":[],"length":0,"stats":{"Line":8}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":80,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":84,"address":[],"length":0,"stats":{"Line":4}},{"line":85,"address":[],"length":0,"stats":{"Line":4}},{"line":87,"address":[],"length":0,"stats":{"Line":4}},{"line":88,"address":[],"length":0,"stats":{"Line":4}},{"line":91,"address":[],"length":0,"stats":{"Line":4}},{"line":92,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":30}},{"line":107,"address":[],"length":0,"stats":{"Line":60}},{"line":108,"address":[],"length":0,"stats":{"Line":30}},{"line":110,"address":[],"length":0,"stats":{"Line":60}},{"line":111,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":30}},{"line":115,"address":[],"length":0,"stats":{"Line":30}},{"line":117,"address":[],"length":0,"stats":{"Line":34}},{"line":118,"address":[],"length":0,"stats":{"Line":8}},{"line":119,"address":[],"length":0,"stats":{"Line":0}},{"line":123,"address":[],"length":0,"stats":{"Line":4}},{"line":124,"address":[],"length":0,"stats":{"Line":0}},{"line":127,"address":[],"length":0,"stats":{"Line":4}},{"line":128,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":30}},{"line":145,"address":[],"length":0,"stats":{"Line":60}},{"line":146,"address":[],"length":0,"stats":{"Line":30}},{"line":148,"address":[],"length":0,"stats":{"Line":60}},{"line":149,"address":[],"length":0,"stats":{"Line":0}},{"line":154,"address":[],"length":0,"stats":{"Line":0}},{"line":155,"address":[],"length":0,"stats":{"Line":0}},{"line":158,"address":[],"length":0,"stats":{"Line":0}},{"line":159,"address":[],"length":0,"stats":{"Line":0}},{"line":160,"address":[],"length":0,"stats":{"Line":0}},{"line":162,"address":[],"length":0,"stats":{"Line":0}},{"line":163,"address":[],"length":0,"stats":{"Line":0}},{"line":167,"address":[],"length":0,"stats":{"Line":30}},{"line":171,"address":[],"length":0,"stats":{"Line":60}},{"line":172,"address":[],"length":0,"stats":{"Line":30}},{"line":174,"address":[],"length":0,"stats":{"Line":60}},{"line":175,"address":[],"length":0,"stats":{"Line":0}},{"line":178,"address":[],"length":0,"stats":{"Line":30}},{"line":179,"address":[],"length":0,"stats":{"Line":0}},{"line":183,"address":[],"length":0,"stats":{"Line":30}},{"line":184,"address":[],"length":0,"stats":{"Line":0}},{"line":188,"address":[],"length":0,"stats":{"Line":0}},{"line":189,"address":[],"length":0,"stats":{"Line":0}},{"line":193,"address":[],"length":0,"stats":{"Line":30}},{"line":194,"address":[],"length":0,"stats":{"Line":30}},{"line":195,"address":[],"length":0,"stats":{"Line":30}},{"line":196,"address":[],"length":0,"stats":{"Line":0}},{"line":199,"address":[],"length":0,"stats":{"Line":30}},{"line":204,"address":[],"length":0,"stats":{"Line":44}},{"line":205,"address":[],"length":0,"stats":{"Line":22}},{"line":207,"address":[],"length":0,"stats":{"Line":44}},{"line":208,"address":[],"length":0,"stats":{"Line":0}},{"line":215,"address":[],"length":0,"stats":{"Line":22}},{"line":216,"address":[],"length":0,"stats":{"Line":0}},{"line":220,"address":[],"length":0,"stats":{"Line":0}},{"line":221,"address":[],"length":0,"stats":{"Line":0}},{"line":224,"address":[],"length":0,"stats":{"Line":22}},{"line":229,"address":[],"length":0,"stats":{"Line":0}},{"line":230,"address":[],"length":0,"stats":{"Line":0}},{"line":232,"address":[],"length":0,"stats":{"Line":0}},{"line":233,"address":[],"length":0,"stats":{"Line":0}},{"line":237,"address":[],"length":0,"stats":{"Line":0}},{"line":238,"address":[],"length":0,"stats":{"Line":0}},{"line":239,"address":[],"length":0,"stats":{"Line":0}},{"line":241,"address":[],"length":0,"stats":{"Line":0}},{"line":242,"address":[],"length":0,"stats":{"Line":0}},{"line":245,"address":[],"length":0,"stats":{"Line":0}},{"line":246,"address":[],"length":0,"stats":{"Line":0}},{"line":250,"address":[],"length":0,"stats":{"Line":0}},{"line":251,"address":[],"length":0,"stats":{"Line":0}},{"line":253,"address":[],"length":0,"stats":{"Line":0}},{"line":254,"address":[],"length":0,"stats":{"Line":0}},{"line":257,"address":[],"length":0,"stats":{"Line":0}},{"line":258,"address":[],"length":0,"stats":{"Line":0}},{"line":262,"address":[],"length":0,"stats":{"Line":0}},{"line":263,"address":[],"length":0,"stats":{"Line":0}},{"line":267,"address":[],"length":0,"stats":{"Line":0}},{"line":268,"address":[],"length":0,"stats":{"Line":0}},{"line":269,"address":[],"length":0,"stats":{"Line":0}},{"line":272,"address":[],"length":0,"stats":{"Line":0}},{"line":273,"address":[],"length":0,"stats":{"Line":0}},{"line":277,"address":[],"length":0,"stats":{"Line":0}},{"line":278,"address":[],"length":0,"stats":{"Line":0}},{"line":280,"address":[],"length":0,"stats":{"Line":0}},{"line":281,"address":[],"length":0,"stats":{"Line":0}},{"line":285,"address":[],"length":0,"stats":{"Line":0}},{"line":288,"address":[],"length":0,"stats":{"Line":0}},{"line":289,"address":[],"length":0,"stats":{"Line":0}},{"line":292,"address":[],"length":0,"stats":{"Line":0}},{"line":293,"address":[],"length":0,"stats":{"Line":0}},{"line":297,"address":[],"length":0,"stats":{"Line":0}},{"line":298,"address":[],"length":0,"stats":{"Line":0}},{"line":300,"address":[],"length":0,"stats":{"Line":0}},{"line":301,"address":[],"length":0,"stats":{"Line":0}},{"line":304,"address":[],"length":0,"stats":{"Line":0}},{"line":305,"address":[],"length":0,"stats":{"Line":0}},{"line":309,"address":[],"length":0,"stats":{"Line":4}},{"line":310,"address":[],"length":0,"stats":{"Line":2}},{"line":312,"address":[],"length":0,"stats":{"Line":6}},{"line":313,"address":[],"length":0,"stats":{"Line":8}},{"line":314,"address":[],"length":0,"stats":{"Line":6}},{"line":315,"address":[],"length":0,"stats":{"Line":4}}],"covered":64,"coverable":134},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","raft_impl.rs"],"content":"use crate::raft::types::*;\nuse super::types::{Store, ConfluxSnapshot};\nuse openraft::{\n    storage::{RaftLogReader, RaftSnapshotBuilder, Snapshot, SnapshotMeta},\n    Entry, OptionalSend, StorageError, StorageIOError,\n};\nuse std::fmt::Debug;\nuse std::io::Cursor;\nuse std::ops::RangeBounds;\nuse std::sync::Arc;\n\n// Implement RaftLogReader for Arc\u003cStore\u003e\nimpl RaftLogReader\u003cTypeConfig\u003e for Arc\u003cStore\u003e {\n    async fn try_get_log_entries\u003cRB: RangeBounds\u003cu64\u003e + Clone + Debug + OptionalSend\u003e(\n        \u0026mut self,\n        range: RB,\n    ) -\u003e std::result::Result\u003cVec\u003cEntry\u003cTypeConfig\u003e\u003e, StorageError\u003cNodeId\u003e\u003e {\n        let mut entries = vec![];\n        {\n            let logs = self.logs.read().await;\n            for (_, serialized) in logs.range(range.clone()) {\n                let entry: Entry\u003cTypeConfig\u003e =\n                    serde_json::from_str(serialized).map_err(|e| StorageIOError::read_logs(\u0026e))?;\n                entries.push(entry);\n            }\n        }\n        Ok(entries)\n    }\n}\n\n// Implement RaftSnapshotBuilder for Arc\u003cStore\u003e\nimpl RaftSnapshotBuilder\u003cTypeConfig\u003e for Arc\u003cStore\u003e {\n    async fn build_snapshot(\n        \u0026mut self,\n    ) -\u003e std::result::Result\u003cSnapshot\u003cTypeConfig\u003e, StorageError\u003cNodeId\u003e\u003e {\n        let data;\n        let last_applied_log;\n        let last_membership;\n\n        {\n            let sm = self.state_machine.read().await;\n            data = serde_json::to_vec(\u0026*sm).map_err(|e| StorageIOError::read_state_machine(\u0026e))?;\n\n            last_applied_log = sm.last_applied_log;\n            last_membership = sm.last_membership.clone();\n        }\n\n        let snapshot_idx = {\n            let mut l = self.snapshot_idx.lock().unwrap();\n            *l += 1;\n            *l\n        };\n\n        let snapshot_id = if let Some(last) = last_applied_log {\n            format!(\"{}-{}-{}\", last.leader_id, last.index, snapshot_idx)\n        } else {\n            format!(\"--{}\", snapshot_idx)\n        };\n\n        let meta = SnapshotMeta {\n            last_log_id: last_applied_log,\n            last_membership,\n            snapshot_id,\n        };\n\n        let snapshot = ConfluxSnapshot {\n            meta: meta.clone(),\n            data: data.clone(),\n        };\n\n        {\n            let mut current_snapshot = self.current_snapshot.write().await;\n            *current_snapshot = Some(snapshot);\n        }\n\n        Ok(Snapshot {\n            meta,\n            snapshot: Box::new(Cursor::new(data)),\n        })\n    }\n}\n","traces":[{"line":14,"address":[],"length":0,"stats":{"Line":0}},{"line":18,"address":[],"length":0,"stats":{"Line":0}},{"line":20,"address":[],"length":0,"stats":{"Line":0}},{"line":21,"address":[],"length":0,"stats":{"Line":0}},{"line":22,"address":[],"length":0,"stats":{"Line":0}},{"line":23,"address":[],"length":0,"stats":{"Line":0}},{"line":24,"address":[],"length":0,"stats":{"Line":0}},{"line":27,"address":[],"length":0,"stats":{"Line":0}},{"line":33,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":48,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":0}},{"line":55,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":68,"address":[],"length":0,"stats":{"Line":0}},{"line":72,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":77,"address":[],"length":0,"stats":{"Line":0}},{"line":78,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":27},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","raft_storage.rs"],"content":"use crate::raft::types::*;\nuse super::types::{Store, ConfluxStateMachine, ConfluxSnapshot};\nuse openraft::{\n    storage::{LogState, Snapshot, SnapshotMeta},\n    Entry, EntryPayload, LogId, OptionalSend, RaftLogId, RaftStorage, StorageError, StorageIOError,\n    StoredMembership, Vote,\n};\nuse std::io::Cursor;\nuse std::sync::Arc;\n\n// Implement RaftStorage for Arc\u003cStore\u003e\nimpl RaftStorage\u003cTypeConfig\u003e for Arc\u003cStore\u003e {\n    async fn get_log_state(\n        \u0026mut self,\n    ) -\u003e std::result::Result\u003cLogState\u003cTypeConfig\u003e, StorageError\u003cNodeId\u003e\u003e {\n        let logs = self.logs.read().await;\n        let last_serialized = logs.iter().next_back().map(|(_, ent)| ent);\n\n        let last = match last_serialized {\n            None =\u003e None,\n            Some(serialized) =\u003e {\n                let entry: Entry\u003cTypeConfig\u003e =\n                    serde_json::from_str(serialized).map_err(|e| StorageIOError::read_logs(\u0026e))?;\n                Some(*entry.get_log_id())\n            }\n        };\n\n        let last_purged = *self.last_purged_log_id.read().await;\n\n        let last = match last {\n            None =\u003e last_purged,\n            Some(x) =\u003e Some(x),\n        };\n\n        Ok(LogState {\n            last_purged_log_id: last_purged,\n            last_log_id: last,\n        })\n    }\n\n    async fn save_vote(\n        \u0026mut self,\n        vote: \u0026Vote\u003cNodeId\u003e,\n    ) -\u003e std::result::Result\u003c(), StorageError\u003cNodeId\u003e\u003e {\n        let mut h = self.vote.write().await;\n        *h = Some(*vote);\n        Ok(())\n    }\n\n    async fn read_vote(\n        \u0026mut self,\n    ) -\u003e std::result::Result\u003cOption\u003cVote\u003cNodeId\u003e\u003e, StorageError\u003cNodeId\u003e\u003e {\n        Ok(*self.vote.read().await)\n    }\n\n    async fn last_applied_state(\n        \u0026mut self,\n    ) -\u003e std::result::Result\u003c\n        (Option\u003cLogId\u003cNodeId\u003e\u003e, StoredMembership\u003cNodeId, Node\u003e),\n        StorageError\u003cNodeId\u003e,\n    \u003e {\n        let sm = self.state_machine.read().await;\n        Ok((sm.last_applied_log, sm.last_membership.clone()))\n    }\n\n    async fn delete_conflict_logs_since(\n        \u0026mut self,\n        log_id: LogId\u003cNodeId\u003e,\n    ) -\u003e std::result::Result\u003c(), StorageError\u003cNodeId\u003e\u003e {\n        let mut logs = self.logs.write().await;\n        let keys = logs\n            .range(log_id.index..)\n            .map(|(k, _v)| *k)\n            .collect::\u003cVec\u003c_\u003e\u003e();\n        for key in keys {\n            logs.remove(\u0026key);\n        }\n        Ok(())\n    }\n\n    async fn purge_logs_upto(\n        \u0026mut self,\n        log_id: LogId\u003cNodeId\u003e,\n    ) -\u003e std::result::Result\u003c(), StorageError\u003cNodeId\u003e\u003e {\n        {\n            let mut ld = self.last_purged_log_id.write().await;\n            assert!(*ld \u003c= Some(log_id));\n            *ld = Some(log_id);\n        }\n\n        {\n            let mut logs = self.logs.write().await;\n            let keys = logs\n                .range(..=log_id.index)\n                .map(|(k, _v)| *k)\n                .collect::\u003cVec\u003c_\u003e\u003e();\n            for key in keys {\n                logs.remove(\u0026key);\n            }\n        }\n\n        Ok(())\n    }\n\n    async fn append_to_log\u003cI\u003e(\n        \u0026mut self,\n        entries: I,\n    ) -\u003e std::result::Result\u003c(), StorageError\u003cNodeId\u003e\u003e\n    where\n        I: IntoIterator\u003cItem = Entry\u003cTypeConfig\u003e\u003e + OptionalSend,\n    {\n        let mut logs = self.logs.write().await;\n        for entry in entries {\n            let s = serde_json::to_string(\u0026entry)\n                .map_err(|e| StorageIOError::write_log_entry(*entry.get_log_id(), \u0026e))?;\n            logs.insert(entry.log_id.index, s);\n        }\n        Ok(())\n    }\n\n    async fn apply_to_state_machine(\n        \u0026mut self,\n        entries: \u0026[Entry\u003cTypeConfig\u003e],\n    ) -\u003e std::result::Result\u003cVec\u003cClientWriteResponse\u003e, StorageError\u003cNodeId\u003e\u003e {\n        let mut res = Vec::with_capacity(entries.len());\n        let mut sm = self.state_machine.write().await;\n\n        for entry in entries {\n            sm.last_applied_log = Some(entry.log_id);\n\n            match \u0026entry.payload {\n                EntryPayload::Blank =\u003e res.push(ClientWriteResponse {\n                    config_id: None,\n                    success: true,\n                    message: \"Blank entry applied\".to_string(),\n                    data: None,\n                }),\n                EntryPayload::Normal(ref data) =\u003e {\n                    // Apply the command to the configuration store\n                    let response = self.apply_command(\u0026data.command).await.unwrap_or_else(|e| {\n                        ClientWriteResponse {\n                            config_id: None,\n                            success: false,\n                            message: format!(\"Error applying command: {}\", e),\n                            data: None,\n                        }\n                    });\n                    res.push(response);\n                }\n                EntryPayload::Membership(ref mem) =\u003e {\n                    sm.last_membership = StoredMembership::new(Some(entry.log_id), mem.clone());\n                    res.push(ClientWriteResponse {\n                        config_id: None,\n                        success: true,\n                        message: \"Membership updated\".to_string(),\n                        data: None,\n                    });\n                }\n            }\n        }\n        Ok(res)\n    }\n\n    async fn begin_receiving_snapshot(\n        \u0026mut self,\n    ) -\u003e std::result::Result\u003c\n        Box\u003c\u003cTypeConfig as openraft::RaftTypeConfig\u003e::SnapshotData\u003e,\n        StorageError\u003cNodeId\u003e,\n    \u003e {\n        Ok(Box::new(Cursor::new(Vec::new())))\n    }\n\n    async fn install_snapshot(\n        \u0026mut self,\n        meta: \u0026SnapshotMeta\u003cNodeId, Node\u003e,\n        snapshot: Box\u003c\u003cTypeConfig as openraft::RaftTypeConfig\u003e::SnapshotData\u003e,\n    ) -\u003e std::result::Result\u003c(), StorageError\u003cNodeId\u003e\u003e {\n        let new_snapshot = ConfluxSnapshot {\n            meta: meta.clone(),\n            data: snapshot.into_inner(),\n        };\n\n        // Update the state machine\n        {\n            let new_sm: ConfluxStateMachine =\n                serde_json::from_slice(\u0026new_snapshot.data).map_err(|e| {\n                    StorageIOError::read_snapshot(Some(new_snapshot.meta.signature()), \u0026e)\n                })?;\n            let mut sm = self.state_machine.write().await;\n            *sm = new_sm;\n        }\n\n        // Update current snapshot\n        let mut current_snapshot = self.current_snapshot.write().await;\n        *current_snapshot = Some(new_snapshot);\n        Ok(())\n    }\n\n    async fn get_current_snapshot(\n        \u0026mut self,\n    ) -\u003e std::result::Result\u003cOption\u003cSnapshot\u003cTypeConfig\u003e\u003e, StorageError\u003cNodeId\u003e\u003e {\n        match \u0026*self.current_snapshot.read().await {\n            Some(snapshot) =\u003e {\n                let data = snapshot.data.clone();\n                Ok(Some(Snapshot {\n                    meta: snapshot.meta.clone(),\n                    snapshot: Box::new(Cursor::new(data)),\n                }))\n            }\n            None =\u003e Ok(None),\n        }\n    }\n\n    type LogReader = Self;\n    type SnapshotBuilder = Self;\n\n    async fn get_log_reader(\u0026mut self) -\u003e Self::LogReader {\n        self.clone()\n    }\n\n    async fn get_snapshot_builder(\u0026mut self) -\u003e Self::SnapshotBuilder {\n        self.clone()\n    }\n}\n","traces":[{"line":13,"address":[],"length":0,"stats":{"Line":0}},{"line":16,"address":[],"length":0,"stats":{"Line":0}},{"line":17,"address":[],"length":0,"stats":{"Line":0}},{"line":19,"address":[],"length":0,"stats":{"Line":0}},{"line":20,"address":[],"length":0,"stats":{"Line":0}},{"line":21,"address":[],"length":0,"stats":{"Line":0}},{"line":22,"address":[],"length":0,"stats":{"Line":0}},{"line":23,"address":[],"length":0,"stats":{"Line":0}},{"line":24,"address":[],"length":0,"stats":{"Line":0}},{"line":28,"address":[],"length":0,"stats":{"Line":0}},{"line":30,"address":[],"length":0,"stats":{"Line":0}},{"line":31,"address":[],"length":0,"stats":{"Line":0}},{"line":32,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":46,"address":[],"length":0,"stats":{"Line":0}},{"line":47,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":53,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":62,"address":[],"length":0,"stats":{"Line":0}},{"line":63,"address":[],"length":0,"stats":{"Line":0}},{"line":66,"address":[],"length":0,"stats":{"Line":0}},{"line":70,"address":[],"length":0,"stats":{"Line":0}},{"line":71,"address":[],"length":0,"stats":{"Line":0}},{"line":72,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}},{"line":78,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":86,"address":[],"length":0,"stats":{"Line":0}},{"line":87,"address":[],"length":0,"stats":{"Line":0}},{"line":88,"address":[],"length":0,"stats":{"Line":0}},{"line":92,"address":[],"length":0,"stats":{"Line":0}},{"line":93,"address":[],"length":0,"stats":{"Line":0}},{"line":94,"address":[],"length":0,"stats":{"Line":0}},{"line":95,"address":[],"length":0,"stats":{"Line":0}},{"line":97,"address":[],"length":0,"stats":{"Line":0}},{"line":98,"address":[],"length":0,"stats":{"Line":0}},{"line":102,"address":[],"length":0,"stats":{"Line":0}},{"line":105,"address":[],"length":0,"stats":{"Line":0}},{"line":112,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":114,"address":[],"length":0,"stats":{"Line":0}},{"line":115,"address":[],"length":0,"stats":{"Line":0}},{"line":116,"address":[],"length":0,"stats":{"Line":0}},{"line":118,"address":[],"length":0,"stats":{"Line":0}},{"line":121,"address":[],"length":0,"stats":{"Line":0}},{"line":125,"address":[],"length":0,"stats":{"Line":0}},{"line":126,"address":[],"length":0,"stats":{"Line":0}},{"line":128,"address":[],"length":0,"stats":{"Line":0}},{"line":129,"address":[],"length":0,"stats":{"Line":0}},{"line":131,"address":[],"length":0,"stats":{"Line":0}},{"line":132,"address":[],"length":0,"stats":{"Line":0}},{"line":133,"address":[],"length":0,"stats":{"Line":0}},{"line":134,"address":[],"length":0,"stats":{"Line":0}},{"line":135,"address":[],"length":0,"stats":{"Line":0}},{"line":136,"address":[],"length":0,"stats":{"Line":0}},{"line":138,"address":[],"length":0,"stats":{"Line":0}},{"line":140,"address":[],"length":0,"stats":{"Line":0}},{"line":141,"address":[],"length":0,"stats":{"Line":0}},{"line":142,"address":[],"length":0,"stats":{"Line":0}},{"line":143,"address":[],"length":0,"stats":{"Line":0}},{"line":144,"address":[],"length":0,"stats":{"Line":0}},{"line":145,"address":[],"length":0,"stats":{"Line":0}},{"line":148,"address":[],"length":0,"stats":{"Line":0}},{"line":150,"address":[],"length":0,"stats":{"Line":0}},{"line":151,"address":[],"length":0,"stats":{"Line":0}},{"line":152,"address":[],"length":0,"stats":{"Line":0}},{"line":153,"address":[],"length":0,"stats":{"Line":0}},{"line":154,"address":[],"length":0,"stats":{"Line":0}},{"line":155,"address":[],"length":0,"stats":{"Line":0}},{"line":156,"address":[],"length":0,"stats":{"Line":0}},{"line":161,"address":[],"length":0,"stats":{"Line":0}},{"line":164,"address":[],"length":0,"stats":{"Line":0}},{"line":170,"address":[],"length":0,"stats":{"Line":0}},{"line":173,"address":[],"length":0,"stats":{"Line":0}},{"line":179,"address":[],"length":0,"stats":{"Line":0}},{"line":180,"address":[],"length":0,"stats":{"Line":0}},{"line":185,"address":[],"length":0,"stats":{"Line":0}},{"line":186,"address":[],"length":0,"stats":{"Line":0}},{"line":187,"address":[],"length":0,"stats":{"Line":0}},{"line":189,"address":[],"length":0,"stats":{"Line":0}},{"line":190,"address":[],"length":0,"stats":{"Line":0}},{"line":194,"address":[],"length":0,"stats":{"Line":0}},{"line":195,"address":[],"length":0,"stats":{"Line":0}},{"line":196,"address":[],"length":0,"stats":{"Line":0}},{"line":199,"address":[],"length":0,"stats":{"Line":0}},{"line":202,"address":[],"length":0,"stats":{"Line":0}},{"line":203,"address":[],"length":0,"stats":{"Line":0}},{"line":204,"address":[],"length":0,"stats":{"Line":0}},{"line":205,"address":[],"length":0,"stats":{"Line":0}},{"line":206,"address":[],"length":0,"stats":{"Line":0}},{"line":207,"address":[],"length":0,"stats":{"Line":0}},{"line":210,"address":[],"length":0,"stats":{"Line":0}},{"line":217,"address":[],"length":0,"stats":{"Line":0}},{"line":218,"address":[],"length":0,"stats":{"Line":0}},{"line":221,"address":[],"length":0,"stats":{"Line":0}},{"line":222,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":103},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","store.rs"],"content":"use crate::error::Result;\nuse super::constants::*;\nuse super::types::{Store, ConfluxStateMachine};\nuse rocksdb::{ColumnFamilyDescriptor, Options as RocksDbOptions, DB};\nuse std::collections::BTreeMap;\nuse std::path::Path;\nuse std::sync::{Arc, Mutex};\nuse tokio::sync::{broadcast, RwLock};\n\nimpl Store {\n    /// Create a new Store instance with RocksDB backend\n    pub async fn new\u003cP: AsRef\u003cPath\u003e\u003e(path: P) -\u003e Result\u003cSelf\u003e {\n        let (change_notifier, _) = broadcast::channel(1000);\n\n        // Create RocksDB options\n        let mut opts = RocksDbOptions::default();\n        opts.create_if_missing(true);\n        opts.create_missing_column_families(true);\n\n        // Define column families\n        let cfs = vec![\n            ColumnFamilyDescriptor::new(CF_CONFIGS, RocksDbOptions::default()),\n            ColumnFamilyDescriptor::new(CF_VERSIONS, RocksDbOptions::default()),\n            ColumnFamilyDescriptor::new(CF_LOGS, RocksDbOptions::default()),\n            ColumnFamilyDescriptor::new(CF_META, RocksDbOptions::default()),\n        ];\n\n        // Open database\n        let db = DB::open_cf_descriptors(\u0026opts, path, cfs).map_err(|e| {\n            crate::error::ConfluxError::storage(format!(\"Failed to open RocksDB: {}\", e))\n        })?;\n\n        let store = Self {\n            db: Arc::new(db),\n            configurations: Arc::new(RwLock::new(BTreeMap::new())),\n            versions: Arc::new(RwLock::new(BTreeMap::new())),\n            name_index: Arc::new(RwLock::new(BTreeMap::new())),\n            next_config_id: Arc::new(RwLock::new(1)),\n            change_notifier: Arc::new(change_notifier),\n            logs: Arc::new(RwLock::new(BTreeMap::new())),\n            last_purged_log_id: Arc::new(RwLock::new(None)),\n            vote: Arc::new(RwLock::new(None)),\n            state_machine: Arc::new(RwLock::new(ConfluxStateMachine::default())),\n            current_snapshot: Arc::new(RwLock::new(None)),\n            snapshot_idx: Arc::new(Mutex::new(0)),\n        };\n\n        // Load existing data from RocksDB into memory cache\n        store.load_from_disk().await?;\n\n        Ok(store)\n    }\n}\n","traces":[{"line":12,"address":[],"length":0,"stats":{"Line":52}},{"line":13,"address":[],"length":0,"stats":{"Line":26}},{"line":16,"address":[],"length":0,"stats":{"Line":26}},{"line":17,"address":[],"length":0,"stats":{"Line":26}},{"line":18,"address":[],"length":0,"stats":{"Line":26}},{"line":21,"address":[],"length":0,"stats":{"Line":26}},{"line":22,"address":[],"length":0,"stats":{"Line":26}},{"line":23,"address":[],"length":0,"stats":{"Line":26}},{"line":24,"address":[],"length":0,"stats":{"Line":26}},{"line":25,"address":[],"length":0,"stats":{"Line":26}},{"line":29,"address":[],"length":0,"stats":{"Line":52}},{"line":30,"address":[],"length":0,"stats":{"Line":0}},{"line":34,"address":[],"length":0,"stats":{"Line":0}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}},{"line":39,"address":[],"length":0,"stats":{"Line":0}},{"line":40,"address":[],"length":0,"stats":{"Line":0}},{"line":41,"address":[],"length":0,"stats":{"Line":0}},{"line":42,"address":[],"length":0,"stats":{"Line":0}},{"line":43,"address":[],"length":0,"stats":{"Line":0}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":26}}],"covered":12,"coverable":26},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","test_fixes.rs"],"content":"#[cfg(test)]\nmod fix_validation_tests {\n    use crate::raft::store::Store;\n    use crate::raft::types::*;\n    use std::collections::BTreeMap;\n    use tempfile::tempdir;\n\n    #[tokio::test]\n    async fn test_release_rules_persistence_fix() {\n        let temp_dir = tempdir().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"myapp\".to_string(),\n            env: \"dev\".to_string(),\n        };\n\n        // Create a config first\n        let create_command = RaftCommand::CreateConfig {\n            namespace: namespace.clone(),\n            name: \"test-config.toml\".to_string(),\n            content: b\"key = \\\"value\\\"\".to_vec(),\n            format: ConfigFormat::Toml,\n            schema: None,\n            creator_id: 1,\n            description: \"Test config\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026create_command).await.unwrap();\n        assert!(response.success);\n        let config_id = response.data.unwrap()[\"config_id\"].as_u64().unwrap();\n\n        // Create a second version\n        let version_command = RaftCommand::CreateVersion {\n            config_id,\n            content: b\"key = \\\"updated_value\\\"\".to_vec(),\n            format: Some(ConfigFormat::Toml),\n            creator_id: 1,\n            description: \"Updated config\".to_string(),\n        };\n        let response = store.apply_command(\u0026version_command).await.unwrap();\n        assert!(response.success);\n\n        // Update release rules - this should now persist correctly\n        let mut labels = BTreeMap::new();\n        labels.insert(\"env\".to_string(), \"production\".to_string());\n\n        let releases = vec![\n            Release::new(labels, 2, 10), // Production gets version 2\n            Release::default(1),         // Default gets version 1\n        ];\n\n        let update_command = RaftCommand::UpdateReleaseRules {\n            config_id,\n            releases: releases.clone(),\n        };\n\n        let response = store.apply_command(\u0026update_command).await.unwrap();\n        assert!(response.success, \"Release rules update should succeed\");\n\n        // Verify the release rules were properly updated and persisted\n        let config = store.get_config(\u0026namespace, \"test-config.toml\").await.unwrap();\n        assert_eq!(config.releases.len(), 2);\n        assert_eq!(config.releases, releases);\n\n        // Test that creating a new store instance loads the persisted data correctly\n        drop(store);\n        let new_store = Store::new(temp_dir.path()).await.unwrap();\n        let loaded_config = new_store.get_config(\u0026namespace, \"test-config.toml\").await.unwrap();\n        assert_eq!(loaded_config.releases.len(), 2);\n        assert_eq!(loaded_config.releases, releases);\n    }\n\n    #[tokio::test]\n    async fn test_improved_error_handling() {\n        let temp_dir = tempdir().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        // Test error handling for non-existent config\n        let update_command = RaftCommand::UpdateReleaseRules {\n            config_id: 999, // Non-existent config\n            releases: vec![Release::default(1)],\n        };\n\n        let response = store.apply_command(\u0026update_command).await.unwrap();\n        assert!(!response.success);\n        assert!(response.message.contains(\"Configuration with ID 999 not found\"));\n\n        // Test error handling for non-existent version\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"myapp\".to_string(),\n            env: \"dev\".to_string(),\n        };\n\n        let create_command = RaftCommand::CreateConfig {\n            namespace,\n            name: \"test-config.toml\".to_string(),\n            content: b\"key = \\\"value\\\"\".to_vec(),\n            format: ConfigFormat::Toml,\n            schema: None,\n            creator_id: 1,\n            description: \"Test config\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026create_command).await.unwrap();\n        let config_id = response.data.unwrap()[\"config_id\"].as_u64().unwrap();\n\n        // Try to create release rule for non-existent version\n        let releases = vec![Release::default(999)]; // Non-existent version\n\n        let update_command = RaftCommand::UpdateReleaseRules {\n            config_id,\n            releases,\n        };\n\n        let response = store.apply_command(\u0026update_command).await.unwrap();\n        assert!(!response.success);\n        assert!(response.message.contains(\"Version 999 does not exist\"));\n    }\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","tests.rs"],"content":"#[cfg(test)]\nmod tests {\n    use crate::raft::store::Store;\n    use crate::raft::types::*;\n    use std::collections::BTreeMap;\n    use tempfile::tempdir;\n\n    #[tokio::test]\n    async fn test_create_config() {\n        let temp_dir = tempdir().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"myapp\".to_string(),\n            env: \"dev\".to_string(),\n        };\n\n        let command = RaftCommand::CreateConfig {\n            namespace: namespace.clone(),\n            name: \"database.toml\".to_string(),\n            content: b\"host = \\\"localhost\\\"\\nport = 5432\".to_vec(),\n            format: ConfigFormat::Toml,\n            schema: None,\n            creator_id: 1,\n            description: \"Initial database config\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026command).await.unwrap();\n        assert!(response.success);\n\n        // Verify config was created\n        let config = store.get_config(\u0026namespace, \"database.toml\").await;\n        assert!(config.is_some());\n\n        let config = config.unwrap();\n        assert_eq!(config.name, \"database.toml\");\n        assert_eq!(config.namespace, namespace);\n        assert_eq!(config.latest_version_id, 1);\n    }\n\n    #[tokio::test]\n    async fn test_create_version() {\n        let temp_dir = tempdir().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"myapp\".to_string(),\n            env: \"dev\".to_string(),\n        };\n\n        // First create a config\n        let create_command = RaftCommand::CreateConfig {\n            namespace: namespace.clone(),\n            name: \"database.toml\".to_string(),\n            content: b\"host = \\\"localhost\\\"\\nport = 5432\".to_vec(),\n            format: ConfigFormat::Toml,\n            schema: None,\n            creator_id: 1,\n            description: \"Initial database config\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026create_command).await.unwrap();\n        assert!(response.success);\n\n        let config_id = response.data.unwrap()[\"config_id\"].as_u64().unwrap();\n\n        // Now create a new version\n        let version_command = RaftCommand::CreateVersion {\n            config_id,\n            content: b\"host = \\\"localhost\\\"\\nport = 5433\".to_vec(),\n            format: Some(ConfigFormat::Toml),\n            creator_id: 1,\n            description: \"Updated port\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026version_command).await.unwrap();\n        assert!(response.success);\n\n        // Verify new version was created\n        let config = store.get_config(\u0026namespace, \"database.toml\").await.unwrap();\n        assert_eq!(config.latest_version_id, 2);\n\n        let version = store.get_config_version(config_id, 2).await.unwrap();\n        assert_eq!(version.description, \"Updated port\");\n    }\n\n    #[tokio::test]\n    async fn test_update_release_rules() {\n        let temp_dir = tempdir().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"myapp\".to_string(),\n            env: \"dev\".to_string(),\n        };\n\n        // Create a config\n        let create_command = RaftCommand::CreateConfig {\n            namespace: namespace.clone(),\n            name: \"database.toml\".to_string(),\n            content: b\"host = \\\"localhost\\\"\\nport = 5432\".to_vec(),\n            format: ConfigFormat::Toml,\n            schema: None,\n            creator_id: 1,\n            description: \"Initial database config\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026create_command).await.unwrap();\n        let config_id = response.data.unwrap()[\"config_id\"].as_u64().unwrap();\n\n        // Create a second version\n        let version_command = RaftCommand::CreateVersion {\n            config_id,\n            content: b\"host = \\\"localhost\\\"\\nport = 5433\".to_vec(),\n            format: Some(ConfigFormat::Toml),\n            creator_id: 1,\n            description: \"Updated port\".to_string(),\n        };\n        store.apply_command(\u0026version_command).await.unwrap();\n\n        // Update release rules\n        let mut labels = BTreeMap::new();\n        labels.insert(\"env\".to_string(), \"production\".to_string());\n\n        let releases = vec![\n            Release::new(labels, 2, 10), // Production gets version 2\n            Release::default(1),         // Default gets version 1\n        ];\n\n        let update_command = RaftCommand::UpdateReleaseRules {\n            config_id,\n            releases: releases.clone(),\n        };\n\n        let response = store.apply_command(\u0026update_command).await.unwrap();\n        assert!(response.success);\n\n        // Verify release rules were updated\n        let config = store.get_config(\u0026namespace, \"database.toml\").await.unwrap();\n        assert_eq!(config.releases.len(), 2);\n        assert_eq!(config.releases, releases);\n    }\n\n    #[tokio::test]\n    async fn test_get_published_config() {\n        let temp_dir = tempdir().unwrap();\n        let store = Store::new(temp_dir.path()).await.unwrap();\n\n        let namespace = ConfigNamespace {\n            tenant: \"test\".to_string(),\n            app: \"myapp\".to_string(),\n            env: \"dev\".to_string(),\n        };\n\n        // Create config and versions\n        let create_command = RaftCommand::CreateConfig {\n            namespace: namespace.clone(),\n            name: \"database.toml\".to_string(),\n            content: b\"host = \\\"localhost\\\"\\nport = 5432\".to_vec(),\n            format: ConfigFormat::Toml,\n            schema: None,\n            creator_id: 1,\n            description: \"Initial database config\".to_string(),\n        };\n\n        let response = store.apply_command(\u0026create_command).await.unwrap();\n        let config_id = response.data.unwrap()[\"config_id\"].as_u64().unwrap();\n\n        // Create version 2\n        let version_command = RaftCommand::CreateVersion {\n            config_id,\n            content: b\"host = \\\"localhost\\\"\\nport = 5433\".to_vec(),\n            format: Some(ConfigFormat::Toml),\n            creator_id: 1,\n            description: \"Production version\".to_string(),\n        };\n        store.apply_command(\u0026version_command).await.unwrap();\n\n        // Set up release rules\n        let mut prod_labels = BTreeMap::new();\n        prod_labels.insert(\"env\".to_string(), \"production\".to_string());\n\n        let releases = vec![\n            Release::new(prod_labels, 2, 10), // Production gets version 2\n            Release::default(1),              // Default gets version 1\n        ];\n\n        let update_command = RaftCommand::UpdateReleaseRules {\n            config_id,\n            releases,\n        };\n        store.apply_command(\u0026update_command).await.unwrap();\n\n        // Test production client\n        let mut prod_client_labels = BTreeMap::new();\n        prod_client_labels.insert(\"env\".to_string(), \"production\".to_string());\n\n        let (_config, version) = store\n            .get_published_config(\u0026namespace, \"database.toml\", \u0026prod_client_labels)\n            .await\n            .unwrap();\n\n        assert_eq!(version.id, 2);\n        assert_eq!(version.description, \"Production version\");\n\n        // Test default client\n        let dev_client_labels = BTreeMap::new();\n\n        let (_config, version) = store\n            .get_published_config(\u0026namespace, \"database.toml\", \u0026dev_client_labels)\n            .await\n            .unwrap();\n\n        assert_eq!(version.id, 1);\n        assert_eq!(version.description, \"Initial database config\");\n    }\n\n    #[tokio::test]\n    async fn test_config_version_integrity() {\n        let content = b\"test content\";\n        let version = ConfigVersion::new(\n            1,\n            1,\n            content.to_vec(),\n            ConfigFormat::Json,\n            1,\n            \"Test version\".to_string(),\n        );\n\n        assert!(version.verify_integrity());\n\n        // Test with modified content\n        let mut modified_version = version.clone();\n        modified_version.content = b\"modified content\".to_vec();\n        assert!(!modified_version.verify_integrity());\n    }\n\n    #[tokio::test]\n    async fn test_release_matching() {\n        let mut labels = BTreeMap::new();\n        labels.insert(\"env\".to_string(), \"production\".to_string());\n        labels.insert(\"region\".to_string(), \"us-east-1\".to_string());\n\n        let release = Release::new(labels, 1, 10);\n\n        // Test exact match\n        let mut client_labels = BTreeMap::new();\n        client_labels.insert(\"env\".to_string(), \"production\".to_string());\n        client_labels.insert(\"region\".to_string(), \"us-east-1\".to_string());\n        client_labels.insert(\"extra\".to_string(), \"value\".to_string());\n\n        assert!(release.matches(\u0026client_labels));\n\n        // Test partial match (should fail)\n        let mut partial_labels = BTreeMap::new();\n        partial_labels.insert(\"env\".to_string(), \"production\".to_string());\n\n        assert!(!release.matches(\u0026partial_labels));\n\n        // Test default release\n        let default_release = Release::default(1);\n        assert!(default_release.is_default());\n        assert!(default_release.matches(\u0026BTreeMap::new()));\n    }\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","transaction.rs"],"content":"use crate::error::Result;\nuse crate::raft::types::*;\nuse super::types::Store;\n\nimpl Store {\n    /// Execute a transactional operation with rollback support\n    pub(crate) async fn execute_transaction\u003cF, R\u003e(\u0026self, operation: F) -\u003e Result\u003cR\u003e\n    where\n        F: FnOnce() -\u003e Result\u003cR\u003e,\n    {\n        // For now, this is a simple wrapper, but can be extended\n        // to support more complex transaction semantics\n        operation()\n    }\n\n    /// Find config by ID with better error handling\n    pub(crate) async fn find_config_by_id(\u0026self, config_id: u64) -\u003e Result\u003c(String, Config)\u003e {\n        let configs = self.configurations.read().await;\n        let found = configs\n            .iter()\n            .find(|(_, config)| config.id == config_id)\n            .map(|(key, config)| (key.clone(), config.clone()));\n\n        match found {\n            Some((key, config)) =\u003e Ok((key, config)),\n            None =\u003e Err(crate::error::ConfluxError::validation(format!(\n                \"Configuration with ID {} not found\",\n                config_id\n            ))),\n        }\n    }\n\n    /// Validate version exists for config\n    pub(crate) async fn validate_version_exists(\u0026self, config_id: u64, version_id: u64) -\u003e Result\u003c()\u003e {\n        let versions = self.versions.read().await;\n        let version_exists = versions\n            .get(\u0026config_id)\n            .map(|config_versions| config_versions.contains_key(\u0026version_id))\n            .unwrap_or(false);\n\n        if !version_exists {\n            return Err(crate::error::ConfluxError::validation(format!(\n                \"Version {} does not exist for config {}\",\n                version_id, config_id\n            )));\n        }\n        Ok(())\n    }\n\n    /// Create a standardized error response\n    pub(crate) fn create_error_response(message: String) -\u003e ClientWriteResponse {\n        ClientWriteResponse {\n            config_id: None,\n            success: false,\n            message,\n            data: None,\n        }\n    }\n\n    /// Create a standardized success response\n    pub(crate) fn create_success_response(message: String, data: Option\u003cserde_json::Value\u003e) -\u003e ClientWriteResponse {\n        ClientWriteResponse {\n            config_id: None,\n            success: true,\n            message,\n            data,\n        }\n    }\n\n    /// Parse config name from config key more robustly\n    pub(crate) fn parse_config_name_from_key(config_key: \u0026str) -\u003e String {\n        config_key\n            .split('/')\n            .last()\n            .map(|s| s.to_string())\n            .unwrap_or_else(|| \"unknown\".to_string())\n    }\n}\n","traces":[{"line":7,"address":[],"length":0,"stats":{"Line":0}},{"line":13,"address":[],"length":0,"stats":{"Line":0}},{"line":17,"address":[],"length":0,"stats":{"Line":36}},{"line":18,"address":[],"length":0,"stats":{"Line":36}},{"line":19,"address":[],"length":0,"stats":{"Line":18}},{"line":21,"address":[],"length":0,"stats":{"Line":52}},{"line":22,"address":[],"length":0,"stats":{"Line":52}},{"line":24,"address":[],"length":0,"stats":{"Line":18}},{"line":25,"address":[],"length":0,"stats":{"Line":16}},{"line":26,"address":[],"length":0,"stats":{"Line":2}},{"line":27,"address":[],"length":0,"stats":{"Line":2}},{"line":28,"address":[],"length":0,"stats":{"Line":2}},{"line":34,"address":[],"length":0,"stats":{"Line":28}},{"line":35,"address":[],"length":0,"stats":{"Line":28}},{"line":36,"address":[],"length":0,"stats":{"Line":14}},{"line":37,"address":[],"length":0,"stats":{"Line":14}},{"line":38,"address":[],"length":0,"stats":{"Line":42}},{"line":41,"address":[],"length":0,"stats":{"Line":14}},{"line":42,"address":[],"length":0,"stats":{"Line":2}},{"line":43,"address":[],"length":0,"stats":{"Line":2}},{"line":44,"address":[],"length":0,"stats":{"Line":2}},{"line":47,"address":[],"length":0,"stats":{"Line":12}},{"line":51,"address":[],"length":0,"stats":{"Line":4}},{"line":61,"address":[],"length":0,"stats":{"Line":14}},{"line":71,"address":[],"length":0,"stats":{"Line":0}},{"line":72,"address":[],"length":0,"stats":{"Line":0}},{"line":75,"address":[],"length":0,"stats":{"Line":0}},{"line":76,"address":[],"length":0,"stats":{"Line":0}}],"covered":22,"coverable":28},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","store","types.rs"],"content":"use crate::raft::types::*;\nuse openraft::{storage::SnapshotMeta, LogId, StoredMembership, Vote};\nuse rocksdb::DB;\nuse serde::{Deserialize, Serialize};\nuse std::collections::BTreeMap;\nuse std::sync::{Arc, Mutex};\nuse tokio::sync::{broadcast, RwLock};\n\n/// Store with RocksDB backend implementing RaftStorage\n#[derive(Clone)]\npub struct Store {\n    /// RocksDB instance for persistent storage\n    pub(crate) db: Arc\u003cDB\u003e,\n\n    /// In-memory cache for configurations\n    pub(crate) configurations: Arc\u003cRwLock\u003cBTreeMap\u003cConfigKey, Config\u003e\u003e\u003e,\n\n    /// In-memory cache for configuration versions\n    pub(crate) versions: Arc\u003cRwLock\u003cBTreeMap\u003cu64, BTreeMap\u003cu64, ConfigVersion\u003e\u003e\u003e\u003e,\n\n    /// Name to config ID index\n    pub(crate) name_index: Arc\u003cRwLock\u003cBTreeMap\u003cConfigKey, u64\u003e\u003e\u003e,\n\n    /// Next available config ID\n    pub(crate) next_config_id: Arc\u003cRwLock\u003cu64\u003e\u003e,\n\n    /// Change notification broadcaster\n    pub(crate) change_notifier: Arc\u003cbroadcast::Sender\u003cConfigChangeEvent\u003e\u003e,\n\n    /// Raft log storage (serialized as JSON strings like memstore)\n    pub(crate) logs: Arc\u003cRwLock\u003cBTreeMap\u003cu64, String\u003e\u003e\u003e,\n\n    /// Last purged log ID\n    pub(crate) last_purged_log_id: Arc\u003cRwLock\u003cOption\u003cLogId\u003cNodeId\u003e\u003e\u003e\u003e,\n\n    /// Vote storage\n    pub(crate) vote: Arc\u003cRwLock\u003cOption\u003cVote\u003cNodeId\u003e\u003e\u003e\u003e,\n\n    /// State machine data\n    pub(crate) state_machine: Arc\u003cRwLock\u003cConfluxStateMachine\u003e\u003e,\n\n    /// Current snapshot\n    pub(crate) current_snapshot: Arc\u003cRwLock\u003cOption\u003cConfluxSnapshot\u003e\u003e\u003e,\n\n    /// Snapshot index counter\n    pub(crate) snapshot_idx: Arc\u003cMutex\u003cu64\u003e\u003e,\n}\n\n/// State machine for Conflux\n#[derive(Debug, Clone, Default, Serialize, Deserialize)]\npub struct ConfluxStateMachine {\n    pub last_applied_log: Option\u003cLogId\u003cNodeId\u003e\u003e,\n    pub last_membership: StoredMembership\u003cNodeId, Node\u003e,\n    // Configuration data is stored in the main Store struct\n}\n\n/// Snapshot data for Conflux\n#[derive(Debug)]\npub struct ConfluxSnapshot {\n    pub meta: SnapshotMeta\u003cNodeId, Node\u003e,\n    pub data: Vec\u003cu8\u003e,\n}\n\n/// Configuration change event\n#[derive(Debug, Clone)]\npub struct ConfigChangeEvent {\n    pub config_id: u64,\n    pub namespace: ConfigNamespace,\n    pub name: String,\n    pub version_id: u64,\n    pub change_type: ConfigChangeType,\n}\n\n/// Type of configuration change\n#[derive(Debug, Clone)]\npub enum ConfigChangeType {\n    Created,\n    Updated,\n    Deleted,\n    ReleaseUpdated,\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","types","command.rs"],"content":"use crate::raft::{ConfigFormat, Release};\n\nuse super::config::ConfigNamespace;\nuse serde::{Deserialize, Serialize};\n\n/// Raft command enumeration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum RaftCommand {\n    /// Create a new configuration with initial version\n    CreateConfig {\n        namespace: ConfigNamespace,\n        name: String,\n        content: Vec\u003cu8\u003e,\n        format: ConfigFormat,\n        schema: Option\u003cString\u003e,\n        creator_id: u64,\n        description: String,\n    },\n    /// Update an existing configuration\n    UpdateConfig {\n        config_id: u64,\n        namespace: ConfigNamespace,\n        name: String,\n        content: Vec\u003cu8\u003e,\n        format: ConfigFormat,\n        schema: Option\u003cString\u003e,\n        description: String,\n    },\n    /// Create a new version for an existing configuration\n    CreateVersion {\n        config_id: u64,\n        content: Vec\u003cu8\u003e,\n        format: Option\u003cConfigFormat\u003e, // Allow format override\n        creator_id: u64,\n        description: String,\n    },\n    /// Release a specific version\n    ReleaseVersion { config_id: u64, version_id: u64 },\n    /// Delete a configuration and all its versions\n    DeleteConfig { config_id: u64 },\n    DeleteVersions {\n        config_id: u64,\n        version_ids: Vec\u003cu64\u003e,\n    },\n    UpdateReleaseRules {\n        config_id: u64,\n        releases: Vec\u003cRelease\u003e,\n    },\n}\n\nimpl RaftCommand {\n    /// Get the config_id that this command operates on (if applicable)\n    pub fn config_id(\u0026self) -\u003e Option\u003cu64\u003e {\n        match self {\n            RaftCommand::CreateConfig { .. } =\u003e None, // New config, no ID yet\n            RaftCommand::CreateVersion { config_id, .. } =\u003e Some(*config_id),\n            RaftCommand::UpdateReleaseRules { config_id, .. } =\u003e Some(*config_id),\n            RaftCommand::DeleteConfig { config_id } =\u003e Some(*config_id),\n            RaftCommand::DeleteVersions { config_id, .. } =\u003e Some(*config_id),\n            RaftCommand::UpdateConfig { config_id, .. } =\u003e Some(*config_id),\n            RaftCommand::ReleaseVersion { config_id, .. } =\u003e Some(*config_id),\n        }\n    }\n\n    /// Get the creator_id for this command (if applicable)\n    pub fn creator_id(\u0026self) -\u003e Option\u003cu64\u003e {\n        match self {\n            RaftCommand::CreateConfig { creator_id, .. } =\u003e Some(*creator_id),\n            RaftCommand::CreateVersion { creator_id, .. } =\u003e Some(*creator_id),\n            RaftCommand::UpdateReleaseRules { .. } =\u003e None,\n            RaftCommand::DeleteConfig { .. } =\u003e None,\n            RaftCommand::DeleteVersions { .. } =\u003e None,\n            RaftCommand::UpdateConfig { .. } =\u003e None,\n            RaftCommand::ReleaseVersion { .. } =\u003e None,\n        }\n    }\n\n    /// Check if this command modifies configuration content\n    pub fn modifies_content(\u0026self) -\u003e bool {\n        matches!(\n            self,\n            RaftCommand::CreateConfig { .. }\n                | RaftCommand::CreateVersion { .. }\n                | RaftCommand::UpdateConfig { .. }\n        )\n    }\n\n    /// Check if this command modifies release rules\n    pub fn modifies_releases(\u0026self) -\u003e bool {\n        matches!(\n            self,\n            RaftCommand::UpdateReleaseRules { .. } | RaftCommand::ReleaseVersion { .. }\n        )\n    }\n}\n\n/// Client request wrapper for Raft\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ClientRequest {\n    pub command: RaftCommand,\n}\n\n/// Client response for write operations\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ClientWriteResponse {\n    pub config_id: Option\u003cu64\u003e,\n    pub success: bool,\n    pub message: String,\n    pub data: Option\u003cserde_json::Value\u003e,\n}\n\nimpl Default for ClientWriteResponse {\n    fn default() -\u003e Self {\n        Self {\n            config_id: None,\n            success: false,\n            message: \"No operation performed\".to_string(),\n            data: None,\n        }\n    }\n}\n","traces":[{"line":53,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":0}},{"line":55,"address":[],"length":0,"stats":{"Line":0}},{"line":56,"address":[],"length":0,"stats":{"Line":0}},{"line":57,"address":[],"length":0,"stats":{"Line":0}},{"line":58,"address":[],"length":0,"stats":{"Line":0}},{"line":59,"address":[],"length":0,"stats":{"Line":0}},{"line":60,"address":[],"length":0,"stats":{"Line":0}},{"line":61,"address":[],"length":0,"stats":{"Line":0}},{"line":66,"address":[],"length":0,"stats":{"Line":0}},{"line":67,"address":[],"length":0,"stats":{"Line":0}},{"line":68,"address":[],"length":0,"stats":{"Line":0}},{"line":69,"address":[],"length":0,"stats":{"Line":0}},{"line":70,"address":[],"length":0,"stats":{"Line":0}},{"line":71,"address":[],"length":0,"stats":{"Line":0}},{"line":72,"address":[],"length":0,"stats":{"Line":0}},{"line":73,"address":[],"length":0,"stats":{"Line":0}},{"line":74,"address":[],"length":0,"stats":{"Line":0}},{"line":79,"address":[],"length":0,"stats":{"Line":0}},{"line":80,"address":[],"length":0,"stats":{"Line":0}},{"line":81,"address":[],"length":0,"stats":{"Line":0}},{"line":89,"address":[],"length":0,"stats":{"Line":0}},{"line":90,"address":[],"length":0,"stats":{"Line":0}},{"line":91,"address":[],"length":0,"stats":{"Line":0}},{"line":113,"address":[],"length":0,"stats":{"Line":0}},{"line":117,"address":[],"length":0,"stats":{"Line":0}}],"covered":0,"coverable":26},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","types","config.rs"],"content":"use serde::{Deserialize, Serialize};\nuse std::collections::BTreeMap;\nuse super::helpers::make_config_key;\n\n/// Configuration namespace identifier\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]\npub struct ConfigNamespace {\n    pub tenant: String,\n    pub app: String,\n    pub env: String,\n}\n\nimpl std::fmt::Display for ConfigNamespace {\n    fn fmt(\u0026self, f: \u0026mut std::fmt::Formatter\u003c'_\u003e) -\u003e std::fmt::Result {\n        write!(f, \"{}/{}/{}\", self.tenant, self.app, self.env)\n    }\n}\n\n/// Configuration format enumeration\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum ConfigFormat {\n    Json,\n    Yaml,\n    Toml,\n    Properties,\n    Xml,\n}\n\n/// Core configuration metadata\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Config {\n    pub id: u64,\n    pub namespace: ConfigNamespace,\n    pub name: String,\n    pub latest_version_id: u64,\n    pub releases: Vec\u003cRelease\u003e,\n    pub schema: Option\u003cString\u003e,\n    pub created_at: chrono::DateTime\u003cchrono::Utc\u003e,\n    pub updated_at: chrono::DateTime\u003cchrono::Utc\u003e,\n}\n\nimpl Config {\n    /// Create a name key for indexing\n    pub fn name_key(\u0026self) -\u003e String {\n        make_config_key(\u0026self.namespace, \u0026self.name)\n    }\n\n    /// Get the default release (highest priority or fallback)\n    pub fn get_default_release(\u0026self) -\u003e Option\u003c\u0026Release\u003e {\n        self.releases.iter().max_by_key(|r| r.priority)\n    }\n\n    /// Find matching release for given client labels\n    pub fn find_matching_release(\n        \u0026self,\n        client_labels: \u0026BTreeMap\u003cString, String\u003e,\n    ) -\u003e Option\u003c\u0026Release\u003e {\n        let mut matching_releases: Vec\u003c_\u003e = self\n            .releases\n            .iter()\n            .filter(|release| {\n                // Check if client labels match release labels\n                release\n                    .labels\n                    .iter()\n                    .all(|(key, value)| client_labels.get(key) == Some(value))\n            })\n            .collect();\n\n        // Sort by priority (descending)\n        matching_releases.sort_by(|a, b| b.priority.cmp(\u0026a.priority));\n\n        // Return the highest priority matching release\n        matching_releases.first().copied()\n    }\n}\n\n/// Release rule for configuration deployment\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub struct Release {\n    pub labels: BTreeMap\u003cString, String\u003e,\n    pub version_id: u64,\n    pub priority: i32,\n}\n\nimpl Release {\n    /// Create a new release rule\n    pub fn new(labels: BTreeMap\u003cString, String\u003e, version_id: u64, priority: i32) -\u003e Self {\n        Self {\n            labels,\n            version_id,\n            priority,\n        }\n    }\n\n    /// Create a default release (no labels, priority 0)\n    pub fn default(version_id: u64) -\u003e Self {\n        Self {\n            labels: BTreeMap::new(),\n            version_id,\n            priority: 0,\n        }\n    }\n\n    /// Check if this release matches the given client labels\n    pub fn matches(\u0026self, client_labels: \u0026BTreeMap\u003cString, String\u003e) -\u003e bool {\n        self.labels\n            .iter()\n            .all(|(key, value)| client_labels.get(key) == Some(value))\n    }\n\n    /// Check if this is a default release (no labels)\n    pub fn is_default(\u0026self) -\u003e bool {\n        self.labels.is_empty()\n    }\n}\n","traces":[{"line":14,"address":[],"length":0,"stats":{"Line":64}},{"line":15,"address":[],"length":0,"stats":{"Line":64}},{"line":44,"address":[],"length":0,"stats":{"Line":0}},{"line":45,"address":[],"length":0,"stats":{"Line":0}},{"line":49,"address":[],"length":0,"stats":{"Line":0}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":54,"address":[],"length":0,"stats":{"Line":4}},{"line":58,"address":[],"length":0,"stats":{"Line":4}},{"line":59,"address":[],"length":0,"stats":{"Line":4}},{"line":61,"address":[],"length":0,"stats":{"Line":12}},{"line":63,"address":[],"length":0,"stats":{"Line":8}},{"line":64,"address":[],"length":0,"stats":{"Line":8}},{"line":65,"address":[],"length":0,"stats":{"Line":8}},{"line":66,"address":[],"length":0,"stats":{"Line":20}},{"line":71,"address":[],"length":0,"stats":{"Line":10}},{"line":74,"address":[],"length":0,"stats":{"Line":4}},{"line":88,"address":[],"length":0,"stats":{"Line":9}},{"line":97,"address":[],"length":0,"stats":{"Line":12}},{"line":99,"address":[],"length":0,"stats":{"Line":12}},{"line":106,"address":[],"length":0,"stats":{"Line":6}},{"line":107,"address":[],"length":0,"stats":{"Line":6}},{"line":109,"address":[],"length":0,"stats":{"Line":20}},{"line":113,"address":[],"length":0,"stats":{"Line":2}},{"line":114,"address":[],"length":0,"stats":{"Line":2}}],"covered":20,"coverable":24},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","types","helpers.rs"],"content":"use super::config::ConfigNamespace;\n\n/// Configuration key type for internal storage\npub type ConfigKey = String;\n\n/// Helper function to create config key\npub fn make_config_key(namespace: \u0026ConfigNamespace, name: \u0026str) -\u003e ConfigKey {\n    format!(\"{}/{}\", namespace, name)\n}\n\n/// Helper function to create config ID key\npub fn make_config_id_key(config_id: u64) -\u003e Vec\u003cu8\u003e {\n    let mut key = vec![0x02];\n    key.extend_from_slice(\u0026config_id.to_be_bytes());\n    key\n}\n\n/// Helper function to create version key\npub fn make_version_key(config_id: u64, version_id: u64) -\u003e Vec\u003cu8\u003e {\n    let mut key = vec![0x03];\n    key.extend_from_slice(\u0026config_id.to_be_bytes());\n    key.extend_from_slice(\u0026version_id.to_be_bytes());\n    key\n}\n\n/// Helper function to create name index key\npub fn make_name_index_key(namespace: \u0026ConfigNamespace, name: \u0026str) -\u003e Vec\u003cu8\u003e {\n    let mut key = vec![0x04];\n    let name_key = format!(\"{}/{}\", namespace, name);\n    key.extend_from_slice(name_key.as_bytes());\n    key\n}\n\n/// Helper function to create reverse index key\npub fn make_reverse_index_key(config_id: u64) -\u003e Vec\u003cu8\u003e {\n    let mut key = vec![0x05];\n    key.extend_from_slice(\u0026config_id.to_be_bytes());\n    key\n}\n","traces":[{"line":7,"address":[],"length":0,"stats":{"Line":34}},{"line":8,"address":[],"length":0,"stats":{"Line":34}},{"line":12,"address":[],"length":0,"stats":{"Line":0}},{"line":13,"address":[],"length":0,"stats":{"Line":0}},{"line":14,"address":[],"length":0,"stats":{"Line":0}},{"line":15,"address":[],"length":0,"stats":{"Line":0}},{"line":19,"address":[],"length":0,"stats":{"Line":22}},{"line":20,"address":[],"length":0,"stats":{"Line":22}},{"line":21,"address":[],"length":0,"stats":{"Line":22}},{"line":22,"address":[],"length":0,"stats":{"Line":22}},{"line":23,"address":[],"length":0,"stats":{"Line":22}},{"line":27,"address":[],"length":0,"stats":{"Line":30}},{"line":28,"address":[],"length":0,"stats":{"Line":30}},{"line":29,"address":[],"length":0,"stats":{"Line":30}},{"line":30,"address":[],"length":0,"stats":{"Line":30}},{"line":31,"address":[],"length":0,"stats":{"Line":30}},{"line":35,"address":[],"length":0,"stats":{"Line":0}},{"line":36,"address":[],"length":0,"stats":{"Line":0}},{"line":37,"address":[],"length":0,"stats":{"Line":0}},{"line":38,"address":[],"length":0,"stats":{"Line":0}}],"covered":12,"coverable":20},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","types","mod.rs"],"content":"use openraft::{BasicNode, Raft};\n\n// 子模块声明\npub mod config;\npub mod version;\npub mod command;\npub mod helpers;\n\n// 重新导出所有公共类型\npub use config::*;\npub use version::*;\npub use command::*;\npub use helpers::*;\n\n/// Node ID type for the Raft cluster\npub type NodeId = u64;\n\n/// Node information for cluster membership\npub type Node = BasicNode;\n\n// Declare Raft types using openraft macro\nopenraft::declare_raft_types!(\n    pub TypeConfig:\n        D = ClientRequest,\n        R = ClientWriteResponse,\n        NodeId = NodeId,\n        Node = Node,\n        SnapshotData = std::io::Cursor\u003cVec\u003cu8\u003e\u003e,\n);\n\n/// Type alias for the Raft instance\npub type ConfluxRaft = Raft\u003cTypeConfig\u003e;\n\n/// Raft metrics for monitoring and debugging\n#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]\npub struct RaftMetrics {\n    pub node_id: NodeId,\n    pub current_term: u64,\n    pub last_log_index: u64,\n    pub last_applied: u64,\n    pub leader_id: Option\u003cNodeId\u003e,\n    pub membership: std::collections::BTreeSet\u003cNodeId\u003e,\n    pub is_leader: bool,\n}\n","traces":[],"covered":0,"coverable":0},{"path":["/","Users","yeheng","workspaces","rust","conflux","src","raft","types","version.rs"],"content":"use serde::{Deserialize, Serialize};\nuse super::config::ConfigFormat;\n\n/// Immutable configuration version\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ConfigVersion {\n    pub id: u64,\n    pub config_id: u64,\n    pub content: Vec\u003cu8\u003e,\n    pub content_hash: String,\n    pub format: ConfigFormat,\n    pub creator_id: u64,\n    pub created_at: chrono::DateTime\u003cchrono::Utc\u003e,\n    pub description: String,\n}\n\nimpl ConfigVersion {\n    /// Create a new ConfigVersion with computed hash\n    pub fn new(\n        id: u64,\n        config_id: u64,\n        content: Vec\u003cu8\u003e,\n        format: ConfigFormat,\n        creator_id: u64,\n        description: String,\n    ) -\u003e Self {\n        use sha2::{Digest, Sha256};\n        let content_hash = format!(\"{:x}\", Sha256::digest(\u0026content));\n\n        Self {\n            id,\n            config_id,\n            content,\n            content_hash,\n            format,\n            creator_id,\n            created_at: chrono::Utc::now(),\n            description,\n        }\n    }\n\n    /// Verify content integrity\n    pub fn verify_integrity(\u0026self) -\u003e bool {\n        use sha2::{Digest, Sha256};\n        let computed_hash = format!(\"{:x}\", Sha256::digest(\u0026self.content));\n        computed_hash == self.content_hash\n    }\n\n    /// Get content as string (for text formats)\n    pub fn content_as_string(\u0026self) -\u003e Result\u003cString, std::string::FromUtf8Error\u003e {\n        String::from_utf8(self.content.clone())\n    }\n}\n","traces":[{"line":19,"address":[],"length":0,"stats":{"Line":10}},{"line":28,"address":[],"length":0,"stats":{"Line":10}},{"line":37,"address":[],"length":0,"stats":{"Line":10}},{"line":43,"address":[],"length":0,"stats":{"Line":4}},{"line":45,"address":[],"length":0,"stats":{"Line":4}},{"line":46,"address":[],"length":0,"stats":{"Line":4}},{"line":50,"address":[],"length":0,"stats":{"Line":0}},{"line":51,"address":[],"length":0,"stats":{"Line":0}}],"covered":6,"coverable":8}]};
        var previousData = null;
    </script>
    <script crossorigin>/** @license React v16.13.1
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';(function(d,r){"object"===typeof exports&&"undefined"!==typeof module?r(exports):"function"===typeof define&&define.amd?define(["exports"],r):(d=d||self,r(d.React={}))})(this,function(d){function r(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function w(a,b,c){this.props=a;this.context=b;this.refs=ba;this.updater=c||ca}function da(){}function L(a,b,c){this.props=a;this.context=b;this.refs=ba;this.updater=c||ca}function ea(a,b,c){var g,e={},fa=null,d=null;if(null!=b)for(g in void 0!==b.ref&&(d=b.ref),void 0!==b.key&&(fa=""+b.key),b)ha.call(b,g)&&!ia.hasOwnProperty(g)&&(e[g]=b[g]);var h=arguments.length-2;if(1===h)e.children=c;else if(1<h){for(var k=Array(h),f=0;f<h;f++)k[f]=arguments[f+2];e.children=k}if(a&&a.defaultProps)for(g in h=a.defaultProps,
h)void 0===e[g]&&(e[g]=h[g]);return{$$typeof:x,type:a,key:fa,ref:d,props:e,_owner:M.current}}function va(a,b){return{$$typeof:x,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function N(a){return"object"===typeof a&&null!==a&&a.$$typeof===x}function wa(a){var b={"=":"=0",":":"=2"};return"$"+(""+a).replace(/[=:]/g,function(a){return b[a]})}function ja(a,b,c,g){if(C.length){var e=C.pop();e.result=a;e.keyPrefix=b;e.func=c;e.context=g;e.count=0;return e}return{result:a,keyPrefix:b,func:c,
context:g,count:0}}function ka(a){a.result=null;a.keyPrefix=null;a.func=null;a.context=null;a.count=0;10>C.length&&C.push(a)}function O(a,b,c,g){var e=typeof a;if("undefined"===e||"boolean"===e)a=null;var d=!1;if(null===a)d=!0;else switch(e){case "string":case "number":d=!0;break;case "object":switch(a.$$typeof){case x:case xa:d=!0}}if(d)return c(g,a,""===b?"."+P(a,0):b),1;d=0;b=""===b?".":b+":";if(Array.isArray(a))for(var f=0;f<a.length;f++){e=a[f];var h=b+P(e,f);d+=O(e,h,c,g)}else if(null===a||
"object"!==typeof a?h=null:(h=la&&a[la]||a["@@iterator"],h="function"===typeof h?h:null),"function"===typeof h)for(a=h.call(a),f=0;!(e=a.next()).done;)e=e.value,h=b+P(e,f++),d+=O(e,h,c,g);else if("object"===e)throw c=""+a,Error(r(31,"[object Object]"===c?"object with keys {"+Object.keys(a).join(", ")+"}":c,""));return d}function Q(a,b,c){return null==a?0:O(a,"",b,c)}function P(a,b){return"object"===typeof a&&null!==a&&null!=a.key?wa(a.key):b.toString(36)}function ya(a,b,c){a.func.call(a.context,b,
a.count++)}function za(a,b,c){var g=a.result,e=a.keyPrefix;a=a.func.call(a.context,b,a.count++);Array.isArray(a)?R(a,g,c,function(a){return a}):null!=a&&(N(a)&&(a=va(a,e+(!a.key||b&&b.key===a.key?"":(""+a.key).replace(ma,"$&/")+"/")+c)),g.push(a))}function R(a,b,c,g,e){var d="";null!=c&&(d=(""+c).replace(ma,"$&/")+"/");b=ja(b,d,g,e);Q(a,za,b);ka(b)}function t(){var a=na.current;if(null===a)throw Error(r(321));return a}function S(a,b){var c=a.length;a.push(b);a:for(;;){var g=c-1>>>1,e=a[g];if(void 0!==
e&&0<D(e,b))a[g]=b,a[c]=e,c=g;else break a}}function n(a){a=a[0];return void 0===a?null:a}function E(a){var b=a[0];if(void 0!==b){var c=a.pop();if(c!==b){a[0]=c;a:for(var g=0,e=a.length;g<e;){var d=2*(g+1)-1,f=a[d],h=d+1,k=a[h];if(void 0!==f&&0>D(f,c))void 0!==k&&0>D(k,f)?(a[g]=k,a[h]=c,g=h):(a[g]=f,a[d]=c,g=d);else if(void 0!==k&&0>D(k,c))a[g]=k,a[h]=c,g=h;else break a}}return b}return null}function D(a,b){var c=a.sortIndex-b.sortIndex;return 0!==c?c:a.id-b.id}function F(a){for(var b=n(u);null!==
b;){if(null===b.callback)E(u);else if(b.startTime<=a)E(u),b.sortIndex=b.expirationTime,S(p,b);else break;b=n(u)}}function T(a){y=!1;F(a);if(!v)if(null!==n(p))v=!0,z(U);else{var b=n(u);null!==b&&G(T,b.startTime-a)}}function U(a,b){v=!1;y&&(y=!1,V());H=!0;var c=m;try{F(b);for(l=n(p);null!==l&&(!(l.expirationTime>b)||a&&!W());){var g=l.callback;if(null!==g){l.callback=null;m=l.priorityLevel;var e=g(l.expirationTime<=b);b=q();"function"===typeof e?l.callback=e:l===n(p)&&E(p);F(b)}else E(p);l=n(p)}if(null!==
l)var d=!0;else{var f=n(u);null!==f&&G(T,f.startTime-b);d=!1}return d}finally{l=null,m=c,H=!1}}function oa(a){switch(a){case 1:return-1;case 2:return 250;case 5:return **********;case 4:return 1E4;default:return 5E3}}var f="function"===typeof Symbol&&Symbol.for,x=f?Symbol.for("react.element"):60103,xa=f?Symbol.for("react.portal"):60106,Aa=f?Symbol.for("react.fragment"):60107,Ba=f?Symbol.for("react.strict_mode"):60108,Ca=f?Symbol.for("react.profiler"):60114,Da=f?Symbol.for("react.provider"):60109,
Ea=f?Symbol.for("react.context"):60110,Fa=f?Symbol.for("react.forward_ref"):60112,Ga=f?Symbol.for("react.suspense"):60113,Ha=f?Symbol.for("react.memo"):60115,Ia=f?Symbol.for("react.lazy"):60116,la="function"===typeof Symbol&&Symbol.iterator,pa=Object.getOwnPropertySymbols,Ja=Object.prototype.hasOwnProperty,Ka=Object.prototype.propertyIsEnumerable,I=function(){try{if(!Object.assign)return!1;var a=new String("abc");a[5]="de";if("5"===Object.getOwnPropertyNames(a)[0])return!1;var b={};for(a=0;10>a;a++)b["_"+
String.fromCharCode(a)]=a;if("**********"!==Object.getOwnPropertyNames(b).map(function(a){return b[a]}).join(""))return!1;var c={};"abcdefghijklmnopqrst".split("").forEach(function(a){c[a]=a});return"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},c)).join("")?!1:!0}catch(g){return!1}}()?Object.assign:function(a,b){if(null===a||void 0===a)throw new TypeError("Object.assign cannot be called with null or undefined");var c=Object(a);for(var g,e=1;e<arguments.length;e++){var d=Object(arguments[e]);
for(var f in d)Ja.call(d,f)&&(c[f]=d[f]);if(pa){g=pa(d);for(var h=0;h<g.length;h++)Ka.call(d,g[h])&&(c[g[h]]=d[g[h]])}}return c},ca={isMounted:function(a){return!1},enqueueForceUpdate:function(a,b,c){},enqueueReplaceState:function(a,b,c,d){},enqueueSetState:function(a,b,c,d){}},ba={};w.prototype.isReactComponent={};w.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error(r(85));this.updater.enqueueSetState(this,a,b,"setState")};w.prototype.forceUpdate=
function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};da.prototype=w.prototype;f=L.prototype=new da;f.constructor=L;I(f,w.prototype);f.isPureReactComponent=!0;var M={current:null},ha=Object.prototype.hasOwnProperty,ia={key:!0,ref:!0,__self:!0,__source:!0},ma=/\/+/g,C=[],na={current:null},X;if("undefined"===typeof window||"function"!==typeof MessageChannel){var A=null,qa=null,ra=function(){if(null!==A)try{var a=q();A(!0,a);A=null}catch(b){throw setTimeout(ra,0),b;}},La=Date.now();var q=
function(){return Date.now()-La};var z=function(a){null!==A?setTimeout(z,0,a):(A=a,setTimeout(ra,0))};var G=function(a,b){qa=setTimeout(a,b)};var V=function(){clearTimeout(qa)};var W=function(){return!1};f=X=function(){}}else{var Y=window.performance,sa=window.Date,Ma=window.setTimeout,Na=window.clearTimeout;"undefined"!==typeof console&&(f=window.cancelAnimationFrame,"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),
"function"!==typeof f&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"));if("object"===typeof Y&&"function"===typeof Y.now)q=function(){return Y.now()};else{var Oa=sa.now();q=function(){return sa.now()-Oa}}var J=!1,K=null,Z=-1,ta=5,ua=0;W=function(){return q()>=ua};f=function(){};X=function(a){0>a||125<a?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):
ta=0<a?Math.floor(1E3/a):5};var B=new MessageChannel,aa=B.port2;B.port1.onmessage=function(){if(null!==K){var a=q();ua=a+ta;try{K(!0,a)?aa.postMessage(null):(J=!1,K=null)}catch(b){throw aa.postMessage(null),b;}}else J=!1};z=function(a){K=a;J||(J=!0,aa.postMessage(null))};G=function(a,b){Z=Ma(function(){a(q())},b)};V=function(){Na(Z);Z=-1}}var p=[],u=[],Pa=1,l=null,m=3,H=!1,v=!1,y=!1,Qa=0;B={ReactCurrentDispatcher:na,ReactCurrentOwner:M,IsSomeRendererActing:{current:!1},assign:I};I(B,{Scheduler:{__proto__:null,
unstable_ImmediatePriority:1,unstable_UserBlockingPriority:2,unstable_NormalPriority:3,unstable_IdlePriority:5,unstable_LowPriority:4,unstable_runWithPriority:function(a,b){switch(a){case 1:case 2:case 3:case 4:case 5:break;default:a=3}var c=m;m=a;try{return b()}finally{m=c}},unstable_next:function(a){switch(m){case 1:case 2:case 3:var b=3;break;default:b=m}var c=m;m=b;try{return a()}finally{m=c}},unstable_scheduleCallback:function(a,b,c){var d=q();if("object"===typeof c&&null!==c){var e=c.delay;
e="number"===typeof e&&0<e?d+e:d;c="number"===typeof c.timeout?c.timeout:oa(a)}else c=oa(a),e=d;c=e+c;a={id:Pa++,callback:b,priorityLevel:a,startTime:e,expirationTime:c,sortIndex:-1};e>d?(a.sortIndex=e,S(u,a),null===n(p)&&a===n(u)&&(y?V():y=!0,G(T,e-d))):(a.sortIndex=c,S(p,a),v||H||(v=!0,z(U)));return a},unstable_cancelCallback:function(a){a.callback=null},unstable_wrapCallback:function(a){var b=m;return function(){var c=m;m=b;try{return a.apply(this,arguments)}finally{m=c}}},unstable_getCurrentPriorityLevel:function(){return m},
unstable_shouldYield:function(){var a=q();F(a);var b=n(p);return b!==l&&null!==l&&null!==b&&null!==b.callback&&b.startTime<=a&&b.expirationTime<l.expirationTime||W()},unstable_requestPaint:f,unstable_continueExecution:function(){v||H||(v=!0,z(U))},unstable_pauseExecution:function(){},unstable_getFirstCallbackNode:function(){return n(p)},get unstable_now(){return q},get unstable_forceFrameRate(){return X},unstable_Profiling:null},SchedulerTracing:{__proto__:null,__interactionsRef:null,__subscriberRef:null,
unstable_clear:function(a){return a()},unstable_getCurrent:function(){return null},unstable_getThreadID:function(){return++Qa},unstable_trace:function(a,b,c){return c()},unstable_wrap:function(a){return a},unstable_subscribe:function(a){},unstable_unsubscribe:function(a){}}});d.Children={map:function(a,b,c){if(null==a)return a;var d=[];R(a,d,null,b,c);return d},forEach:function(a,b,c){if(null==a)return a;b=ja(null,null,b,c);Q(a,ya,b);ka(b)},count:function(a){return Q(a,function(){return null},null)},
toArray:function(a){var b=[];R(a,b,null,function(a){return a});return b},only:function(a){if(!N(a))throw Error(r(143));return a}};d.Component=w;d.Fragment=Aa;d.Profiler=Ca;d.PureComponent=L;d.StrictMode=Ba;d.Suspense=Ga;d.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B;d.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error(r(267,a));var d=I({},a.props),e=a.key,f=a.ref,m=a._owner;if(null!=b){void 0!==b.ref&&(f=b.ref,m=M.current);void 0!==b.key&&(e=""+b.key);if(a.type&&a.type.defaultProps)var h=
a.type.defaultProps;for(k in b)ha.call(b,k)&&!ia.hasOwnProperty(k)&&(d[k]=void 0===b[k]&&void 0!==h?h[k]:b[k])}var k=arguments.length-2;if(1===k)d.children=c;else if(1<k){h=Array(k);for(var l=0;l<k;l++)h[l]=arguments[l+2];d.children=h}return{$$typeof:x,type:a.type,key:e,ref:f,props:d,_owner:m}};d.createContext=function(a,b){void 0===b&&(b=null);a={$$typeof:Ea,_calculateChangedBits:b,_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null};a.Provider={$$typeof:Da,_context:a};return a.Consumer=
a};d.createElement=ea;d.createFactory=function(a){var b=ea.bind(null,a);b.type=a;return b};d.createRef=function(){return{current:null}};d.forwardRef=function(a){return{$$typeof:Fa,render:a}};d.isValidElement=N;d.lazy=function(a){return{$$typeof:Ia,_ctor:a,_status:-1,_result:null}};d.memo=function(a,b){return{$$typeof:Ha,type:a,compare:void 0===b?null:b}};d.useCallback=function(a,b){return t().useCallback(a,b)};d.useContext=function(a,b){return t().useContext(a,b)};d.useDebugValue=function(a,b){};
d.useEffect=function(a,b){return t().useEffect(a,b)};d.useImperativeHandle=function(a,b,c){return t().useImperativeHandle(a,b,c)};d.useLayoutEffect=function(a,b){return t().useLayoutEffect(a,b)};d.useMemo=function(a,b){return t().useMemo(a,b)};d.useReducer=function(a,b,c){return t().useReducer(a,b,c)};d.useRef=function(a){return t().useRef(a)};d.useState=function(a){return t().useState(a)};d.version="16.13.1"});
</script>
    <script crossorigin>/** @license React v16.13.1
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/*
 Modernizr 3.0.0pre (Custom Build) | MIT
*/
'use strict';(function(I,ea){"object"===typeof exports&&"undefined"!==typeof module?ea(exports,require("react")):"function"===typeof define&&define.amd?define(["exports","react"],ea):(I=I||self,ea(I.ReactDOM={},I.React))})(this,function(I,ea){function k(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function ji(a,b,c,d,e,f,g,h,m){yb=!1;gc=null;ki.apply(li,arguments)}function mi(a,b,c,d,e,f,g,h,m){ji.apply(this,arguments);if(yb){if(yb){var n=gc;yb=!1;gc=null}else throw Error(k(198));hc||(hc=!0,pd=n)}}function lf(a,b,c){var d=a.type||"unknown-event";a.currentTarget=mf(c);mi(d,b,void 0,a);a.currentTarget=null}function nf(){if(ic)for(var a in cb){var b=cb[a],c=ic.indexOf(a);if(!(-1<c))throw Error(k(96,a));if(!jc[c]){if(!b.extractEvents)throw Error(k(97,a));jc[c]=b;c=b.eventTypes;for(var d in c){var e=
void 0;var f=c[d],g=b,h=d;if(qd.hasOwnProperty(h))throw Error(k(99,h));qd[h]=f;var m=f.phasedRegistrationNames;if(m){for(e in m)m.hasOwnProperty(e)&&of(m[e],g,h);e=!0}else f.registrationName?(of(f.registrationName,g,h),e=!0):e=!1;if(!e)throw Error(k(98,d,a));}}}}function of(a,b,c){if(db[a])throw Error(k(100,a));db[a]=b;rd[a]=b.eventTypes[c].dependencies}function pf(a){var b=!1,c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(!cb.hasOwnProperty(c)||cb[c]!==d){if(cb[c])throw Error(k(102,c));cb[c]=
d;b=!0}}b&&nf()}function qf(a){if(a=rf(a)){if("function"!==typeof sd)throw Error(k(280));var b=a.stateNode;b&&(b=td(b),sd(a.stateNode,a.type,b))}}function sf(a){eb?fb?fb.push(a):fb=[a]:eb=a}function tf(){if(eb){var a=eb,b=fb;fb=eb=null;qf(a);if(b)for(a=0;a<b.length;a++)qf(b[a])}}function ud(){if(null!==eb||null!==fb)vd(),tf()}function uf(a,b,c){if(wd)return a(b,c);wd=!0;try{return vf(a,b,c)}finally{wd=!1,ud()}}function ni(a){if(wf.call(xf,a))return!0;if(wf.call(yf,a))return!1;if(oi.test(a))return xf[a]=
!0;yf[a]=!0;return!1}function pi(a,b,c,d){if(null!==c&&0===c.type)return!1;switch(typeof b){case "function":case "symbol":return!0;case "boolean":if(d)return!1;if(null!==c)return!c.acceptsBooleans;a=a.toLowerCase().slice(0,5);return"data-"!==a&&"aria-"!==a;default:return!1}}function qi(a,b,c,d){if(null===b||"undefined"===typeof b||pi(a,b,c,d))return!0;if(d)return!1;if(null!==c)switch(c.type){case 3:return!b;case 4:return!1===b;case 5:return isNaN(b);case 6:return isNaN(b)||1>b}return!1}function L(a,
b,c,d,e,f){this.acceptsBooleans=2===b||3===b||4===b;this.attributeName=d;this.attributeNamespace=e;this.mustUseProperty=c;this.propertyName=a;this.type=b;this.sanitizeURL=f}function xd(a,b,c,d){var e=E.hasOwnProperty(b)?E[b]:null;var f=null!==e?0===e.type:d?!1:!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1]?!1:!0;f||(qi(b,c,e,d)&&(c=null),d||null===e?ni(b)&&(null===c?a.removeAttribute(b):a.setAttribute(b,""+c)):e.mustUseProperty?a[e.propertyName]=null===c?3===e.type?!1:"":c:(b=e.attributeName,
d=e.attributeNamespace,null===c?a.removeAttribute(b):(e=e.type,c=3===e||4===e&&!0===c?"":""+c,d?a.setAttributeNS(d,b,c):a.setAttribute(b,c))))}function zb(a){if(null===a||"object"!==typeof a)return null;a=zf&&a[zf]||a["@@iterator"];return"function"===typeof a?a:null}function ri(a){if(-1===a._status){a._status=0;var b=a._ctor;b=b();a._result=b;b.then(function(b){0===a._status&&(b=b.default,a._status=1,a._result=b)},function(b){0===a._status&&(a._status=2,a._result=b)})}}function na(a){if(null==a)return null;
if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Ma:return"Fragment";case gb:return"Portal";case kc:return"Profiler";case Af:return"StrictMode";case lc:return"Suspense";case yd:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Bf:return"Context.Consumer";case Cf:return"Context.Provider";case zd:var b=a.render;b=b.displayName||b.name||"";return a.displayName||(""!==b?"ForwardRef("+b+")":"ForwardRef");case Ad:return na(a.type);
case Df:return na(a.render);case Ef:if(a=1===a._status?a._result:null)return na(a)}return null}function Bd(a){var b="";do{a:switch(a.tag){case 3:case 4:case 6:case 7:case 10:case 9:var c="";break a;default:var d=a._debugOwner,e=a._debugSource,f=na(a.type);c=null;d&&(c=na(d.type));d=f;f="";e?f=" (at "+e.fileName.replace(si,"")+":"+e.lineNumber+")":c&&(f=" (created by "+c+")");c="\n    in "+(d||"Unknown")+f}b+=c;a=a.return}while(a);return b}function va(a){switch(typeof a){case "boolean":case "number":case "object":case "string":case "undefined":return a;
default:return""}}function Ff(a){var b=a.type;return(a=a.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===b||"radio"===b)}function ti(a){var b=Ff(a)?"checked":"value",c=Object.getOwnPropertyDescriptor(a.constructor.prototype,b),d=""+a[b];if(!a.hasOwnProperty(b)&&"undefined"!==typeof c&&"function"===typeof c.get&&"function"===typeof c.set){var e=c.get,f=c.set;Object.defineProperty(a,b,{configurable:!0,get:function(){return e.call(this)},set:function(a){d=""+a;f.call(this,a)}});Object.defineProperty(a,
b,{enumerable:c.enumerable});return{getValue:function(){return d},setValue:function(a){d=""+a},stopTracking:function(){a._valueTracker=null;delete a[b]}}}}function mc(a){a._valueTracker||(a._valueTracker=ti(a))}function Gf(a){if(!a)return!1;var b=a._valueTracker;if(!b)return!0;var c=b.getValue();var d="";a&&(d=Ff(a)?a.checked?"true":"false":a.value);a=d;return a!==c?(b.setValue(a),!0):!1}function Cd(a,b){var c=b.checked;return M({},b,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=
c?c:a._wrapperState.initialChecked})}function Hf(a,b){var c=null==b.defaultValue?"":b.defaultValue,d=null!=b.checked?b.checked:b.defaultChecked;c=va(null!=b.value?b.value:c);a._wrapperState={initialChecked:d,initialValue:c,controlled:"checkbox"===b.type||"radio"===b.type?null!=b.checked:null!=b.value}}function If(a,b){b=b.checked;null!=b&&xd(a,"checked",b,!1)}function Dd(a,b){If(a,b);var c=va(b.value),d=b.type;if(null!=c)if("number"===d){if(0===c&&""===a.value||a.value!=c)a.value=""+c}else a.value!==
""+c&&(a.value=""+c);else if("submit"===d||"reset"===d){a.removeAttribute("value");return}b.hasOwnProperty("value")?Ed(a,b.type,c):b.hasOwnProperty("defaultValue")&&Ed(a,b.type,va(b.defaultValue));null==b.checked&&null!=b.defaultChecked&&(a.defaultChecked=!!b.defaultChecked)}function Jf(a,b,c){if(b.hasOwnProperty("value")||b.hasOwnProperty("defaultValue")){var d=b.type;if(!("submit"!==d&&"reset"!==d||void 0!==b.value&&null!==b.value))return;b=""+a._wrapperState.initialValue;c||b===a.value||(a.value=
b);a.defaultValue=b}c=a.name;""!==c&&(a.name="");a.defaultChecked=!!a._wrapperState.initialChecked;""!==c&&(a.name=c)}function Ed(a,b,c){if("number"!==b||a.ownerDocument.activeElement!==a)null==c?a.defaultValue=""+a._wrapperState.initialValue:a.defaultValue!==""+c&&(a.defaultValue=""+c)}function ui(a){var b="";ea.Children.forEach(a,function(a){null!=a&&(b+=a)});return b}function Fd(a,b){a=M({children:void 0},b);if(b=ui(b.children))a.children=b;return a}function hb(a,b,c,d){a=a.options;if(b){b={};
for(var e=0;e<c.length;e++)b["$"+c[e]]=!0;for(c=0;c<a.length;c++)e=b.hasOwnProperty("$"+a[c].value),a[c].selected!==e&&(a[c].selected=e),e&&d&&(a[c].defaultSelected=!0)}else{c=""+va(c);b=null;for(e=0;e<a.length;e++){if(a[e].value===c){a[e].selected=!0;d&&(a[e].defaultSelected=!0);return}null!==b||a[e].disabled||(b=a[e])}null!==b&&(b.selected=!0)}}function Gd(a,b){if(null!=b.dangerouslySetInnerHTML)throw Error(k(91));return M({},b,{value:void 0,defaultValue:void 0,children:""+a._wrapperState.initialValue})}
function Kf(a,b){var c=b.value;if(null==c){c=b.children;b=b.defaultValue;if(null!=c){if(null!=b)throw Error(k(92));if(Array.isArray(c)){if(!(1>=c.length))throw Error(k(93));c=c[0]}b=c}null==b&&(b="");c=b}a._wrapperState={initialValue:va(c)}}function Lf(a,b){var c=va(b.value),d=va(b.defaultValue);null!=c&&(c=""+c,c!==a.value&&(a.value=c),null==b.defaultValue&&a.defaultValue!==c&&(a.defaultValue=c));null!=d&&(a.defaultValue=""+d)}function Mf(a,b){b=a.textContent;b===a._wrapperState.initialValue&&""!==
b&&null!==b&&(a.value=b)}function Nf(a){switch(a){case "svg":return"http://www.w3.org/2000/svg";case "math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Hd(a,b){return null==a||"http://www.w3.org/1999/xhtml"===a?Nf(b):"http://www.w3.org/2000/svg"===a&&"foreignObject"===b?"http://www.w3.org/1999/xhtml":a}function nc(a,b){var c={};c[a.toLowerCase()]=b.toLowerCase();c["Webkit"+a]="webkit"+b;c["Moz"+a]="moz"+b;return c}function oc(a){if(Id[a])return Id[a];
if(!ib[a])return a;var b=ib[a],c;for(c in b)if(b.hasOwnProperty(c)&&c in Of)return Id[a]=b[c];return a}function Jd(a){var b=Pf.get(a);void 0===b&&(b=new Map,Pf.set(a,b));return b}function Na(a){var b=a,c=a;if(a.alternate)for(;b.return;)b=b.return;else{a=b;do b=a,0!==(b.effectTag&1026)&&(c=b.return),a=b.return;while(a)}return 3===b.tag?c:null}function Qf(a){if(13===a.tag){var b=a.memoizedState;null===b&&(a=a.alternate,null!==a&&(b=a.memoizedState));if(null!==b)return b.dehydrated}return null}function Rf(a){if(Na(a)!==
a)throw Error(k(188));}function vi(a){var b=a.alternate;if(!b){b=Na(a);if(null===b)throw Error(k(188));return b!==a?null:a}for(var c=a,d=b;;){var e=c.return;if(null===e)break;var f=e.alternate;if(null===f){d=e.return;if(null!==d){c=d;continue}break}if(e.child===f.child){for(f=e.child;f;){if(f===c)return Rf(e),a;if(f===d)return Rf(e),b;f=f.sibling}throw Error(k(188));}if(c.return!==d.return)c=e,d=f;else{for(var g=!1,h=e.child;h;){if(h===c){g=!0;c=e;d=f;break}if(h===d){g=!0;d=e;c=f;break}h=h.sibling}if(!g){for(h=
f.child;h;){if(h===c){g=!0;c=f;d=e;break}if(h===d){g=!0;d=f;c=e;break}h=h.sibling}if(!g)throw Error(k(189));}}if(c.alternate!==d)throw Error(k(190));}if(3!==c.tag)throw Error(k(188));return c.stateNode.current===c?a:b}function Sf(a){a=vi(a);if(!a)return null;for(var b=a;;){if(5===b.tag||6===b.tag)return b;if(b.child)b.child.return=b,b=b.child;else{if(b===a)break;for(;!b.sibling;){if(!b.return||b.return===a)return null;b=b.return}b.sibling.return=b.return;b=b.sibling}}return null}function jb(a,b){if(null==
b)throw Error(k(30));if(null==a)return b;if(Array.isArray(a)){if(Array.isArray(b))return a.push.apply(a,b),a;a.push(b);return a}return Array.isArray(b)?[a].concat(b):[a,b]}function Kd(a,b,c){Array.isArray(a)?a.forEach(b,c):a&&b.call(c,a)}function pc(a){null!==a&&(Ab=jb(Ab,a));a=Ab;Ab=null;if(a){Kd(a,wi);if(Ab)throw Error(k(95));if(hc)throw a=pd,hc=!1,pd=null,a;}}function Ld(a){a=a.target||a.srcElement||window;a.correspondingUseElement&&(a=a.correspondingUseElement);return 3===a.nodeType?a.parentNode:
a}function Tf(a){if(!wa)return!1;a="on"+a;var b=a in document;b||(b=document.createElement("div"),b.setAttribute(a,"return;"),b="function"===typeof b[a]);return b}function Uf(a){a.topLevelType=null;a.nativeEvent=null;a.targetInst=null;a.ancestors.length=0;10>qc.length&&qc.push(a)}function Vf(a,b,c,d){if(qc.length){var e=qc.pop();e.topLevelType=a;e.eventSystemFlags=d;e.nativeEvent=b;e.targetInst=c;return e}return{topLevelType:a,eventSystemFlags:d,nativeEvent:b,targetInst:c,ancestors:[]}}function Wf(a){var b=
a.targetInst,c=b;do{if(!c){a.ancestors.push(c);break}var d=c;if(3===d.tag)d=d.stateNode.containerInfo;else{for(;d.return;)d=d.return;d=3!==d.tag?null:d.stateNode.containerInfo}if(!d)break;b=c.tag;5!==b&&6!==b||a.ancestors.push(c);c=Bb(d)}while(c);for(c=0;c<a.ancestors.length;c++){b=a.ancestors[c];var e=Ld(a.nativeEvent);d=a.topLevelType;var f=a.nativeEvent,g=a.eventSystemFlags;0===c&&(g|=64);for(var h=null,m=0;m<jc.length;m++){var n=jc[m];n&&(n=n.extractEvents(d,b,f,e,g))&&(h=jb(h,n))}pc(h)}}function Md(a,
b,c){if(!c.has(a)){switch(a){case "scroll":Cb(b,"scroll",!0);break;case "focus":case "blur":Cb(b,"focus",!0);Cb(b,"blur",!0);c.set("blur",null);c.set("focus",null);break;case "cancel":case "close":Tf(a)&&Cb(b,a,!0);break;case "invalid":case "submit":case "reset":break;default:-1===Db.indexOf(a)&&w(a,b)}c.set(a,null)}}function xi(a,b){var c=Jd(b);Nd.forEach(function(a){Md(a,b,c)});yi.forEach(function(a){Md(a,b,c)})}function Od(a,b,c,d,e){return{blockedOn:a,topLevelType:b,eventSystemFlags:c|32,nativeEvent:e,
container:d}}function Xf(a,b){switch(a){case "focus":case "blur":xa=null;break;case "dragenter":case "dragleave":ya=null;break;case "mouseover":case "mouseout":za=null;break;case "pointerover":case "pointerout":Eb.delete(b.pointerId);break;case "gotpointercapture":case "lostpointercapture":Fb.delete(b.pointerId)}}function Gb(a,b,c,d,e,f){if(null===a||a.nativeEvent!==f)return a=Od(b,c,d,e,f),null!==b&&(b=Hb(b),null!==b&&Yf(b)),a;a.eventSystemFlags|=d;return a}function zi(a,b,c,d,e){switch(b){case "focus":return xa=
Gb(xa,a,b,c,d,e),!0;case "dragenter":return ya=Gb(ya,a,b,c,d,e),!0;case "mouseover":return za=Gb(za,a,b,c,d,e),!0;case "pointerover":var f=e.pointerId;Eb.set(f,Gb(Eb.get(f)||null,a,b,c,d,e));return!0;case "gotpointercapture":return f=e.pointerId,Fb.set(f,Gb(Fb.get(f)||null,a,b,c,d,e)),!0}return!1}function Ai(a){var b=Bb(a.target);if(null!==b){var c=Na(b);if(null!==c)if(b=c.tag,13===b){if(b=Qf(c),null!==b){a.blockedOn=b;Pd(a.priority,function(){Bi(c)});return}}else if(3===b&&c.stateNode.hydrate){a.blockedOn=
3===c.tag?c.stateNode.containerInfo:null;return}}a.blockedOn=null}function rc(a){if(null!==a.blockedOn)return!1;var b=Qd(a.topLevelType,a.eventSystemFlags,a.container,a.nativeEvent);if(null!==b){var c=Hb(b);null!==c&&Yf(c);a.blockedOn=b;return!1}return!0}function Zf(a,b,c){rc(a)&&c.delete(b)}function Ci(){for(Rd=!1;0<fa.length;){var a=fa[0];if(null!==a.blockedOn){a=Hb(a.blockedOn);null!==a&&Di(a);break}var b=Qd(a.topLevelType,a.eventSystemFlags,a.container,a.nativeEvent);null!==b?a.blockedOn=b:fa.shift()}null!==
xa&&rc(xa)&&(xa=null);null!==ya&&rc(ya)&&(ya=null);null!==za&&rc(za)&&(za=null);Eb.forEach(Zf);Fb.forEach(Zf)}function Ib(a,b){a.blockedOn===b&&(a.blockedOn=null,Rd||(Rd=!0,$f(ag,Ci)))}function bg(a){if(0<fa.length){Ib(fa[0],a);for(var b=1;b<fa.length;b++){var c=fa[b];c.blockedOn===a&&(c.blockedOn=null)}}null!==xa&&Ib(xa,a);null!==ya&&Ib(ya,a);null!==za&&Ib(za,a);b=function(b){return Ib(b,a)};Eb.forEach(b);Fb.forEach(b);for(b=0;b<Jb.length;b++)c=Jb[b],c.blockedOn===a&&(c.blockedOn=null);for(;0<Jb.length&&
(b=Jb[0],null===b.blockedOn);)Ai(b),null===b.blockedOn&&Jb.shift()}function Sd(a,b){for(var c=0;c<a.length;c+=2){var d=a[c],e=a[c+1],f="on"+(e[0].toUpperCase()+e.slice(1));f={phasedRegistrationNames:{bubbled:f,captured:f+"Capture"},dependencies:[d],eventPriority:b};Td.set(d,b);cg.set(d,f);dg[e]=f}}function w(a,b){Cb(b,a,!1)}function Cb(a,b,c){var d=Td.get(b);switch(void 0===d?2:d){case 0:d=Ei.bind(null,b,1,a);break;case 1:d=Fi.bind(null,b,1,a);break;default:d=sc.bind(null,b,1,a)}c?a.addEventListener(b,
d,!0):a.addEventListener(b,d,!1)}function Ei(a,b,c,d){Oa||vd();var e=sc,f=Oa;Oa=!0;try{eg(e,a,b,c,d)}finally{(Oa=f)||ud()}}function Fi(a,b,c,d){Gi(Hi,sc.bind(null,a,b,c,d))}function sc(a,b,c,d){if(tc)if(0<fa.length&&-1<Nd.indexOf(a))a=Od(null,a,b,c,d),fa.push(a);else{var e=Qd(a,b,c,d);if(null===e)Xf(a,d);else if(-1<Nd.indexOf(a))a=Od(e,a,b,c,d),fa.push(a);else if(!zi(e,a,b,c,d)){Xf(a,d);a=Vf(a,d,null,b);try{uf(Wf,a)}finally{Uf(a)}}}}function Qd(a,b,c,d){c=Ld(d);c=Bb(c);if(null!==c){var e=Na(c);if(null===
e)c=null;else{var f=e.tag;if(13===f){c=Qf(e);if(null!==c)return c;c=null}else if(3===f){if(e.stateNode.hydrate)return 3===e.tag?e.stateNode.containerInfo:null;c=null}else e!==c&&(c=null)}}a=Vf(a,d,c,b);try{uf(Wf,a)}finally{Uf(a)}return null}function fg(a,b,c){return null==b||"boolean"===typeof b||""===b?"":c||"number"!==typeof b||0===b||Kb.hasOwnProperty(a)&&Kb[a]?(""+b).trim():b+"px"}function gg(a,b){a=a.style;for(var c in b)if(b.hasOwnProperty(c)){var d=0===c.indexOf("--"),e=fg(c,b[c],d);"float"===
c&&(c="cssFloat");d?a.setProperty(c,e):a[c]=e}}function Ud(a,b){if(b){if(Ii[a]&&(null!=b.children||null!=b.dangerouslySetInnerHTML))throw Error(k(137,a,""));if(null!=b.dangerouslySetInnerHTML){if(null!=b.children)throw Error(k(60));if(!("object"===typeof b.dangerouslySetInnerHTML&&"__html"in b.dangerouslySetInnerHTML))throw Error(k(61));}if(null!=b.style&&"object"!==typeof b.style)throw Error(k(62,""));}}function Vd(a,b){if(-1===a.indexOf("-"))return"string"===typeof b.is;switch(a){case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":return!1;
default:return!0}}function oa(a,b){a=9===a.nodeType||11===a.nodeType?a:a.ownerDocument;var c=Jd(a);b=rd[b];for(var d=0;d<b.length;d++)Md(b[d],a,c)}function uc(){}function Wd(a){a=a||("undefined"!==typeof document?document:void 0);if("undefined"===typeof a)return null;try{return a.activeElement||a.body}catch(b){return a.body}}function hg(a){for(;a&&a.firstChild;)a=a.firstChild;return a}function ig(a,b){var c=hg(a);a=0;for(var d;c;){if(3===c.nodeType){d=a+c.textContent.length;if(a<=b&&d>=b)return{node:c,
offset:b-a};a=d}a:{for(;c;){if(c.nextSibling){c=c.nextSibling;break a}c=c.parentNode}c=void 0}c=hg(c)}}function jg(a,b){return a&&b?a===b?!0:a&&3===a.nodeType?!1:b&&3===b.nodeType?jg(a,b.parentNode):"contains"in a?a.contains(b):a.compareDocumentPosition?!!(a.compareDocumentPosition(b)&16):!1:!1}function kg(){for(var a=window,b=Wd();b instanceof a.HTMLIFrameElement;){try{var c="string"===typeof b.contentWindow.location.href}catch(d){c=!1}if(c)a=b.contentWindow;else break;b=Wd(a.document)}return b}
function Xd(a){var b=a&&a.nodeName&&a.nodeName.toLowerCase();return b&&("input"===b&&("text"===a.type||"search"===a.type||"tel"===a.type||"url"===a.type||"password"===a.type)||"textarea"===b||"true"===a.contentEditable)}function lg(a,b){switch(a){case "button":case "input":case "select":case "textarea":return!!b.autoFocus}return!1}function Yd(a,b){return"textarea"===a||"option"===a||"noscript"===a||"string"===typeof b.children||"number"===typeof b.children||"object"===typeof b.dangerouslySetInnerHTML&&
null!==b.dangerouslySetInnerHTML&&null!=b.dangerouslySetInnerHTML.__html}function kb(a){for(;null!=a;a=a.nextSibling){var b=a.nodeType;if(1===b||3===b)break}return a}function mg(a){a=a.previousSibling;for(var b=0;a;){if(8===a.nodeType){var c=a.data;if(c===ng||c===Zd||c===$d){if(0===b)return a;b--}else c===og&&b++}a=a.previousSibling}return null}function Bb(a){var b=a[Aa];if(b)return b;for(var c=a.parentNode;c;){if(b=c[Lb]||c[Aa]){c=b.alternate;if(null!==b.child||null!==c&&null!==c.child)for(a=mg(a);null!==
a;){if(c=a[Aa])return c;a=mg(a)}return b}a=c;c=a.parentNode}return null}function Hb(a){a=a[Aa]||a[Lb];return!a||5!==a.tag&&6!==a.tag&&13!==a.tag&&3!==a.tag?null:a}function Pa(a){if(5===a.tag||6===a.tag)return a.stateNode;throw Error(k(33));}function ae(a){return a[vc]||null}function pa(a){do a=a.return;while(a&&5!==a.tag);return a?a:null}function pg(a,b){var c=a.stateNode;if(!c)return null;var d=td(c);if(!d)return null;c=d[b];a:switch(b){case "onClick":case "onClickCapture":case "onDoubleClick":case "onDoubleClickCapture":case "onMouseDown":case "onMouseDownCapture":case "onMouseMove":case "onMouseMoveCapture":case "onMouseUp":case "onMouseUpCapture":case "onMouseEnter":(d=
!d.disabled)||(a=a.type,d=!("button"===a||"input"===a||"select"===a||"textarea"===a));a=!d;break a;default:a=!1}if(a)return null;if(c&&"function"!==typeof c)throw Error(k(231,b,typeof c));return c}function qg(a,b,c){if(b=pg(a,c.dispatchConfig.phasedRegistrationNames[b]))c._dispatchListeners=jb(c._dispatchListeners,b),c._dispatchInstances=jb(c._dispatchInstances,a)}function Ji(a){if(a&&a.dispatchConfig.phasedRegistrationNames){for(var b=a._targetInst,c=[];b;)c.push(b),b=pa(b);for(b=c.length;0<b--;)qg(c[b],
"captured",a);for(b=0;b<c.length;b++)qg(c[b],"bubbled",a)}}function be(a,b,c){a&&c&&c.dispatchConfig.registrationName&&(b=pg(a,c.dispatchConfig.registrationName))&&(c._dispatchListeners=jb(c._dispatchListeners,b),c._dispatchInstances=jb(c._dispatchInstances,a))}function Ki(a){a&&a.dispatchConfig.registrationName&&be(a._targetInst,null,a)}function lb(a){Kd(a,Ji)}function rg(){if(wc)return wc;var a,b=ce,c=b.length,d,e="value"in Ba?Ba.value:Ba.textContent,f=e.length;for(a=0;a<c&&b[a]===e[a];a++);var g=
c-a;for(d=1;d<=g&&b[c-d]===e[f-d];d++);return wc=e.slice(a,1<d?1-d:void 0)}function xc(){return!0}function yc(){return!1}function R(a,b,c,d){this.dispatchConfig=a;this._targetInst=b;this.nativeEvent=c;a=this.constructor.Interface;for(var e in a)a.hasOwnProperty(e)&&((b=a[e])?this[e]=b(c):"target"===e?this.target=d:this[e]=c[e]);this.isDefaultPrevented=(null!=c.defaultPrevented?c.defaultPrevented:!1===c.returnValue)?xc:yc;this.isPropagationStopped=yc;return this}function Li(a,b,c,d){if(this.eventPool.length){var e=
this.eventPool.pop();this.call(e,a,b,c,d);return e}return new this(a,b,c,d)}function Mi(a){if(!(a instanceof this))throw Error(k(279));a.destructor();10>this.eventPool.length&&this.eventPool.push(a)}function sg(a){a.eventPool=[];a.getPooled=Li;a.release=Mi}function tg(a,b){switch(a){case "keyup":return-1!==Ni.indexOf(b.keyCode);case "keydown":return 229!==b.keyCode;case "keypress":case "mousedown":case "blur":return!0;default:return!1}}function ug(a){a=a.detail;return"object"===typeof a&&"data"in
a?a.data:null}function Oi(a,b){switch(a){case "compositionend":return ug(b);case "keypress":if(32!==b.which)return null;vg=!0;return wg;case "textInput":return a=b.data,a===wg&&vg?null:a;default:return null}}function Pi(a,b){if(mb)return"compositionend"===a||!de&&tg(a,b)?(a=rg(),wc=ce=Ba=null,mb=!1,a):null;switch(a){case "paste":return null;case "keypress":if(!(b.ctrlKey||b.altKey||b.metaKey)||b.ctrlKey&&b.altKey){if(b.char&&1<b.char.length)return b.char;if(b.which)return String.fromCharCode(b.which)}return null;
case "compositionend":return xg&&"ko"!==b.locale?null:b.data;default:return null}}function yg(a){var b=a&&a.nodeName&&a.nodeName.toLowerCase();return"input"===b?!!Qi[a.type]:"textarea"===b?!0:!1}function zg(a,b,c){a=R.getPooled(Ag.change,a,b,c);a.type="change";sf(c);lb(a);return a}function Ri(a){pc(a)}function zc(a){var b=Pa(a);if(Gf(b))return a}function Si(a,b){if("change"===a)return b}function Bg(){Mb&&(Mb.detachEvent("onpropertychange",Cg),Nb=Mb=null)}function Cg(a){if("value"===a.propertyName&&
zc(Nb))if(a=zg(Nb,a,Ld(a)),Oa)pc(a);else{Oa=!0;try{ee(Ri,a)}finally{Oa=!1,ud()}}}function Ti(a,b,c){"focus"===a?(Bg(),Mb=b,Nb=c,Mb.attachEvent("onpropertychange",Cg)):"blur"===a&&Bg()}function Ui(a,b){if("selectionchange"===a||"keyup"===a||"keydown"===a)return zc(Nb)}function Vi(a,b){if("click"===a)return zc(b)}function Wi(a,b){if("input"===a||"change"===a)return zc(b)}function Xi(a){var b=this.nativeEvent;return b.getModifierState?b.getModifierState(a):(a=Yi[a])?!!b[a]:!1}function fe(a){return Xi}
function Zi(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}function Ob(a,b){if(Qa(a,b))return!0;if("object"!==typeof a||null===a||"object"!==typeof b||null===b)return!1;var c=Object.keys(a),d=Object.keys(b);if(c.length!==d.length)return!1;for(d=0;d<c.length;d++)if(!$i.call(b,c[d])||!Qa(a[c[d]],b[c[d]]))return!1;return!0}function Dg(a,b){var c=b.window===b?b.document:9===b.nodeType?b:b.ownerDocument;if(ge||null==nb||nb!==Wd(c))return null;c=nb;"selectionStart"in c&&Xd(c)?c={start:c.selectionStart,
end:c.selectionEnd}:(c=(c.ownerDocument&&c.ownerDocument.defaultView||window).getSelection(),c={anchorNode:c.anchorNode,anchorOffset:c.anchorOffset,focusNode:c.focusNode,focusOffset:c.focusOffset});return Pb&&Ob(Pb,c)?null:(Pb=c,a=R.getPooled(Eg.select,he,a,b),a.type="select",a.target=nb,lb(a),a)}function Ac(a){var b=a.keyCode;"charCode"in a?(a=a.charCode,0===a&&13===b&&(a=13)):a=b;10===a&&(a=13);return 32<=a||13===a?a:0}function q(a,b){0>ob||(a.current=ie[ob],ie[ob]=null,ob--)}function y(a,b,c){ob++;
ie[ob]=a.current;a.current=b}function pb(a,b){var c=a.type.contextTypes;if(!c)return Ca;var d=a.stateNode;if(d&&d.__reactInternalMemoizedUnmaskedChildContext===b)return d.__reactInternalMemoizedMaskedChildContext;var e={},f;for(f in c)e[f]=b[f];d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=b,a.__reactInternalMemoizedMaskedChildContext=e);return e}function N(a){a=a.childContextTypes;return null!==a&&void 0!==a}function Fg(a,b,c){if(B.current!==Ca)throw Error(k(168));y(B,b);y(G,c)}
function Gg(a,b,c){var d=a.stateNode;a=b.childContextTypes;if("function"!==typeof d.getChildContext)return c;d=d.getChildContext();for(var e in d)if(!(e in a))throw Error(k(108,na(b)||"Unknown",e));return M({},c,{},d)}function Bc(a){a=(a=a.stateNode)&&a.__reactInternalMemoizedMergedChildContext||Ca;Ra=B.current;y(B,a);y(G,G.current);return!0}function Hg(a,b,c){var d=a.stateNode;if(!d)throw Error(k(169));c?(a=Gg(a,b,Ra),d.__reactInternalMemoizedMergedChildContext=a,q(G),q(B),y(B,a)):q(G);y(G,c)}function Cc(){switch(aj()){case Dc:return 99;
case Ig:return 98;case Jg:return 97;case Kg:return 96;case Lg:return 95;default:throw Error(k(332));}}function Mg(a){switch(a){case 99:return Dc;case 98:return Ig;case 97:return Jg;case 96:return Kg;case 95:return Lg;default:throw Error(k(332));}}function Da(a,b){a=Mg(a);return bj(a,b)}function Ng(a,b,c){a=Mg(a);return je(a,b,c)}function Og(a){null===qa?(qa=[a],Ec=je(Dc,Pg)):qa.push(a);return Qg}function ha(){if(null!==Ec){var a=Ec;Ec=null;Rg(a)}Pg()}function Pg(){if(!ke&&null!==qa){ke=!0;var a=0;
try{var b=qa;Da(99,function(){for(;a<b.length;a++){var c=b[a];do c=c(!0);while(null!==c)}});qa=null}catch(c){throw null!==qa&&(qa=qa.slice(a+1)),je(Dc,ha),c;}finally{ke=!1}}}function Fc(a,b,c){c/=10;return 1073741821-(((1073741821-a+b/10)/c|0)+1)*c}function aa(a,b){if(a&&a.defaultProps){b=M({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c])}return b}function le(){Gc=qb=Hc=null}function me(a){var b=Ic.current;q(Ic);a.type._context._currentValue=b}function Sg(a,b){for(;null!==a;){var c=
a.alternate;if(a.childExpirationTime<b)a.childExpirationTime=b,null!==c&&c.childExpirationTime<b&&(c.childExpirationTime=b);else if(null!==c&&c.childExpirationTime<b)c.childExpirationTime=b;else break;a=a.return}}function rb(a,b){Hc=a;Gc=qb=null;a=a.dependencies;null!==a&&null!==a.firstContext&&(a.expirationTime>=b&&(ia=!0),a.firstContext=null)}function W(a,b){if(Gc!==a&&!1!==b&&0!==b){if("number"!==typeof b||**********===b)Gc=a,b=**********;b={context:a,observedBits:b,next:null};if(null===qb){if(null===
Hc)throw Error(k(308));qb=b;Hc.dependencies={expirationTime:0,firstContext:b,responders:null}}else qb=qb.next=b}return a._currentValue}function ne(a){a.updateQueue={baseState:a.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function oe(a,b){a=a.updateQueue;b.updateQueue===a&&(b.updateQueue={baseState:a.baseState,baseQueue:a.baseQueue,shared:a.shared,effects:a.effects})}function Ea(a,b){a={expirationTime:a,suspenseConfig:b,tag:Tg,payload:null,callback:null,next:null};return a.next=
a}function Fa(a,b){a=a.updateQueue;if(null!==a){a=a.shared;var c=a.pending;null===c?b.next=b:(b.next=c.next,c.next=b);a.pending=b}}function Ug(a,b){var c=a.alternate;null!==c&&oe(c,a);a=a.updateQueue;c=a.baseQueue;null===c?(a.baseQueue=b.next=b,b.next=b):(b.next=c.next,c.next=b)}function Qb(a,b,c,d){var e=a.updateQueue;Ga=!1;var f=e.baseQueue,g=e.shared.pending;if(null!==g){if(null!==f){var h=f.next;f.next=g.next;g.next=h}f=g;e.shared.pending=null;h=a.alternate;null!==h&&(h=h.updateQueue,null!==h&&
(h.baseQueue=g))}if(null!==f){h=f.next;var m=e.baseState,n=0,k=null,ba=null,l=null;if(null!==h){var p=h;do{g=p.expirationTime;if(g<d){var t={expirationTime:p.expirationTime,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null};null===l?(ba=l=t,k=m):l=l.next=t;g>n&&(n=g)}else{null!==l&&(l=l.next={expirationTime:**********,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null});Vg(g,p.suspenseConfig);a:{var q=a,r=p;g=b;t=c;switch(r.tag){case 1:q=
r.payload;if("function"===typeof q){m=q.call(t,m,g);break a}m=q;break a;case 3:q.effectTag=q.effectTag&-4097|64;case Tg:q=r.payload;g="function"===typeof q?q.call(t,m,g):q;if(null===g||void 0===g)break a;m=M({},m,g);break a;case Jc:Ga=!0}}null!==p.callback&&(a.effectTag|=32,g=e.effects,null===g?e.effects=[p]:g.push(p))}p=p.next;if(null===p||p===h)if(g=e.shared.pending,null===g)break;else p=f.next=g.next,g.next=h,e.baseQueue=f=g,e.shared.pending=null}while(1)}null===l?k=m:l.next=ba;e.baseState=k;e.baseQueue=
l;Kc(n);a.expirationTime=n;a.memoizedState=m}}function Wg(a,b,c){a=b.effects;b.effects=null;if(null!==a)for(b=0;b<a.length;b++){var d=a[b],e=d.callback;if(null!==e){d.callback=null;d=e;e=c;if("function"!==typeof d)throw Error(k(191,d));d.call(e)}}}function Lc(a,b,c,d){b=a.memoizedState;c=c(d,b);c=null===c||void 0===c?b:M({},b,c);a.memoizedState=c;0===a.expirationTime&&(a.updateQueue.baseState=c)}function Xg(a,b,c,d,e,f,g){a=a.stateNode;return"function"===typeof a.shouldComponentUpdate?a.shouldComponentUpdate(d,
f,g):b.prototype&&b.prototype.isPureReactComponent?!Ob(c,d)||!Ob(e,f):!0}function Yg(a,b,c){var d=!1,e=Ca;var f=b.contextType;"object"===typeof f&&null!==f?f=W(f):(e=N(b)?Ra:B.current,d=b.contextTypes,f=(d=null!==d&&void 0!==d)?pb(a,e):Ca);b=new b(c,f);a.memoizedState=null!==b.state&&void 0!==b.state?b.state:null;b.updater=Mc;a.stateNode=b;b._reactInternalFiber=a;d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=e,a.__reactInternalMemoizedMaskedChildContext=f);return b}function Zg(a,
b,c,d){a=b.state;"function"===typeof b.componentWillReceiveProps&&b.componentWillReceiveProps(c,d);"function"===typeof b.UNSAFE_componentWillReceiveProps&&b.UNSAFE_componentWillReceiveProps(c,d);b.state!==a&&Mc.enqueueReplaceState(b,b.state,null)}function pe(a,b,c,d){var e=a.stateNode;e.props=c;e.state=a.memoizedState;e.refs=$g;ne(a);var f=b.contextType;"object"===typeof f&&null!==f?e.context=W(f):(f=N(b)?Ra:B.current,e.context=pb(a,f));Qb(a,c,e,d);e.state=a.memoizedState;f=b.getDerivedStateFromProps;
"function"===typeof f&&(Lc(a,b,f,c),e.state=a.memoizedState);"function"===typeof b.getDerivedStateFromProps||"function"===typeof e.getSnapshotBeforeUpdate||"function"!==typeof e.UNSAFE_componentWillMount&&"function"!==typeof e.componentWillMount||(b=e.state,"function"===typeof e.componentWillMount&&e.componentWillMount(),"function"===typeof e.UNSAFE_componentWillMount&&e.UNSAFE_componentWillMount(),b!==e.state&&Mc.enqueueReplaceState(e,e.state,null),Qb(a,c,e,d),e.state=a.memoizedState);"function"===
typeof e.componentDidMount&&(a.effectTag|=4)}function Rb(a,b,c){a=c.ref;if(null!==a&&"function"!==typeof a&&"object"!==typeof a){if(c._owner){c=c._owner;if(c){if(1!==c.tag)throw Error(k(309));var d=c.stateNode}if(!d)throw Error(k(147,a));var e=""+a;if(null!==b&&null!==b.ref&&"function"===typeof b.ref&&b.ref._stringRef===e)return b.ref;b=function(a){var b=d.refs;b===$g&&(b=d.refs={});null===a?delete b[e]:b[e]=a};b._stringRef=e;return b}if("string"!==typeof a)throw Error(k(284));if(!c._owner)throw Error(k(290,
a));}return a}function Nc(a,b){if("textarea"!==a.type)throw Error(k(31,"[object Object]"===Object.prototype.toString.call(b)?"object with keys {"+Object.keys(b).join(", ")+"}":b,""));}function ah(a){function b(b,c){if(a){var d=b.lastEffect;null!==d?(d.nextEffect=c,b.lastEffect=c):b.firstEffect=b.lastEffect=c;c.nextEffect=null;c.effectTag=8}}function c(c,d){if(!a)return null;for(;null!==d;)b(c,d),d=d.sibling;return null}function d(a,b){for(a=new Map;null!==b;)null!==b.key?a.set(b.key,b):a.set(b.index,
b),b=b.sibling;return a}function e(a,b){a=Sa(a,b);a.index=0;a.sibling=null;return a}function f(b,c,d){b.index=d;if(!a)return c;d=b.alternate;if(null!==d)return d=d.index,d<c?(b.effectTag=2,c):d;b.effectTag=2;return c}function g(b){a&&null===b.alternate&&(b.effectTag=2);return b}function h(a,b,c,d){if(null===b||6!==b.tag)return b=qe(c,a.mode,d),b.return=a,b;b=e(b,c);b.return=a;return b}function m(a,b,c,d){if(null!==b&&b.elementType===c.type)return d=e(b,c.props),d.ref=Rb(a,b,c),d.return=a,d;d=Oc(c.type,
c.key,c.props,null,a.mode,d);d.ref=Rb(a,b,c);d.return=a;return d}function n(a,b,c,d){if(null===b||4!==b.tag||b.stateNode.containerInfo!==c.containerInfo||b.stateNode.implementation!==c.implementation)return b=re(c,a.mode,d),b.return=a,b;b=e(b,c.children||[]);b.return=a;return b}function l(a,b,c,d,f){if(null===b||7!==b.tag)return b=Ha(c,a.mode,d,f),b.return=a,b;b=e(b,c);b.return=a;return b}function ba(a,b,c){if("string"===typeof b||"number"===typeof b)return b=qe(""+b,a.mode,c),b.return=a,b;if("object"===
typeof b&&null!==b){switch(b.$$typeof){case Pc:return c=Oc(b.type,b.key,b.props,null,a.mode,c),c.ref=Rb(a,null,b),c.return=a,c;case gb:return b=re(b,a.mode,c),b.return=a,b}if(Qc(b)||zb(b))return b=Ha(b,a.mode,c,null),b.return=a,b;Nc(a,b)}return null}function p(a,b,c,d){var e=null!==b?b.key:null;if("string"===typeof c||"number"===typeof c)return null!==e?null:h(a,b,""+c,d);if("object"===typeof c&&null!==c){switch(c.$$typeof){case Pc:return c.key===e?c.type===Ma?l(a,b,c.props.children,d,e):m(a,b,c,
d):null;case gb:return c.key===e?n(a,b,c,d):null}if(Qc(c)||zb(c))return null!==e?null:l(a,b,c,d,null);Nc(a,c)}return null}function t(a,b,c,d,e){if("string"===typeof d||"number"===typeof d)return a=a.get(c)||null,h(b,a,""+d,e);if("object"===typeof d&&null!==d){switch(d.$$typeof){case Pc:return a=a.get(null===d.key?c:d.key)||null,d.type===Ma?l(b,a,d.props.children,e,d.key):m(b,a,d,e);case gb:return a=a.get(null===d.key?c:d.key)||null,n(b,a,d,e)}if(Qc(d)||zb(d))return a=a.get(c)||null,l(b,a,d,e,null);
Nc(b,d)}return null}function q(e,g,h,m){for(var n=null,k=null,l=g,r=g=0,C=null;null!==l&&r<h.length;r++){l.index>r?(C=l,l=null):C=l.sibling;var O=p(e,l,h[r],m);if(null===O){null===l&&(l=C);break}a&&l&&null===O.alternate&&b(e,l);g=f(O,g,r);null===k?n=O:k.sibling=O;k=O;l=C}if(r===h.length)return c(e,l),n;if(null===l){for(;r<h.length;r++)l=ba(e,h[r],m),null!==l&&(g=f(l,g,r),null===k?n=l:k.sibling=l,k=l);return n}for(l=d(e,l);r<h.length;r++)C=t(l,e,r,h[r],m),null!==C&&(a&&null!==C.alternate&&l.delete(null===
C.key?r:C.key),g=f(C,g,r),null===k?n=C:k.sibling=C,k=C);a&&l.forEach(function(a){return b(e,a)});return n}function w(e,g,h,n){var m=zb(h);if("function"!==typeof m)throw Error(k(150));h=m.call(h);if(null==h)throw Error(k(151));for(var l=m=null,r=g,C=g=0,O=null,v=h.next();null!==r&&!v.done;C++,v=h.next()){r.index>C?(O=r,r=null):O=r.sibling;var q=p(e,r,v.value,n);if(null===q){null===r&&(r=O);break}a&&r&&null===q.alternate&&b(e,r);g=f(q,g,C);null===l?m=q:l.sibling=q;l=q;r=O}if(v.done)return c(e,r),m;
if(null===r){for(;!v.done;C++,v=h.next())v=ba(e,v.value,n),null!==v&&(g=f(v,g,C),null===l?m=v:l.sibling=v,l=v);return m}for(r=d(e,r);!v.done;C++,v=h.next())v=t(r,e,C,v.value,n),null!==v&&(a&&null!==v.alternate&&r.delete(null===v.key?C:v.key),g=f(v,g,C),null===l?m=v:l.sibling=v,l=v);a&&r.forEach(function(a){return b(e,a)});return m}return function(a,d,f,h){var m="object"===typeof f&&null!==f&&f.type===Ma&&null===f.key;m&&(f=f.props.children);var n="object"===typeof f&&null!==f;if(n)switch(f.$$typeof){case Pc:a:{n=
f.key;for(m=d;null!==m;){if(m.key===n){switch(m.tag){case 7:if(f.type===Ma){c(a,m.sibling);d=e(m,f.props.children);d.return=a;a=d;break a}break;default:if(m.elementType===f.type){c(a,m.sibling);d=e(m,f.props);d.ref=Rb(a,m,f);d.return=a;a=d;break a}}c(a,m);break}else b(a,m);m=m.sibling}f.type===Ma?(d=Ha(f.props.children,a.mode,h,f.key),d.return=a,a=d):(h=Oc(f.type,f.key,f.props,null,a.mode,h),h.ref=Rb(a,d,f),h.return=a,a=h)}return g(a);case gb:a:{for(m=f.key;null!==d;){if(d.key===m)if(4===d.tag&&d.stateNode.containerInfo===
f.containerInfo&&d.stateNode.implementation===f.implementation){c(a,d.sibling);d=e(d,f.children||[]);d.return=a;a=d;break a}else{c(a,d);break}else b(a,d);d=d.sibling}d=re(f,a.mode,h);d.return=a;a=d}return g(a)}if("string"===typeof f||"number"===typeof f)return f=""+f,null!==d&&6===d.tag?(c(a,d.sibling),d=e(d,f),d.return=a,a=d):(c(a,d),d=qe(f,a.mode,h),d.return=a,a=d),g(a);if(Qc(f))return q(a,d,f,h);if(zb(f))return w(a,d,f,h);n&&Nc(a,f);if("undefined"===typeof f&&!m)switch(a.tag){case 1:case 0:throw a=
a.type,Error(k(152,a.displayName||a.name||"Component"));}return c(a,d)}}function Ta(a){if(a===Sb)throw Error(k(174));return a}function se(a,b){y(Tb,b);y(Ub,a);y(ja,Sb);a=b.nodeType;switch(a){case 9:case 11:b=(b=b.documentElement)?b.namespaceURI:Hd(null,"");break;default:a=8===a?b.parentNode:b,b=a.namespaceURI||null,a=a.tagName,b=Hd(b,a)}q(ja);y(ja,b)}function tb(a){q(ja);q(Ub);q(Tb)}function bh(a){Ta(Tb.current);var b=Ta(ja.current);var c=Hd(b,a.type);b!==c&&(y(Ub,a),y(ja,c))}function te(a){Ub.current===
a&&(q(ja),q(Ub))}function Rc(a){for(var b=a;null!==b;){if(13===b.tag){var c=b.memoizedState;if(null!==c&&(c=c.dehydrated,null===c||c.data===$d||c.data===Zd))return b}else if(19===b.tag&&void 0!==b.memoizedProps.revealOrder){if(0!==(b.effectTag&64))return b}else if(null!==b.child){b.child.return=b;b=b.child;continue}if(b===a)break;for(;null===b.sibling;){if(null===b.return||b.return===a)return null;b=b.return}b.sibling.return=b.return;b=b.sibling}return null}function ue(a,b){return{responder:a,props:b}}
function S(){throw Error(k(321));}function ve(a,b){if(null===b)return!1;for(var c=0;c<b.length&&c<a.length;c++)if(!Qa(a[c],b[c]))return!1;return!0}function we(a,b,c,d,e,f){Ia=f;z=b;b.memoizedState=null;b.updateQueue=null;b.expirationTime=0;Sc.current=null===a||null===a.memoizedState?dj:ej;a=c(d,e);if(b.expirationTime===Ia){f=0;do{b.expirationTime=0;if(!(25>f))throw Error(k(301));f+=1;J=K=null;b.updateQueue=null;Sc.current=fj;a=c(d,e)}while(b.expirationTime===Ia)}Sc.current=Tc;b=null!==K&&null!==K.next;
Ia=0;J=K=z=null;Uc=!1;if(b)throw Error(k(300));return a}function ub(){var a={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};null===J?z.memoizedState=J=a:J=J.next=a;return J}function vb(){if(null===K){var a=z.alternate;a=null!==a?a.memoizedState:null}else a=K.next;var b=null===J?z.memoizedState:J.next;if(null!==b)J=b,K=a;else{if(null===a)throw Error(k(310));K=a;a={memoizedState:K.memoizedState,baseState:K.baseState,baseQueue:K.baseQueue,queue:K.queue,next:null};null===J?z.memoizedState=
J=a:J=J.next=a}return J}function Ua(a,b){return"function"===typeof b?b(a):b}function Vc(a,b,c){b=vb();c=b.queue;if(null===c)throw Error(k(311));c.lastRenderedReducer=a;var d=K,e=d.baseQueue,f=c.pending;if(null!==f){if(null!==e){var g=e.next;e.next=f.next;f.next=g}d.baseQueue=e=f;c.pending=null}if(null!==e){e=e.next;d=d.baseState;var h=g=f=null,m=e;do{var n=m.expirationTime;if(n<Ia){var l={expirationTime:m.expirationTime,suspenseConfig:m.suspenseConfig,action:m.action,eagerReducer:m.eagerReducer,eagerState:m.eagerState,
next:null};null===h?(g=h=l,f=d):h=h.next=l;n>z.expirationTime&&(z.expirationTime=n,Kc(n))}else null!==h&&(h=h.next={expirationTime:**********,suspenseConfig:m.suspenseConfig,action:m.action,eagerReducer:m.eagerReducer,eagerState:m.eagerState,next:null}),Vg(n,m.suspenseConfig),d=m.eagerReducer===a?m.eagerState:a(d,m.action);m=m.next}while(null!==m&&m!==e);null===h?f=d:h.next=g;Qa(d,b.memoizedState)||(ia=!0);b.memoizedState=d;b.baseState=f;b.baseQueue=h;c.lastRenderedState=d}return[b.memoizedState,
c.dispatch]}function Wc(a,b,c){b=vb();c=b.queue;if(null===c)throw Error(k(311));c.lastRenderedReducer=a;var d=c.dispatch,e=c.pending,f=b.memoizedState;if(null!==e){c.pending=null;var g=e=e.next;do f=a(f,g.action),g=g.next;while(g!==e);Qa(f,b.memoizedState)||(ia=!0);b.memoizedState=f;null===b.baseQueue&&(b.baseState=f);c.lastRenderedState=f}return[f,d]}function xe(a){var b=ub();"function"===typeof a&&(a=a());b.memoizedState=b.baseState=a;a=b.queue={pending:null,dispatch:null,lastRenderedReducer:Ua,
lastRenderedState:a};a=a.dispatch=ch.bind(null,z,a);return[b.memoizedState,a]}function ye(a,b,c,d){a={tag:a,create:b,destroy:c,deps:d,next:null};b=z.updateQueue;null===b?(b={lastEffect:null},z.updateQueue=b,b.lastEffect=a.next=a):(c=b.lastEffect,null===c?b.lastEffect=a.next=a:(d=c.next,c.next=a,a.next=d,b.lastEffect=a));return a}function dh(a){return vb().memoizedState}function ze(a,b,c,d){var e=ub();z.effectTag|=a;e.memoizedState=ye(1|b,c,void 0,void 0===d?null:d)}function Ae(a,b,c,d){var e=vb();
d=void 0===d?null:d;var f=void 0;if(null!==K){var g=K.memoizedState;f=g.destroy;if(null!==d&&ve(d,g.deps)){ye(b,c,f,d);return}}z.effectTag|=a;e.memoizedState=ye(1|b,c,f,d)}function eh(a,b){return ze(516,4,a,b)}function Xc(a,b){return Ae(516,4,a,b)}function fh(a,b){return Ae(4,2,a,b)}function gh(a,b){if("function"===typeof b)return a=a(),b(a),function(){b(null)};if(null!==b&&void 0!==b)return a=a(),b.current=a,function(){b.current=null}}function hh(a,b,c){c=null!==c&&void 0!==c?c.concat([a]):null;
return Ae(4,2,gh.bind(null,b,a),c)}function Be(a,b){}function ih(a,b){ub().memoizedState=[a,void 0===b?null:b];return a}function Yc(a,b){var c=vb();b=void 0===b?null:b;var d=c.memoizedState;if(null!==d&&null!==b&&ve(b,d[1]))return d[0];c.memoizedState=[a,b];return a}function jh(a,b){var c=vb();b=void 0===b?null:b;var d=c.memoizedState;if(null!==d&&null!==b&&ve(b,d[1]))return d[0];a=a();c.memoizedState=[a,b];return a}function Ce(a,b,c){var d=Cc();Da(98>d?98:d,function(){a(!0)});Da(97<d?97:d,function(){var d=
X.suspense;X.suspense=void 0===b?null:b;try{a(!1),c()}finally{X.suspense=d}})}function ch(a,b,c){var d=ka(),e=Vb.suspense;d=Va(d,a,e);e={expirationTime:d,suspenseConfig:e,action:c,eagerReducer:null,eagerState:null,next:null};var f=b.pending;null===f?e.next=e:(e.next=f.next,f.next=e);b.pending=e;f=a.alternate;if(a===z||null!==f&&f===z)Uc=!0,e.expirationTime=Ia,z.expirationTime=Ia;else{if(0===a.expirationTime&&(null===f||0===f.expirationTime)&&(f=b.lastRenderedReducer,null!==f))try{var g=b.lastRenderedState,
h=f(g,c);e.eagerReducer=f;e.eagerState=h;if(Qa(h,g))return}catch(m){}finally{}Ja(a,d)}}function kh(a,b){var c=la(5,null,null,0);c.elementType="DELETED";c.type="DELETED";c.stateNode=b;c.return=a;c.effectTag=8;null!==a.lastEffect?(a.lastEffect.nextEffect=c,a.lastEffect=c):a.firstEffect=a.lastEffect=c}function lh(a,b){switch(a.tag){case 5:var c=a.type;b=1!==b.nodeType||c.toLowerCase()!==b.nodeName.toLowerCase()?null:b;return null!==b?(a.stateNode=b,!0):!1;case 6:return b=""===a.pendingProps||3!==b.nodeType?
null:b,null!==b?(a.stateNode=b,!0):!1;case 13:return!1;default:return!1}}function De(a){if(Wa){var b=Ka;if(b){var c=b;if(!lh(a,b)){b=kb(c.nextSibling);if(!b||!lh(a,b)){a.effectTag=a.effectTag&-1025|2;Wa=!1;ra=a;return}kh(ra,c)}ra=a;Ka=kb(b.firstChild)}else a.effectTag=a.effectTag&-1025|2,Wa=!1,ra=a}}function mh(a){for(a=a.return;null!==a&&5!==a.tag&&3!==a.tag&&13!==a.tag;)a=a.return;ra=a}function Zc(a){if(a!==ra)return!1;if(!Wa)return mh(a),Wa=!0,!1;var b=a.type;if(5!==a.tag||"head"!==b&&"body"!==
b&&!Yd(b,a.memoizedProps))for(b=Ka;b;)kh(a,b),b=kb(b.nextSibling);mh(a);if(13===a.tag){a=a.memoizedState;a=null!==a?a.dehydrated:null;if(!a)throw Error(k(317));a:{a=a.nextSibling;for(b=0;a;){if(8===a.nodeType){var c=a.data;if(c===og){if(0===b){Ka=kb(a.nextSibling);break a}b--}else c!==ng&&c!==Zd&&c!==$d||b++}a=a.nextSibling}Ka=null}}else Ka=ra?kb(a.stateNode.nextSibling):null;return!0}function Ee(){Ka=ra=null;Wa=!1}function T(a,b,c,d){b.child=null===a?Fe(b,null,c,d):wb(b,a.child,c,d)}function nh(a,
b,c,d,e){c=c.render;var f=b.ref;rb(b,e);d=we(a,b,c,d,f,e);if(null!==a&&!ia)return b.updateQueue=a.updateQueue,b.effectTag&=-517,a.expirationTime<=e&&(a.expirationTime=0),sa(a,b,e);b.effectTag|=1;T(a,b,d,e);return b.child}function oh(a,b,c,d,e,f){if(null===a){var g=c.type;if("function"===typeof g&&!Ge(g)&&void 0===g.defaultProps&&null===c.compare&&void 0===c.defaultProps)return b.tag=15,b.type=g,ph(a,b,g,d,e,f);a=Oc(c.type,null,d,null,b.mode,f);a.ref=b.ref;a.return=b;return b.child=a}g=a.child;if(e<
f&&(e=g.memoizedProps,c=c.compare,c=null!==c?c:Ob,c(e,d)&&a.ref===b.ref))return sa(a,b,f);b.effectTag|=1;a=Sa(g,d);a.ref=b.ref;a.return=b;return b.child=a}function ph(a,b,c,d,e,f){return null!==a&&Ob(a.memoizedProps,d)&&a.ref===b.ref&&(ia=!1,e<f)?(b.expirationTime=a.expirationTime,sa(a,b,f)):He(a,b,c,d,f)}function qh(a,b){var c=b.ref;if(null===a&&null!==c||null!==a&&a.ref!==c)b.effectTag|=128}function He(a,b,c,d,e){var f=N(c)?Ra:B.current;f=pb(b,f);rb(b,e);c=we(a,b,c,d,f,e);if(null!==a&&!ia)return b.updateQueue=
a.updateQueue,b.effectTag&=-517,a.expirationTime<=e&&(a.expirationTime=0),sa(a,b,e);b.effectTag|=1;T(a,b,c,e);return b.child}function rh(a,b,c,d,e){if(N(c)){var f=!0;Bc(b)}else f=!1;rb(b,e);if(null===b.stateNode)null!==a&&(a.alternate=null,b.alternate=null,b.effectTag|=2),Yg(b,c,d),pe(b,c,d,e),d=!0;else if(null===a){var g=b.stateNode,h=b.memoizedProps;g.props=h;var m=g.context,n=c.contextType;"object"===typeof n&&null!==n?n=W(n):(n=N(c)?Ra:B.current,n=pb(b,n));var l=c.getDerivedStateFromProps,k="function"===
typeof l||"function"===typeof g.getSnapshotBeforeUpdate;k||"function"!==typeof g.UNSAFE_componentWillReceiveProps&&"function"!==typeof g.componentWillReceiveProps||(h!==d||m!==n)&&Zg(b,g,d,n);Ga=!1;var p=b.memoizedState;g.state=p;Qb(b,d,g,e);m=b.memoizedState;h!==d||p!==m||G.current||Ga?("function"===typeof l&&(Lc(b,c,l,d),m=b.memoizedState),(h=Ga||Xg(b,c,h,d,p,m,n))?(k||"function"!==typeof g.UNSAFE_componentWillMount&&"function"!==typeof g.componentWillMount||("function"===typeof g.componentWillMount&&
g.componentWillMount(),"function"===typeof g.UNSAFE_componentWillMount&&g.UNSAFE_componentWillMount()),"function"===typeof g.componentDidMount&&(b.effectTag|=4)):("function"===typeof g.componentDidMount&&(b.effectTag|=4),b.memoizedProps=d,b.memoizedState=m),g.props=d,g.state=m,g.context=n,d=h):("function"===typeof g.componentDidMount&&(b.effectTag|=4),d=!1)}else g=b.stateNode,oe(a,b),h=b.memoizedProps,g.props=b.type===b.elementType?h:aa(b.type,h),m=g.context,n=c.contextType,"object"===typeof n&&null!==
n?n=W(n):(n=N(c)?Ra:B.current,n=pb(b,n)),l=c.getDerivedStateFromProps,(k="function"===typeof l||"function"===typeof g.getSnapshotBeforeUpdate)||"function"!==typeof g.UNSAFE_componentWillReceiveProps&&"function"!==typeof g.componentWillReceiveProps||(h!==d||m!==n)&&Zg(b,g,d,n),Ga=!1,m=b.memoizedState,g.state=m,Qb(b,d,g,e),p=b.memoizedState,h!==d||m!==p||G.current||Ga?("function"===typeof l&&(Lc(b,c,l,d),p=b.memoizedState),(l=Ga||Xg(b,c,h,d,m,p,n))?(k||"function"!==typeof g.UNSAFE_componentWillUpdate&&
"function"!==typeof g.componentWillUpdate||("function"===typeof g.componentWillUpdate&&g.componentWillUpdate(d,p,n),"function"===typeof g.UNSAFE_componentWillUpdate&&g.UNSAFE_componentWillUpdate(d,p,n)),"function"===typeof g.componentDidUpdate&&(b.effectTag|=4),"function"===typeof g.getSnapshotBeforeUpdate&&(b.effectTag|=256)):("function"!==typeof g.componentDidUpdate||h===a.memoizedProps&&m===a.memoizedState||(b.effectTag|=4),"function"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&m===
a.memoizedState||(b.effectTag|=256),b.memoizedProps=d,b.memoizedState=p),g.props=d,g.state=p,g.context=n,d=l):("function"!==typeof g.componentDidUpdate||h===a.memoizedProps&&m===a.memoizedState||(b.effectTag|=4),"function"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&m===a.memoizedState||(b.effectTag|=256),d=!1);return Ie(a,b,c,d,f,e)}function Ie(a,b,c,d,e,f){qh(a,b);var g=0!==(b.effectTag&64);if(!d&&!g)return e&&Hg(b,c,!1),sa(a,b,f);d=b.stateNode;gj.current=b;var h=g&&"function"!==typeof c.getDerivedStateFromError?
null:d.render();b.effectTag|=1;null!==a&&g?(b.child=wb(b,a.child,null,f),b.child=wb(b,null,h,f)):T(a,b,h,f);b.memoizedState=d.state;e&&Hg(b,c,!0);return b.child}function sh(a){var b=a.stateNode;b.pendingContext?Fg(a,b.pendingContext,b.pendingContext!==b.context):b.context&&Fg(a,b.context,!1);se(a,b.containerInfo)}function th(a,b,c){var d=b.mode,e=b.pendingProps,f=D.current,g=!1,h;(h=0!==(b.effectTag&64))||(h=0!==(f&2)&&(null===a||null!==a.memoizedState));h?(g=!0,b.effectTag&=-65):null!==a&&null===
a.memoizedState||void 0===e.fallback||!0===e.unstable_avoidThisFallback||(f|=1);y(D,f&1);if(null===a){void 0!==e.fallback&&De(b);if(g){g=e.fallback;e=Ha(null,d,0,null);e.return=b;if(0===(b.mode&2))for(a=null!==b.memoizedState?b.child.child:b.child,e.child=a;null!==a;)a.return=e,a=a.sibling;c=Ha(g,d,c,null);c.return=b;e.sibling=c;b.memoizedState=Je;b.child=e;return c}d=e.children;b.memoizedState=null;return b.child=Fe(b,null,d,c)}if(null!==a.memoizedState){a=a.child;d=a.sibling;if(g){e=e.fallback;
c=Sa(a,a.pendingProps);c.return=b;if(0===(b.mode&2)&&(g=null!==b.memoizedState?b.child.child:b.child,g!==a.child))for(c.child=g;null!==g;)g.return=c,g=g.sibling;d=Sa(d,e);d.return=b;c.sibling=d;c.childExpirationTime=0;b.memoizedState=Je;b.child=c;return d}c=wb(b,a.child,e.children,c);b.memoizedState=null;return b.child=c}a=a.child;if(g){g=e.fallback;e=Ha(null,d,0,null);e.return=b;e.child=a;null!==a&&(a.return=e);if(0===(b.mode&2))for(a=null!==b.memoizedState?b.child.child:b.child,e.child=a;null!==
a;)a.return=e,a=a.sibling;c=Ha(g,d,c,null);c.return=b;e.sibling=c;c.effectTag|=2;e.childExpirationTime=0;b.memoizedState=Je;b.child=e;return c}b.memoizedState=null;return b.child=wb(b,a,e.children,c)}function uh(a,b){a.expirationTime<b&&(a.expirationTime=b);var c=a.alternate;null!==c&&c.expirationTime<b&&(c.expirationTime=b);Sg(a.return,b)}function Ke(a,b,c,d,e,f){var g=a.memoizedState;null===g?a.memoizedState={isBackwards:b,rendering:null,renderingStartTime:0,last:d,tail:c,tailExpiration:0,tailMode:e,
lastEffect:f}:(g.isBackwards=b,g.rendering=null,g.renderingStartTime=0,g.last=d,g.tail=c,g.tailExpiration=0,g.tailMode=e,g.lastEffect=f)}function vh(a,b,c){var d=b.pendingProps,e=d.revealOrder,f=d.tail;T(a,b,d.children,c);d=D.current;if(0!==(d&2))d=d&1|2,b.effectTag|=64;else{if(null!==a&&0!==(a.effectTag&64))a:for(a=b.child;null!==a;){if(13===a.tag)null!==a.memoizedState&&uh(a,c);else if(19===a.tag)uh(a,c);else if(null!==a.child){a.child.return=a;a=a.child;continue}if(a===b)break a;for(;null===a.sibling;){if(null===
a.return||a.return===b)break a;a=a.return}a.sibling.return=a.return;a=a.sibling}d&=1}y(D,d);if(0===(b.mode&2))b.memoizedState=null;else switch(e){case "forwards":c=b.child;for(e=null;null!==c;)a=c.alternate,null!==a&&null===Rc(a)&&(e=c),c=c.sibling;c=e;null===c?(e=b.child,b.child=null):(e=c.sibling,c.sibling=null);Ke(b,!1,e,c,f,b.lastEffect);break;case "backwards":c=null;e=b.child;for(b.child=null;null!==e;){a=e.alternate;if(null!==a&&null===Rc(a)){b.child=e;break}a=e.sibling;e.sibling=c;c=e;e=a}Ke(b,
!0,c,null,f,b.lastEffect);break;case "together":Ke(b,!1,null,null,void 0,b.lastEffect);break;default:b.memoizedState=null}return b.child}function sa(a,b,c){null!==a&&(b.dependencies=a.dependencies);var d=b.expirationTime;0!==d&&Kc(d);if(b.childExpirationTime<c)return null;if(null!==a&&b.child!==a.child)throw Error(k(153));if(null!==b.child){a=b.child;c=Sa(a,a.pendingProps);b.child=c;for(c.return=b;null!==a.sibling;)a=a.sibling,c=c.sibling=Sa(a,a.pendingProps),c.return=b;c.sibling=null}return b.child}
function $c(a,b){switch(a.tailMode){case "hidden":b=a.tail;for(var c=null;null!==b;)null!==b.alternate&&(c=b),b=b.sibling;null===c?a.tail=null:c.sibling=null;break;case "collapsed":c=a.tail;for(var d=null;null!==c;)null!==c.alternate&&(d=c),c=c.sibling;null===d?b||null===a.tail?a.tail=null:a.tail.sibling=null:d.sibling=null}}function hj(a,b,c){var d=b.pendingProps;switch(b.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return N(b.type)&&(q(G),q(B)),
null;case 3:return tb(),q(G),q(B),c=b.stateNode,c.pendingContext&&(c.context=c.pendingContext,c.pendingContext=null),null!==a&&null!==a.child||!Zc(b)||(b.effectTag|=4),wh(b),null;case 5:te(b);c=Ta(Tb.current);var e=b.type;if(null!==a&&null!=b.stateNode)ij(a,b,e,d,c),a.ref!==b.ref&&(b.effectTag|=128);else{if(!d){if(null===b.stateNode)throw Error(k(166));return null}a=Ta(ja.current);if(Zc(b)){d=b.stateNode;e=b.type;var f=b.memoizedProps;d[Aa]=b;d[vc]=f;switch(e){case "iframe":case "object":case "embed":w("load",
d);break;case "video":case "audio":for(a=0;a<Db.length;a++)w(Db[a],d);break;case "source":w("error",d);break;case "img":case "image":case "link":w("error",d);w("load",d);break;case "form":w("reset",d);w("submit",d);break;case "details":w("toggle",d);break;case "input":Hf(d,f);w("invalid",d);oa(c,"onChange");break;case "select":d._wrapperState={wasMultiple:!!f.multiple};w("invalid",d);oa(c,"onChange");break;case "textarea":Kf(d,f),w("invalid",d),oa(c,"onChange")}Ud(e,f);a=null;for(var g in f)if(f.hasOwnProperty(g)){var h=
f[g];"children"===g?"string"===typeof h?d.textContent!==h&&(a=["children",h]):"number"===typeof h&&d.textContent!==""+h&&(a=["children",""+h]):db.hasOwnProperty(g)&&null!=h&&oa(c,g)}switch(e){case "input":mc(d);Jf(d,f,!0);break;case "textarea":mc(d);Mf(d);break;case "select":case "option":break;default:"function"===typeof f.onClick&&(d.onclick=uc)}c=a;b.updateQueue=c;null!==c&&(b.effectTag|=4)}else{g=9===c.nodeType?c:c.ownerDocument;"http://www.w3.org/1999/xhtml"===a&&(a=Nf(e));"http://www.w3.org/1999/xhtml"===
a?"script"===e?(a=g.createElement("div"),a.innerHTML="<script>\x3c/script>",a=a.removeChild(a.firstChild)):"string"===typeof d.is?a=g.createElement(e,{is:d.is}):(a=g.createElement(e),"select"===e&&(g=a,d.multiple?g.multiple=!0:d.size&&(g.size=d.size))):a=g.createElementNS(a,e);a[Aa]=b;a[vc]=d;jj(a,b,!1,!1);b.stateNode=a;g=Vd(e,d);switch(e){case "iframe":case "object":case "embed":w("load",a);h=d;break;case "video":case "audio":for(h=0;h<Db.length;h++)w(Db[h],a);h=d;break;case "source":w("error",a);
h=d;break;case "img":case "image":case "link":w("error",a);w("load",a);h=d;break;case "form":w("reset",a);w("submit",a);h=d;break;case "details":w("toggle",a);h=d;break;case "input":Hf(a,d);h=Cd(a,d);w("invalid",a);oa(c,"onChange");break;case "option":h=Fd(a,d);break;case "select":a._wrapperState={wasMultiple:!!d.multiple};h=M({},d,{value:void 0});w("invalid",a);oa(c,"onChange");break;case "textarea":Kf(a,d);h=Gd(a,d);w("invalid",a);oa(c,"onChange");break;default:h=d}Ud(e,h);var m=h;for(f in m)if(m.hasOwnProperty(f)){var n=
m[f];"style"===f?gg(a,n):"dangerouslySetInnerHTML"===f?(n=n?n.__html:void 0,null!=n&&xh(a,n)):"children"===f?"string"===typeof n?("textarea"!==e||""!==n)&&Wb(a,n):"number"===typeof n&&Wb(a,""+n):"suppressContentEditableWarning"!==f&&"suppressHydrationWarning"!==f&&"autoFocus"!==f&&(db.hasOwnProperty(f)?null!=n&&oa(c,f):null!=n&&xd(a,f,n,g))}switch(e){case "input":mc(a);Jf(a,d,!1);break;case "textarea":mc(a);Mf(a);break;case "option":null!=d.value&&a.setAttribute("value",""+va(d.value));break;case "select":a.multiple=
!!d.multiple;c=d.value;null!=c?hb(a,!!d.multiple,c,!1):null!=d.defaultValue&&hb(a,!!d.multiple,d.defaultValue,!0);break;default:"function"===typeof h.onClick&&(a.onclick=uc)}lg(e,d)&&(b.effectTag|=4)}null!==b.ref&&(b.effectTag|=128)}return null;case 6:if(a&&null!=b.stateNode)kj(a,b,a.memoizedProps,d);else{if("string"!==typeof d&&null===b.stateNode)throw Error(k(166));c=Ta(Tb.current);Ta(ja.current);Zc(b)?(c=b.stateNode,d=b.memoizedProps,c[Aa]=b,c.nodeValue!==d&&(b.effectTag|=4)):(c=(9===c.nodeType?
c:c.ownerDocument).createTextNode(d),c[Aa]=b,b.stateNode=c)}return null;case 13:q(D);d=b.memoizedState;if(0!==(b.effectTag&64))return b.expirationTime=c,b;c=null!==d;d=!1;null===a?void 0!==b.memoizedProps.fallback&&Zc(b):(e=a.memoizedState,d=null!==e,c||null===e||(e=a.child.sibling,null!==e&&(f=b.firstEffect,null!==f?(b.firstEffect=e,e.nextEffect=f):(b.firstEffect=b.lastEffect=e,e.nextEffect=null),e.effectTag=8)));if(c&&!d&&0!==(b.mode&2))if(null===a&&!0!==b.memoizedProps.unstable_avoidThisFallback||
0!==(D.current&1))F===Xa&&(F=ad);else{if(F===Xa||F===ad)F=bd;0!==Xb&&null!==U&&(Ya(U,P),yh(U,Xb))}if(c||d)b.effectTag|=4;return null;case 4:return tb(),wh(b),null;case 10:return me(b),null;case 17:return N(b.type)&&(q(G),q(B)),null;case 19:q(D);d=b.memoizedState;if(null===d)return null;e=0!==(b.effectTag&64);f=d.rendering;if(null===f)if(e)$c(d,!1);else{if(F!==Xa||null!==a&&0!==(a.effectTag&64))for(f=b.child;null!==f;){a=Rc(f);if(null!==a){b.effectTag|=64;$c(d,!1);e=a.updateQueue;null!==e&&(b.updateQueue=
e,b.effectTag|=4);null===d.lastEffect&&(b.firstEffect=null);b.lastEffect=d.lastEffect;for(d=b.child;null!==d;)e=d,f=c,e.effectTag&=2,e.nextEffect=null,e.firstEffect=null,e.lastEffect=null,a=e.alternate,null===a?(e.childExpirationTime=0,e.expirationTime=f,e.child=null,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null):(e.childExpirationTime=a.childExpirationTime,e.expirationTime=a.expirationTime,e.child=a.child,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,
e.updateQueue=a.updateQueue,f=a.dependencies,e.dependencies=null===f?null:{expirationTime:f.expirationTime,firstContext:f.firstContext,responders:f.responders}),d=d.sibling;y(D,D.current&1|2);return b.child}f=f.sibling}}else{if(!e)if(a=Rc(f),null!==a){if(b.effectTag|=64,e=!0,c=a.updateQueue,null!==c&&(b.updateQueue=c,b.effectTag|=4),$c(d,!0),null===d.tail&&"hidden"===d.tailMode&&!f.alternate)return b=b.lastEffect=d.lastEffect,null!==b&&(b.nextEffect=null),null}else 2*Y()-d.renderingStartTime>d.tailExpiration&&
1<c&&(b.effectTag|=64,e=!0,$c(d,!1),b.expirationTime=b.childExpirationTime=c-1);d.isBackwards?(f.sibling=b.child,b.child=f):(c=d.last,null!==c?c.sibling=f:b.child=f,d.last=f)}return null!==d.tail?(0===d.tailExpiration&&(d.tailExpiration=Y()+500),c=d.tail,d.rendering=c,d.tail=c.sibling,d.lastEffect=b.lastEffect,d.renderingStartTime=Y(),c.sibling=null,b=D.current,y(D,e?b&1|2:b&1),c):null}throw Error(k(156,b.tag));}function lj(a,b){switch(a.tag){case 1:return N(a.type)&&(q(G),q(B)),b=a.effectTag,b&4096?
(a.effectTag=b&-4097|64,a):null;case 3:tb();q(G);q(B);b=a.effectTag;if(0!==(b&64))throw Error(k(285));a.effectTag=b&-4097|64;return a;case 5:return te(a),null;case 13:return q(D),b=a.effectTag,b&4096?(a.effectTag=b&-4097|64,a):null;case 19:return q(D),null;case 4:return tb(),null;case 10:return me(a),null;default:return null}}function Le(a,b){return{value:a,source:b,stack:Bd(b)}}function Me(a,b){var c=b.source,d=b.stack;null===d&&null!==c&&(d=Bd(c));null!==c&&na(c.type);b=b.value;null!==a&&1===a.tag&&
na(a.type);try{console.error(b)}catch(e){setTimeout(function(){throw e;})}}function mj(a,b){try{b.props=a.memoizedProps,b.state=a.memoizedState,b.componentWillUnmount()}catch(c){Za(a,c)}}function zh(a){var b=a.ref;if(null!==b)if("function"===typeof b)try{b(null)}catch(c){Za(a,c)}else b.current=null}function nj(a,b){switch(b.tag){case 0:case 11:case 15:case 22:return;case 1:if(b.effectTag&256&&null!==a){var c=a.memoizedProps,d=a.memoizedState;a=b.stateNode;b=a.getSnapshotBeforeUpdate(b.elementType===
b.type?c:aa(b.type,c),d);a.__reactInternalSnapshotBeforeUpdate=b}return;case 3:case 5:case 6:case 4:case 17:return}throw Error(k(163));}function Ah(a,b){b=b.updateQueue;b=null!==b?b.lastEffect:null;if(null!==b){var c=b=b.next;do{if((c.tag&a)===a){var d=c.destroy;c.destroy=void 0;void 0!==d&&d()}c=c.next}while(c!==b)}}function Bh(a,b){b=b.updateQueue;b=null!==b?b.lastEffect:null;if(null!==b){var c=b=b.next;do{if((c.tag&a)===a){var d=c.create;c.destroy=d()}c=c.next}while(c!==b)}}function oj(a,b,c,d){switch(c.tag){case 0:case 11:case 15:case 22:Bh(3,
c);return;case 1:a=c.stateNode;c.effectTag&4&&(null===b?a.componentDidMount():(d=c.elementType===c.type?b.memoizedProps:aa(c.type,b.memoizedProps),a.componentDidUpdate(d,b.memoizedState,a.__reactInternalSnapshotBeforeUpdate)));b=c.updateQueue;null!==b&&Wg(c,b,a);return;case 3:b=c.updateQueue;if(null!==b){a=null;if(null!==c.child)switch(c.child.tag){case 5:a=c.child.stateNode;break;case 1:a=c.child.stateNode}Wg(c,b,a)}return;case 5:a=c.stateNode;null===b&&c.effectTag&4&&lg(c.type,c.memoizedProps)&&
a.focus();return;case 6:return;case 4:return;case 12:return;case 13:null===c.memoizedState&&(c=c.alternate,null!==c&&(c=c.memoizedState,null!==c&&(c=c.dehydrated,null!==c&&bg(c))));return;case 19:case 17:case 20:case 21:return}throw Error(k(163));}function Ch(a,b,c){"function"===typeof Ne&&Ne(b);switch(b.tag){case 0:case 11:case 14:case 15:case 22:a=b.updateQueue;if(null!==a&&(a=a.lastEffect,null!==a)){var d=a.next;Da(97<c?97:c,function(){var a=d;do{var c=a.destroy;if(void 0!==c){var g=b;try{c()}catch(h){Za(g,
h)}}a=a.next}while(a!==d)})}break;case 1:zh(b);c=b.stateNode;"function"===typeof c.componentWillUnmount&&mj(b,c);break;case 5:zh(b);break;case 4:Dh(a,b,c)}}function Eh(a){var b=a.alternate;a.return=null;a.child=null;a.memoizedState=null;a.updateQueue=null;a.dependencies=null;a.alternate=null;a.firstEffect=null;a.lastEffect=null;a.pendingProps=null;a.memoizedProps=null;a.stateNode=null;null!==b&&Eh(b)}function Fh(a){return 5===a.tag||3===a.tag||4===a.tag}function Gh(a){a:{for(var b=a.return;null!==
b;){if(Fh(b)){var c=b;break a}b=b.return}throw Error(k(160));}b=c.stateNode;switch(c.tag){case 5:var d=!1;break;case 3:b=b.containerInfo;d=!0;break;case 4:b=b.containerInfo;d=!0;break;default:throw Error(k(161));}c.effectTag&16&&(Wb(b,""),c.effectTag&=-17);a:b:for(c=a;;){for(;null===c.sibling;){if(null===c.return||Fh(c.return)){c=null;break a}c=c.return}c.sibling.return=c.return;for(c=c.sibling;5!==c.tag&&6!==c.tag&&18!==c.tag;){if(c.effectTag&2)continue b;if(null===c.child||4===c.tag)continue b;
else c.child.return=c,c=c.child}if(!(c.effectTag&2)){c=c.stateNode;break a}}d?Oe(a,c,b):Pe(a,c,b)}function Oe(a,b,c){var d=a.tag,e=5===d||6===d;if(e)a=e?a.stateNode:a.stateNode.instance,b?8===c.nodeType?c.parentNode.insertBefore(a,b):c.insertBefore(a,b):(8===c.nodeType?(b=c.parentNode,b.insertBefore(a,c)):(b=c,b.appendChild(a)),c=c._reactRootContainer,null!==c&&void 0!==c||null!==b.onclick||(b.onclick=uc));else if(4!==d&&(a=a.child,null!==a))for(Oe(a,b,c),a=a.sibling;null!==a;)Oe(a,b,c),a=a.sibling}
function Pe(a,b,c){var d=a.tag,e=5===d||6===d;if(e)a=e?a.stateNode:a.stateNode.instance,b?c.insertBefore(a,b):c.appendChild(a);else if(4!==d&&(a=a.child,null!==a))for(Pe(a,b,c),a=a.sibling;null!==a;)Pe(a,b,c),a=a.sibling}function Dh(a,b,c){for(var d=b,e=!1,f,g;;){if(!e){e=d.return;a:for(;;){if(null===e)throw Error(k(160));f=e.stateNode;switch(e.tag){case 5:g=!1;break a;case 3:f=f.containerInfo;g=!0;break a;case 4:f=f.containerInfo;g=!0;break a}e=e.return}e=!0}if(5===d.tag||6===d.tag){a:for(var h=
a,m=d,n=c,l=m;;)if(Ch(h,l,n),null!==l.child&&4!==l.tag)l.child.return=l,l=l.child;else{if(l===m)break a;for(;null===l.sibling;){if(null===l.return||l.return===m)break a;l=l.return}l.sibling.return=l.return;l=l.sibling}g?(h=f,m=d.stateNode,8===h.nodeType?h.parentNode.removeChild(m):h.removeChild(m)):f.removeChild(d.stateNode)}else if(4===d.tag){if(null!==d.child){f=d.stateNode.containerInfo;g=!0;d.child.return=d;d=d.child;continue}}else if(Ch(a,d,c),null!==d.child){d.child.return=d;d=d.child;continue}if(d===
b)break;for(;null===d.sibling;){if(null===d.return||d.return===b)return;d=d.return;4===d.tag&&(e=!1)}d.sibling.return=d.return;d=d.sibling}}function Qe(a,b){switch(b.tag){case 0:case 11:case 14:case 15:case 22:Ah(3,b);return;case 1:return;case 5:var c=b.stateNode;if(null!=c){var d=b.memoizedProps,e=null!==a?a.memoizedProps:d;a=b.type;var f=b.updateQueue;b.updateQueue=null;if(null!==f){c[vc]=d;"input"===a&&"radio"===d.type&&null!=d.name&&If(c,d);Vd(a,e);b=Vd(a,d);for(e=0;e<f.length;e+=2){var g=f[e],
h=f[e+1];"style"===g?gg(c,h):"dangerouslySetInnerHTML"===g?xh(c,h):"children"===g?Wb(c,h):xd(c,g,h,b)}switch(a){case "input":Dd(c,d);break;case "textarea":Lf(c,d);break;case "select":b=c._wrapperState.wasMultiple,c._wrapperState.wasMultiple=!!d.multiple,a=d.value,null!=a?hb(c,!!d.multiple,a,!1):b!==!!d.multiple&&(null!=d.defaultValue?hb(c,!!d.multiple,d.defaultValue,!0):hb(c,!!d.multiple,d.multiple?[]:"",!1))}}}return;case 6:if(null===b.stateNode)throw Error(k(162));b.stateNode.nodeValue=b.memoizedProps;
return;case 3:b=b.stateNode;b.hydrate&&(b.hydrate=!1,bg(b.containerInfo));return;case 12:return;case 13:c=b;null===b.memoizedState?d=!1:(d=!0,c=b.child,Re=Y());if(null!==c)a:for(a=c;;){if(5===a.tag)f=a.stateNode,d?(f=f.style,"function"===typeof f.setProperty?f.setProperty("display","none","important"):f.display="none"):(f=a.stateNode,e=a.memoizedProps.style,e=void 0!==e&&null!==e&&e.hasOwnProperty("display")?e.display:null,f.style.display=fg("display",e));else if(6===a.tag)a.stateNode.nodeValue=d?
"":a.memoizedProps;else if(13===a.tag&&null!==a.memoizedState&&null===a.memoizedState.dehydrated){f=a.child.sibling;f.return=a;a=f;continue}else if(null!==a.child){a.child.return=a;a=a.child;continue}if(a===c)break;for(;null===a.sibling;){if(null===a.return||a.return===c)break a;a=a.return}a.sibling.return=a.return;a=a.sibling}Hh(b);return;case 19:Hh(b);return;case 17:return}throw Error(k(163));}function Hh(a){var b=a.updateQueue;if(null!==b){a.updateQueue=null;var c=a.stateNode;null===c&&(c=a.stateNode=
new pj);b.forEach(function(b){var d=qj.bind(null,a,b);c.has(b)||(c.add(b),b.then(d,d))})}}function Ih(a,b,c){c=Ea(c,null);c.tag=3;c.payload={element:null};var d=b.value;c.callback=function(){cd||(cd=!0,Se=d);Me(a,b)};return c}function Jh(a,b,c){c=Ea(c,null);c.tag=3;var d=a.type.getDerivedStateFromError;if("function"===typeof d){var e=b.value;c.payload=function(){Me(a,b);return d(e)}}var f=a.stateNode;null!==f&&"function"===typeof f.componentDidCatch&&(c.callback=function(){"function"!==typeof d&&
(null===La?La=new Set([this]):La.add(this),Me(a,b));var c=b.stack;this.componentDidCatch(b.value,{componentStack:null!==c?c:""})});return c}function ka(){return(p&(ca|ma))!==H?1073741821-(Y()/10|0):0!==dd?dd:dd=1073741821-(Y()/10|0)}function Va(a,b,c){b=b.mode;if(0===(b&2))return **********;var d=Cc();if(0===(b&4))return 99===d?**********:1073741822;if((p&ca)!==H)return P;if(null!==c)a=Fc(a,c.timeoutMs|0||5E3,250);else switch(d){case 99:a=**********;break;case 98:a=Fc(a,150,100);break;case 97:case 96:a=
Fc(a,5E3,250);break;case 95:a=2;break;default:throw Error(k(326));}null!==U&&a===P&&--a;return a}function ed(a,b){a.expirationTime<b&&(a.expirationTime=b);var c=a.alternate;null!==c&&c.expirationTime<b&&(c.expirationTime=b);var d=a.return,e=null;if(null===d&&3===a.tag)e=a.stateNode;else for(;null!==d;){c=d.alternate;d.childExpirationTime<b&&(d.childExpirationTime=b);null!==c&&c.childExpirationTime<b&&(c.childExpirationTime=b);if(null===d.return&&3===d.tag){e=d.stateNode;break}d=d.return}null!==e&&
(U===e&&(Kc(b),F===bd&&Ya(e,P)),yh(e,b));return e}function fd(a){var b=a.lastExpiredTime;if(0!==b)return b;b=a.firstPendingTime;if(!Kh(a,b))return b;var c=a.lastPingedTime;a=a.nextKnownPendingLevel;a=c>a?c:a;return 2>=a&&b!==a?0:a}function V(a){if(0!==a.lastExpiredTime)a.callbackExpirationTime=**********,a.callbackPriority=99,a.callbackNode=Og(Te.bind(null,a));else{var b=fd(a),c=a.callbackNode;if(0===b)null!==c&&(a.callbackNode=null,a.callbackExpirationTime=0,a.callbackPriority=90);else{var d=ka();
**********===b?d=99:1===b||2===b?d=95:(d=10*(1073741821-b)-10*(1073741821-d),d=0>=d?99:250>=d?98:5250>=d?97:95);if(null!==c){var e=a.callbackPriority;if(a.callbackExpirationTime===b&&e>=d)return;c!==Qg&&Rg(c)}a.callbackExpirationTime=b;a.callbackPriority=d;b=**********===b?Og(Te.bind(null,a)):Ng(d,Lh.bind(null,a),{timeout:10*(1073741821-b)-Y()});a.callbackNode=b}}}function Lh(a,b){dd=0;if(b)return b=ka(),Ue(a,b),V(a),null;var c=fd(a);if(0!==c){b=a.callbackNode;if((p&(ca|ma))!==H)throw Error(k(327));
xb();a===U&&c===P||$a(a,c);if(null!==t){var d=p;p|=ca;var e=Mh();do try{rj();break}catch(h){Nh(a,h)}while(1);le();p=d;gd.current=e;if(F===hd)throw b=id,$a(a,c),Ya(a,c),V(a),b;if(null===t)switch(e=a.finishedWork=a.current.alternate,a.finishedExpirationTime=c,d=F,U=null,d){case Xa:case hd:throw Error(k(345));case Oh:Ue(a,2<c?2:c);break;case ad:Ya(a,c);d=a.lastSuspendedTime;c===d&&(a.nextKnownPendingLevel=Ve(e));if(**********===ta&&(e=Re+Ph-Y(),10<e)){if(jd){var f=a.lastPingedTime;if(0===f||f>=c){a.lastPingedTime=
c;$a(a,c);break}}f=fd(a);if(0!==f&&f!==c)break;if(0!==d&&d!==c){a.lastPingedTime=d;break}a.timeoutHandle=We(ab.bind(null,a),e);break}ab(a);break;case bd:Ya(a,c);d=a.lastSuspendedTime;c===d&&(a.nextKnownPendingLevel=Ve(e));if(jd&&(e=a.lastPingedTime,0===e||e>=c)){a.lastPingedTime=c;$a(a,c);break}e=fd(a);if(0!==e&&e!==c)break;if(0!==d&&d!==c){a.lastPingedTime=d;break}**********!==Yb?d=10*(1073741821-Yb)-Y():**********===ta?d=0:(d=10*(1073741821-ta)-5E3,e=Y(),c=10*(1073741821-c)-e,d=e-d,0>d&&(d=0),d=
(120>d?120:480>d?480:1080>d?1080:1920>d?1920:3E3>d?3E3:4320>d?4320:1960*sj(d/1960))-d,c<d&&(d=c));if(10<d){a.timeoutHandle=We(ab.bind(null,a),d);break}ab(a);break;case Xe:if(**********!==ta&&null!==kd){f=ta;var g=kd;d=g.busyMinDurationMs|0;0>=d?d=0:(e=g.busyDelayMs|0,f=Y()-(10*(1073741821-f)-(g.timeoutMs|0||5E3)),d=f<=e?0:e+d-f);if(10<d){Ya(a,c);a.timeoutHandle=We(ab.bind(null,a),d);break}}ab(a);break;default:throw Error(k(329));}V(a);if(a.callbackNode===b)return Lh.bind(null,a)}}return null}function Te(a){var b=
a.lastExpiredTime;b=0!==b?b:**********;if((p&(ca|ma))!==H)throw Error(k(327));xb();a===U&&b===P||$a(a,b);if(null!==t){var c=p;p|=ca;var d=Mh();do try{tj();break}catch(e){Nh(a,e)}while(1);le();p=c;gd.current=d;if(F===hd)throw c=id,$a(a,b),Ya(a,b),V(a),c;if(null!==t)throw Error(k(261));a.finishedWork=a.current.alternate;a.finishedExpirationTime=b;U=null;ab(a);V(a)}return null}function uj(){if(null!==bb){var a=bb;bb=null;a.forEach(function(a,c){Ue(c,a);V(c)});ha()}}function Qh(a,b){var c=p;p|=1;try{return a(b)}finally{p=
c,p===H&&ha()}}function Rh(a,b){var c=p;p&=-2;p|=Ye;try{return a(b)}finally{p=c,p===H&&ha()}}function $a(a,b){a.finishedWork=null;a.finishedExpirationTime=0;var c=a.timeoutHandle;-1!==c&&(a.timeoutHandle=-1,vj(c));if(null!==t)for(c=t.return;null!==c;){var d=c;switch(d.tag){case 1:d=d.type.childContextTypes;null!==d&&void 0!==d&&(q(G),q(B));break;case 3:tb();q(G);q(B);break;case 5:te(d);break;case 4:tb();break;case 13:q(D);break;case 19:q(D);break;case 10:me(d)}c=c.return}U=a;t=Sa(a.current,null);
P=b;F=Xa;id=null;Yb=ta=**********;kd=null;Xb=0;jd=!1}function Nh(a,b){do{try{le();Sc.current=Tc;if(Uc)for(var c=z.memoizedState;null!==c;){var d=c.queue;null!==d&&(d.pending=null);c=c.next}Ia=0;J=K=z=null;Uc=!1;if(null===t||null===t.return)return F=hd,id=b,t=null;a:{var e=a,f=t.return,g=t,h=b;b=P;g.effectTag|=2048;g.firstEffect=g.lastEffect=null;if(null!==h&&"object"===typeof h&&"function"===typeof h.then){var m=h;if(0===(g.mode&2)){var n=g.alternate;n?(g.updateQueue=n.updateQueue,g.memoizedState=
n.memoizedState,g.expirationTime=n.expirationTime):(g.updateQueue=null,g.memoizedState=null)}var l=0!==(D.current&1),k=f;do{var p;if(p=13===k.tag){var q=k.memoizedState;if(null!==q)p=null!==q.dehydrated?!0:!1;else{var w=k.memoizedProps;p=void 0===w.fallback?!1:!0!==w.unstable_avoidThisFallback?!0:l?!1:!0}}if(p){var y=k.updateQueue;if(null===y){var r=new Set;r.add(m);k.updateQueue=r}else y.add(m);if(0===(k.mode&2)){k.effectTag|=64;g.effectTag&=-2981;if(1===g.tag)if(null===g.alternate)g.tag=17;else{var O=
Ea(**********,null);O.tag=Jc;Fa(g,O)}g.expirationTime=**********;break a}h=void 0;g=b;var v=e.pingCache;null===v?(v=e.pingCache=new wj,h=new Set,v.set(m,h)):(h=v.get(m),void 0===h&&(h=new Set,v.set(m,h)));if(!h.has(g)){h.add(g);var x=xj.bind(null,e,m,g);m.then(x,x)}k.effectTag|=4096;k.expirationTime=b;break a}k=k.return}while(null!==k);h=Error((na(g.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+
Bd(g))}F!==Xe&&(F=Oh);h=Le(h,g);k=f;do{switch(k.tag){case 3:m=h;k.effectTag|=4096;k.expirationTime=b;var A=Ih(k,m,b);Ug(k,A);break a;case 1:m=h;var u=k.type,B=k.stateNode;if(0===(k.effectTag&64)&&("function"===typeof u.getDerivedStateFromError||null!==B&&"function"===typeof B.componentDidCatch&&(null===La||!La.has(B)))){k.effectTag|=4096;k.expirationTime=b;var H=Jh(k,m,b);Ug(k,H);break a}}k=k.return}while(null!==k)}t=Sh(t)}catch(cj){b=cj;continue}break}while(1)}function Mh(a){a=gd.current;gd.current=
Tc;return null===a?Tc:a}function Vg(a,b){a<ta&&2<a&&(ta=a);null!==b&&a<Yb&&2<a&&(Yb=a,kd=b)}function Kc(a){a>Xb&&(Xb=a)}function tj(){for(;null!==t;)t=Th(t)}function rj(){for(;null!==t&&!yj();)t=Th(t)}function Th(a){var b=zj(a.alternate,a,P);a.memoizedProps=a.pendingProps;null===b&&(b=Sh(a));Uh.current=null;return b}function Sh(a){t=a;do{var b=t.alternate;a=t.return;if(0===(t.effectTag&2048)){b=hj(b,t,P);if(1===P||1!==t.childExpirationTime){for(var c=0,d=t.child;null!==d;){var e=d.expirationTime,
f=d.childExpirationTime;e>c&&(c=e);f>c&&(c=f);d=d.sibling}t.childExpirationTime=c}if(null!==b)return b;null!==a&&0===(a.effectTag&2048)&&(null===a.firstEffect&&(a.firstEffect=t.firstEffect),null!==t.lastEffect&&(null!==a.lastEffect&&(a.lastEffect.nextEffect=t.firstEffect),a.lastEffect=t.lastEffect),1<t.effectTag&&(null!==a.lastEffect?a.lastEffect.nextEffect=t:a.firstEffect=t,a.lastEffect=t))}else{b=lj(t);if(null!==b)return b.effectTag&=2047,b;null!==a&&(a.firstEffect=a.lastEffect=null,a.effectTag|=
2048)}b=t.sibling;if(null!==b)return b;t=a}while(null!==t);F===Xa&&(F=Xe);return null}function Ve(a){var b=a.expirationTime;a=a.childExpirationTime;return b>a?b:a}function ab(a){var b=Cc();Da(99,Aj.bind(null,a,b));return null}function Aj(a,b){do xb();while(null!==Zb);if((p&(ca|ma))!==H)throw Error(k(327));var c=a.finishedWork,d=a.finishedExpirationTime;if(null===c)return null;a.finishedWork=null;a.finishedExpirationTime=0;if(c===a.current)throw Error(k(177));a.callbackNode=null;a.callbackExpirationTime=
0;a.callbackPriority=90;a.nextKnownPendingLevel=0;var e=Ve(c);a.firstPendingTime=e;d<=a.lastSuspendedTime?a.firstSuspendedTime=a.lastSuspendedTime=a.nextKnownPendingLevel=0:d<=a.firstSuspendedTime&&(a.firstSuspendedTime=d-1);d<=a.lastPingedTime&&(a.lastPingedTime=0);d<=a.lastExpiredTime&&(a.lastExpiredTime=0);a===U&&(t=U=null,P=0);1<c.effectTag?null!==c.lastEffect?(c.lastEffect.nextEffect=c,e=c.firstEffect):e=c:e=c.firstEffect;if(null!==e){var f=p;p|=ma;Uh.current=null;Ze=tc;var g=kg();if(Xd(g)){if("selectionStart"in
g)var h={start:g.selectionStart,end:g.selectionEnd};else a:{h=(h=g.ownerDocument)&&h.defaultView||window;var m=h.getSelection&&h.getSelection();if(m&&0!==m.rangeCount){h=m.anchorNode;var n=m.anchorOffset,q=m.focusNode;m=m.focusOffset;try{h.nodeType,q.nodeType}catch(sb){h=null;break a}var ba=0,w=-1,y=-1,B=0,D=0,r=g,z=null;b:for(;;){for(var v;;){r!==h||0!==n&&3!==r.nodeType||(w=ba+n);r!==q||0!==m&&3!==r.nodeType||(y=ba+m);3===r.nodeType&&(ba+=r.nodeValue.length);if(null===(v=r.firstChild))break;z=r;
r=v}for(;;){if(r===g)break b;z===h&&++B===n&&(w=ba);z===q&&++D===m&&(y=ba);if(null!==(v=r.nextSibling))break;r=z;z=r.parentNode}r=v}h=-1===w||-1===y?null:{start:w,end:y}}else h=null}h=h||{start:0,end:0}}else h=null;$e={activeElementDetached:null,focusedElem:g,selectionRange:h};tc=!1;l=e;do try{Bj()}catch(sb){if(null===l)throw Error(k(330));Za(l,sb);l=l.nextEffect}while(null!==l);l=e;do try{for(g=a,h=b;null!==l;){var x=l.effectTag;x&16&&Wb(l.stateNode,"");if(x&128){var A=l.alternate;if(null!==A){var u=
A.ref;null!==u&&("function"===typeof u?u(null):u.current=null)}}switch(x&1038){case 2:Gh(l);l.effectTag&=-3;break;case 6:Gh(l);l.effectTag&=-3;Qe(l.alternate,l);break;case 1024:l.effectTag&=-1025;break;case 1028:l.effectTag&=-1025;Qe(l.alternate,l);break;case 4:Qe(l.alternate,l);break;case 8:n=l,Dh(g,n,h),Eh(n)}l=l.nextEffect}}catch(sb){if(null===l)throw Error(k(330));Za(l,sb);l=l.nextEffect}while(null!==l);u=$e;A=kg();x=u.focusedElem;h=u.selectionRange;if(A!==x&&x&&x.ownerDocument&&jg(x.ownerDocument.documentElement,
x)){null!==h&&Xd(x)&&(A=h.start,u=h.end,void 0===u&&(u=A),"selectionStart"in x?(x.selectionStart=A,x.selectionEnd=Math.min(u,x.value.length)):(u=(A=x.ownerDocument||document)&&A.defaultView||window,u.getSelection&&(u=u.getSelection(),n=x.textContent.length,g=Math.min(h.start,n),h=void 0===h.end?g:Math.min(h.end,n),!u.extend&&g>h&&(n=h,h=g,g=n),n=ig(x,g),q=ig(x,h),n&&q&&(1!==u.rangeCount||u.anchorNode!==n.node||u.anchorOffset!==n.offset||u.focusNode!==q.node||u.focusOffset!==q.offset)&&(A=A.createRange(),
A.setStart(n.node,n.offset),u.removeAllRanges(),g>h?(u.addRange(A),u.extend(q.node,q.offset)):(A.setEnd(q.node,q.offset),u.addRange(A))))));A=[];for(u=x;u=u.parentNode;)1===u.nodeType&&A.push({element:u,left:u.scrollLeft,top:u.scrollTop});"function"===typeof x.focus&&x.focus();for(x=0;x<A.length;x++)u=A[x],u.element.scrollLeft=u.left,u.element.scrollTop=u.top}tc=!!Ze;$e=Ze=null;a.current=c;l=e;do try{for(x=a;null!==l;){var F=l.effectTag;F&36&&oj(x,l.alternate,l);if(F&128){A=void 0;var E=l.ref;if(null!==
E){var G=l.stateNode;switch(l.tag){case 5:A=G;break;default:A=G}"function"===typeof E?E(A):E.current=A}}l=l.nextEffect}}catch(sb){if(null===l)throw Error(k(330));Za(l,sb);l=l.nextEffect}while(null!==l);l=null;Cj();p=f}else a.current=c;if(ld)ld=!1,Zb=a,$b=b;else for(l=e;null!==l;)b=l.nextEffect,l.nextEffect=null,l=b;b=a.firstPendingTime;0===b&&(La=null);**********===b?a===af?ac++:(ac=0,af=a):ac=0;"function"===typeof bf&&bf(c.stateNode,d);V(a);if(cd)throw cd=!1,a=Se,Se=null,a;if((p&Ye)!==H)return null;
ha();return null}function Bj(){for(;null!==l;){var a=l.effectTag;0!==(a&256)&&nj(l.alternate,l);0===(a&512)||ld||(ld=!0,Ng(97,function(){xb();return null}));l=l.nextEffect}}function xb(){if(90!==$b){var a=97<$b?97:$b;$b=90;return Da(a,Dj)}}function Dj(){if(null===Zb)return!1;var a=Zb;Zb=null;if((p&(ca|ma))!==H)throw Error(k(331));var b=p;p|=ma;for(a=a.current.firstEffect;null!==a;){try{var c=a;if(0!==(c.effectTag&512))switch(c.tag){case 0:case 11:case 15:case 22:Ah(5,c),Bh(5,c)}}catch(d){if(null===
a)throw Error(k(330));Za(a,d)}c=a.nextEffect;a.nextEffect=null;a=c}p=b;ha();return!0}function Vh(a,b,c){b=Le(c,b);b=Ih(a,b,**********);Fa(a,b);a=ed(a,**********);null!==a&&V(a)}function Za(a,b){if(3===a.tag)Vh(a,a,b);else for(var c=a.return;null!==c;){if(3===c.tag){Vh(c,a,b);break}else if(1===c.tag){var d=c.stateNode;if("function"===typeof c.type.getDerivedStateFromError||"function"===typeof d.componentDidCatch&&(null===La||!La.has(d))){a=Le(b,a);a=Jh(c,a,**********);Fa(c,a);c=ed(c,**********);null!==
c&&V(c);break}}c=c.return}}function xj(a,b,c){var d=a.pingCache;null!==d&&d.delete(b);U===a&&P===c?F===bd||F===ad&&**********===ta&&Y()-Re<Ph?$a(a,P):jd=!0:Kh(a,c)&&(b=a.lastPingedTime,0!==b&&b<c||(a.lastPingedTime=c,V(a)))}function qj(a,b){var c=a.stateNode;null!==c&&c.delete(b);b=0;0===b&&(b=ka(),b=Va(b,a,null));a=ed(a,b);null!==a&&V(a)}function Ej(a){if("undefined"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var b=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(b.isDisabled||!b.supportsFiber)return!0;try{var c=
b.inject(a);bf=function(a,e){try{b.onCommitFiberRoot(c,a,void 0,64===(a.current.effectTag&64))}catch(f){}};Ne=function(a){try{b.onCommitFiberUnmount(c,a)}catch(e){}}}catch(d){}return!0}function Fj(a,b,c,d){this.tag=a;this.key=c;this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null;this.index=0;this.ref=null;this.pendingProps=b;this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null;this.mode=d;this.effectTag=0;this.lastEffect=this.firstEffect=this.nextEffect=
null;this.childExpirationTime=this.expirationTime=0;this.alternate=null}function Ge(a){a=a.prototype;return!(!a||!a.isReactComponent)}function Gj(a){if("function"===typeof a)return Ge(a)?1:0;if(void 0!==a&&null!==a){a=a.$$typeof;if(a===zd)return 11;if(a===Ad)return 14}return 2}function Sa(a,b){var c=a.alternate;null===c?(c=la(a.tag,b,a.key,a.mode),c.elementType=a.elementType,c.type=a.type,c.stateNode=a.stateNode,c.alternate=a,a.alternate=c):(c.pendingProps=b,c.effectTag=0,c.nextEffect=null,c.firstEffect=
null,c.lastEffect=null);c.childExpirationTime=a.childExpirationTime;c.expirationTime=a.expirationTime;c.child=a.child;c.memoizedProps=a.memoizedProps;c.memoizedState=a.memoizedState;c.updateQueue=a.updateQueue;b=a.dependencies;c.dependencies=null===b?null:{expirationTime:b.expirationTime,firstContext:b.firstContext,responders:b.responders};c.sibling=a.sibling;c.index=a.index;c.ref=a.ref;return c}function Oc(a,b,c,d,e,f){var g=2;d=a;if("function"===typeof a)Ge(a)&&(g=1);else if("string"===typeof a)g=
5;else a:switch(a){case Ma:return Ha(c.children,e,f,b);case Hj:g=8;e|=7;break;case Af:g=8;e|=1;break;case kc:return a=la(12,c,b,e|8),a.elementType=kc,a.type=kc,a.expirationTime=f,a;case lc:return a=la(13,c,b,e),a.type=lc,a.elementType=lc,a.expirationTime=f,a;case yd:return a=la(19,c,b,e),a.elementType=yd,a.expirationTime=f,a;default:if("object"===typeof a&&null!==a)switch(a.$$typeof){case Cf:g=10;break a;case Bf:g=9;break a;case zd:g=11;break a;case Ad:g=14;break a;case Ef:g=16;d=null;break a;case Df:g=
22;break a}throw Error(k(130,null==a?a:typeof a,""));}b=la(g,c,b,e);b.elementType=a;b.type=d;b.expirationTime=f;return b}function Ha(a,b,c,d){a=la(7,a,d,b);a.expirationTime=c;return a}function qe(a,b,c){a=la(6,a,null,b);a.expirationTime=c;return a}function re(a,b,c){b=la(4,null!==a.children?a.children:[],a.key,b);b.expirationTime=c;b.stateNode={containerInfo:a.containerInfo,pendingChildren:null,implementation:a.implementation};return b}function Ij(a,b,c){this.tag=b;this.current=null;this.containerInfo=
a;this.pingCache=this.pendingChildren=null;this.finishedExpirationTime=0;this.finishedWork=null;this.timeoutHandle=-1;this.pendingContext=this.context=null;this.hydrate=c;this.callbackNode=null;this.callbackPriority=90;this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function Kh(a,b){var c=a.firstSuspendedTime;a=a.lastSuspendedTime;return 0!==c&&c>=b&&a<=b}function Ya(a,b){var c=a.firstSuspendedTime,d=a.lastSuspendedTime;
c<b&&(a.firstSuspendedTime=b);if(d>b||0===c)a.lastSuspendedTime=b;b<=a.lastPingedTime&&(a.lastPingedTime=0);b<=a.lastExpiredTime&&(a.lastExpiredTime=0)}function yh(a,b){b>a.firstPendingTime&&(a.firstPendingTime=b);var c=a.firstSuspendedTime;0!==c&&(b>=c?a.firstSuspendedTime=a.lastSuspendedTime=a.nextKnownPendingLevel=0:b>=a.lastSuspendedTime&&(a.lastSuspendedTime=b+1),b>a.nextKnownPendingLevel&&(a.nextKnownPendingLevel=b))}function Ue(a,b){var c=a.lastExpiredTime;if(0===c||c>b)a.lastExpiredTime=b}
function md(a,b,c,d){var e=b.current,f=ka(),g=Vb.suspense;f=Va(f,e,g);a:if(c){c=c._reactInternalFiber;b:{if(Na(c)!==c||1!==c.tag)throw Error(k(170));var h=c;do{switch(h.tag){case 3:h=h.stateNode.context;break b;case 1:if(N(h.type)){h=h.stateNode.__reactInternalMemoizedMergedChildContext;break b}}h=h.return}while(null!==h);throw Error(k(171));}if(1===c.tag){var m=c.type;if(N(m)){c=Gg(c,m,h);break a}}c=h}else c=Ca;null===b.context?b.context=c:b.pendingContext=c;b=Ea(f,g);b.payload={element:a};d=void 0===
d?null:d;null!==d&&(b.callback=d);Fa(e,b);Ja(e,f);return f}function cf(a){a=a.current;if(!a.child)return null;switch(a.child.tag){case 5:return a.child.stateNode;default:return a.child.stateNode}}function Wh(a,b){a=a.memoizedState;null!==a&&null!==a.dehydrated&&a.retryTime<b&&(a.retryTime=b)}function df(a,b){Wh(a,b);(a=a.alternate)&&Wh(a,b)}function ef(a,b,c){c=null!=c&&!0===c.hydrate;var d=new Ij(a,b,c),e=la(3,null,null,2===b?7:1===b?3:0);d.current=e;e.stateNode=d;ne(e);a[Lb]=d.current;c&&0!==b&&
xi(a,9===a.nodeType?a:a.ownerDocument);this._internalRoot=d}function bc(a){return!(!a||1!==a.nodeType&&9!==a.nodeType&&11!==a.nodeType&&(8!==a.nodeType||" react-mount-point-unstable "!==a.nodeValue))}function Jj(a,b){b||(b=a?9===a.nodeType?a.documentElement:a.firstChild:null,b=!(!b||1!==b.nodeType||!b.hasAttribute("data-reactroot")));if(!b)for(var c;c=a.lastChild;)a.removeChild(c);return new ef(a,0,b?{hydrate:!0}:void 0)}function nd(a,b,c,d,e){var f=c._reactRootContainer;if(f){var g=f._internalRoot;
if("function"===typeof e){var h=e;e=function(){var a=cf(g);h.call(a)}}md(b,g,a,e)}else{f=c._reactRootContainer=Jj(c,d);g=f._internalRoot;if("function"===typeof e){var m=e;e=function(){var a=cf(g);m.call(a)}}Rh(function(){md(b,g,a,e)})}return cf(g)}function Kj(a,b,c){var d=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:gb,key:null==d?null:""+d,children:a,containerInfo:b,implementation:c}}function Xh(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;
if(!bc(b))throw Error(k(200));return Kj(a,b,null,c)}if(!ea)throw Error(k(227));var ki=function(a,b,c,d,e,f,g,h,m){var n=Array.prototype.slice.call(arguments,3);try{b.apply(c,n)}catch(C){this.onError(C)}},yb=!1,gc=null,hc=!1,pd=null,li={onError:function(a){yb=!0;gc=a}},td=null,rf=null,mf=null,ic=null,cb={},jc=[],qd={},db={},rd={},wa=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),M=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.assign,
sd=null,eb=null,fb=null,ee=function(a,b){return a(b)},eg=function(a,b,c,d,e){return a(b,c,d,e)},vd=function(){},vf=ee,Oa=!1,wd=!1,Z=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Scheduler,Lj=Z.unstable_cancelCallback,ff=Z.unstable_now,$f=Z.unstable_scheduleCallback,Mj=Z.unstable_shouldYield,Yh=Z.unstable_requestPaint,Pd=Z.unstable_runWithPriority,Nj=Z.unstable_getCurrentPriorityLevel,Oj=Z.unstable_ImmediatePriority,Zh=Z.unstable_UserBlockingPriority,ag=Z.unstable_NormalPriority,Pj=Z.unstable_LowPriority,
Qj=Z.unstable_IdlePriority,oi=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,wf=Object.prototype.hasOwnProperty,yf={},xf={},E={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(a){E[a]=
new L(a,0,!1,a,null,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(a){var b=a[0];E[b]=new L(b,1,!1,a[1],null,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(a){E[a]=new L(a,2,!1,a.toLowerCase(),null,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(a){E[a]=new L(a,2,!1,a,null,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(a){E[a]=
new L(a,3,!1,a.toLowerCase(),null,!1)});["checked","multiple","muted","selected"].forEach(function(a){E[a]=new L(a,3,!0,a,null,!1)});["capture","download"].forEach(function(a){E[a]=new L(a,4,!1,a,null,!1)});["cols","rows","size","span"].forEach(function(a){E[a]=new L(a,6,!1,a,null,!1)});["rowSpan","start"].forEach(function(a){E[a]=new L(a,5,!1,a.toLowerCase(),null,!1)});var gf=/[\-:]([a-z])/g,hf=function(a){return a[1].toUpperCase()};"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(a){var b=
a.replace(gf,hf);E[b]=new L(b,1,!1,a,null,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(a){var b=a.replace(gf,hf);E[b]=new L(b,1,!1,a,"http://www.w3.org/1999/xlink",!1)});["xml:base","xml:lang","xml:space"].forEach(function(a){var b=a.replace(gf,hf);E[b]=new L(b,1,!1,a,"http://www.w3.org/XML/1998/namespace",!1)});["tabIndex","crossOrigin"].forEach(function(a){E[a]=new L(a,1,!1,a.toLowerCase(),null,!1)});E.xlinkHref=new L("xlinkHref",1,
!1,"xlink:href","http://www.w3.org/1999/xlink",!0);["src","href","action","formAction"].forEach(function(a){E[a]=new L(a,1,!1,a.toLowerCase(),null,!0)});var da=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;da.hasOwnProperty("ReactCurrentDispatcher")||(da.ReactCurrentDispatcher={current:null});da.hasOwnProperty("ReactCurrentBatchConfig")||(da.ReactCurrentBatchConfig={suspense:null});var si=/^(.*)[\\\/]/,Q="function"===typeof Symbol&&Symbol.for,Pc=Q?Symbol.for("react.element"):60103,gb=Q?Symbol.for("react.portal"):
60106,Ma=Q?Symbol.for("react.fragment"):60107,Af=Q?Symbol.for("react.strict_mode"):60108,kc=Q?Symbol.for("react.profiler"):60114,Cf=Q?Symbol.for("react.provider"):60109,Bf=Q?Symbol.for("react.context"):60110,Hj=Q?Symbol.for("react.concurrent_mode"):60111,zd=Q?Symbol.for("react.forward_ref"):60112,lc=Q?Symbol.for("react.suspense"):60113,yd=Q?Symbol.for("react.suspense_list"):60120,Ad=Q?Symbol.for("react.memo"):60115,Ef=Q?Symbol.for("react.lazy"):60116,Df=Q?Symbol.for("react.block"):60121,zf="function"===
typeof Symbol&&Symbol.iterator,od,xh=function(a){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(b,c,d,e){MSApp.execUnsafeLocalFunction(function(){return a(b,c,d,e)})}:a}(function(a,b){if("http://www.w3.org/2000/svg"!==a.namespaceURI||"innerHTML"in a)a.innerHTML=b;else{od=od||document.createElement("div");od.innerHTML="<svg>"+b.valueOf().toString()+"</svg>";for(b=od.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;b.firstChild;)a.appendChild(b.firstChild)}}),Wb=function(a,
b){if(b){var c=a.firstChild;if(c&&c===a.lastChild&&3===c.nodeType){c.nodeValue=b;return}}a.textContent=b},ib={animationend:nc("Animation","AnimationEnd"),animationiteration:nc("Animation","AnimationIteration"),animationstart:nc("Animation","AnimationStart"),transitionend:nc("Transition","TransitionEnd")},Id={},Of={};wa&&(Of=document.createElement("div").style,"AnimationEvent"in window||(delete ib.animationend.animation,delete ib.animationiteration.animation,delete ib.animationstart.animation),"TransitionEvent"in
window||delete ib.transitionend.transition);var $h=oc("animationend"),ai=oc("animationiteration"),bi=oc("animationstart"),ci=oc("transitionend"),Db="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Pf=new ("function"===typeof WeakMap?WeakMap:Map),Ab=null,wi=function(a){if(a){var b=a._dispatchListeners,c=a._dispatchInstances;
if(Array.isArray(b))for(var d=0;d<b.length&&!a.isPropagationStopped();d++)lf(a,b[d],c[d]);else b&&lf(a,b,c);a._dispatchListeners=null;a._dispatchInstances=null;a.isPersistent()||a.constructor.release(a)}},qc=[],Rd=!1,fa=[],xa=null,ya=null,za=null,Eb=new Map,Fb=new Map,Jb=[],Nd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),
yi="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" "),dg={},cg=new Map,Td=new Map,Rj=["abort","abort",$h,"animationEnd",ai,"animationIteration",bi,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata",
"loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",ci,"transitionEnd","waiting","waiting"];Sd("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),
0);Sd("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1);Sd(Rj,2);(function(a,b){for(var c=0;c<a.length;c++)Td.set(a[c],b)})("change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),0);var Hi=Zh,Gi=Pd,tc=!0,Kb={animationIterationCount:!0,
borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,
strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Sj=["Webkit","ms","Moz","O"];Object.keys(Kb).forEach(function(a){Sj.forEach(function(b){b=b+a.charAt(0).toUpperCase()+a.substring(1);Kb[b]=Kb[a]})});var Ii=M({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),ng="$",og="/$",$d="$?",Zd="$!",Ze=null,$e=null,We="function"===typeof setTimeout?setTimeout:void 0,vj="function"===
typeof clearTimeout?clearTimeout:void 0,jf=Math.random().toString(36).slice(2),Aa="__reactInternalInstance$"+jf,vc="__reactEventHandlers$"+jf,Lb="__reactContainere$"+jf,Ba=null,ce=null,wc=null;M(R.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():"unknown"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=xc)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():"unknown"!==
typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=xc)},persist:function(){this.isPersistent=xc},isPersistent:yc,destructor:function(){var a=this.constructor.Interface,b;for(b in a)this[b]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null;this.isPropagationStopped=this.isDefaultPrevented=yc;this._dispatchInstances=this._dispatchListeners=null}});R.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(a){return a.timeStamp||
Date.now()},defaultPrevented:null,isTrusted:null};R.extend=function(a){function b(){return c.apply(this,arguments)}var c=this,d=function(){};d.prototype=c.prototype;d=new d;M(d,b.prototype);b.prototype=d;b.prototype.constructor=b;b.Interface=M({},c.Interface,a);b.extend=c.extend;sg(b);return b};sg(R);var Tj=R.extend({data:null}),Uj=R.extend({data:null}),Ni=[9,13,27,32],de=wa&&"CompositionEvent"in window,cc=null;wa&&"documentMode"in document&&(cc=document.documentMode);var Vj=wa&&"TextEvent"in window&&
!cc,xg=wa&&(!de||cc&&8<cc&&11>=cc),wg=String.fromCharCode(32),ua={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},
dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},vg=!1,mb=!1,Wj={eventTypes:ua,extractEvents:function(a,b,c,d,e){var f;if(de)b:{switch(a){case "compositionstart":var g=ua.compositionStart;break b;case "compositionend":g=ua.compositionEnd;break b;case "compositionupdate":g=
ua.compositionUpdate;break b}g=void 0}else mb?tg(a,c)&&(g=ua.compositionEnd):"keydown"===a&&229===c.keyCode&&(g=ua.compositionStart);g?(xg&&"ko"!==c.locale&&(mb||g!==ua.compositionStart?g===ua.compositionEnd&&mb&&(f=rg()):(Ba=d,ce="value"in Ba?Ba.value:Ba.textContent,mb=!0)),e=Tj.getPooled(g,b,c,d),f?e.data=f:(f=ug(c),null!==f&&(e.data=f)),lb(e),f=e):f=null;(a=Vj?Oi(a,c):Pi(a,c))?(b=Uj.getPooled(ua.beforeInput,b,c,d),b.data=a,lb(b)):b=null;return null===f?b:null===b?f:[f,b]}},Qi={color:!0,date:!0,
datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0},Ag={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}},Mb=null,Nb=null,kf=!1;wa&&(kf=Tf("input")&&(!document.documentMode||9<document.documentMode));var Xj={eventTypes:Ag,_isInputEventSupported:kf,extractEvents:function(a,b,c,d,e){e=b?Pa(b):window;var f=
e.nodeName&&e.nodeName.toLowerCase();if("select"===f||"input"===f&&"file"===e.type)var g=Si;else if(yg(e))if(kf)g=Wi;else{g=Ui;var h=Ti}else(f=e.nodeName)&&"input"===f.toLowerCase()&&("checkbox"===e.type||"radio"===e.type)&&(g=Vi);if(g&&(g=g(a,b)))return zg(g,c,d);h&&h(a,e,b);"blur"===a&&(a=e._wrapperState)&&a.controlled&&"number"===e.type&&Ed(e,"number",e.value)}},dc=R.extend({view:null,detail:null}),Yi={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"},di=0,ei=0,fi=!1,gi=!1,ec=dc.extend({screenX:null,
screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:fe,button:null,buttons:null,relatedTarget:function(a){return a.relatedTarget||(a.fromElement===a.srcElement?a.toElement:a.fromElement)},movementX:function(a){if("movementX"in a)return a.movementX;var b=di;di=a.screenX;return fi?"mousemove"===a.type?a.screenX-b:0:(fi=!0,0)},movementY:function(a){if("movementY"in a)return a.movementY;var b=ei;ei=a.screenY;return gi?"mousemove"===
a.type?a.screenY-b:0:(gi=!0,0)}}),hi=ec.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),fc={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout",
"pointerover"]}},Yj={eventTypes:fc,extractEvents:function(a,b,c,d,e){var f="mouseover"===a||"pointerover"===a,g="mouseout"===a||"pointerout"===a;if(f&&0===(e&32)&&(c.relatedTarget||c.fromElement)||!g&&!f)return null;f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window;if(g){if(g=b,b=(b=c.relatedTarget||c.toElement)?Bb(b):null,null!==b){var h=Na(b);if(b!==h||5!==b.tag&&6!==b.tag)b=null}}else g=null;if(g===b)return null;if("mouseout"===a||"mouseover"===a){var m=ec;var n=fc.mouseLeave;
var l=fc.mouseEnter;var k="mouse"}else if("pointerout"===a||"pointerover"===a)m=hi,n=fc.pointerLeave,l=fc.pointerEnter,k="pointer";a=null==g?f:Pa(g);f=null==b?f:Pa(b);n=m.getPooled(n,g,c,d);n.type=k+"leave";n.target=a;n.relatedTarget=f;c=m.getPooled(l,b,c,d);c.type=k+"enter";c.target=f;c.relatedTarget=a;d=g;k=b;if(d&&k)a:{m=d;l=k;g=0;for(a=m;a;a=pa(a))g++;a=0;for(b=l;b;b=pa(b))a++;for(;0<g-a;)m=pa(m),g--;for(;0<a-g;)l=pa(l),a--;for(;g--;){if(m===l||m===l.alternate)break a;m=pa(m);l=pa(l)}m=null}else m=
null;l=m;for(m=[];d&&d!==l;){g=d.alternate;if(null!==g&&g===l)break;m.push(d);d=pa(d)}for(d=[];k&&k!==l;){g=k.alternate;if(null!==g&&g===l)break;d.push(k);k=pa(k)}for(k=0;k<m.length;k++)be(m[k],"bubbled",n);for(k=d.length;0<k--;)be(d[k],"captured",c);return 0===(e&64)?[n]:[n,c]}},Qa="function"===typeof Object.is?Object.is:Zi,$i=Object.prototype.hasOwnProperty,Zj=wa&&"documentMode"in document&&11>=document.documentMode,Eg={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},
dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},nb=null,he=null,Pb=null,ge=!1,ak={eventTypes:Eg,extractEvents:function(a,b,c,d,e,f){e=f||(d.window===d?d.document:9===d.nodeType?d:d.ownerDocument);if(!(f=!e)){a:{e=Jd(e);f=rd.onSelect;for(var g=0;g<f.length;g++)if(!e.has(f[g])){e=!1;break a}e=!0}f=!e}if(f)return null;e=b?Pa(b):window;switch(a){case "focus":if(yg(e)||"true"===e.contentEditable)nb=e,he=b,Pb=null;break;case "blur":Pb=he=nb=null;
break;case "mousedown":ge=!0;break;case "contextmenu":case "mouseup":case "dragend":return ge=!1,Dg(c,d);case "selectionchange":if(Zj)break;case "keydown":case "keyup":return Dg(c,d)}return null}},bk=R.extend({animationName:null,elapsedTime:null,pseudoElement:null}),ck=R.extend({clipboardData:function(a){return"clipboardData"in a?a.clipboardData:window.clipboardData}}),dk=dc.extend({relatedTarget:null}),ek={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",
Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fk={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",
224:"Meta"},gk=dc.extend({key:function(a){if(a.key){var b=ek[a.key]||a.key;if("Unidentified"!==b)return b}return"keypress"===a.type?(a=Ac(a),13===a?"Enter":String.fromCharCode(a)):"keydown"===a.type||"keyup"===a.type?fk[a.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:fe,charCode:function(a){return"keypress"===a.type?Ac(a):0},keyCode:function(a){return"keydown"===a.type||"keyup"===a.type?a.keyCode:0},which:function(a){return"keypress"===
a.type?Ac(a):"keydown"===a.type||"keyup"===a.type?a.keyCode:0}}),hk=ec.extend({dataTransfer:null}),ik=dc.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:fe}),jk=R.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),kk=ec.extend({deltaX:function(a){return"deltaX"in a?a.deltaX:"wheelDeltaX"in a?-a.wheelDeltaX:0},deltaY:function(a){return"deltaY"in a?a.deltaY:"wheelDeltaY"in a?-a.wheelDeltaY:"wheelDelta"in a?
-a.wheelDelta:0},deltaZ:null,deltaMode:null}),lk={eventTypes:dg,extractEvents:function(a,b,c,d,e){e=cg.get(a);if(!e)return null;switch(a){case "keypress":if(0===Ac(c))return null;case "keydown":case "keyup":a=gk;break;case "blur":case "focus":a=dk;break;case "click":if(2===c.button)return null;case "auxclick":case "dblclick":case "mousedown":case "mousemove":case "mouseup":case "mouseout":case "mouseover":case "contextmenu":a=ec;break;case "drag":case "dragend":case "dragenter":case "dragexit":case "dragleave":case "dragover":case "dragstart":case "drop":a=
hk;break;case "touchcancel":case "touchend":case "touchmove":case "touchstart":a=ik;break;case $h:case ai:case bi:a=bk;break;case ci:a=jk;break;case "scroll":a=dc;break;case "wheel":a=kk;break;case "copy":case "cut":case "paste":a=ck;break;case "gotpointercapture":case "lostpointercapture":case "pointercancel":case "pointerdown":case "pointermove":case "pointerout":case "pointerover":case "pointerup":a=hi;break;default:a=R}b=a.getPooled(e,b,c,d);lb(b);return b}};(function(a){if(ic)throw Error(k(101));
ic=Array.prototype.slice.call(a);nf()})("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" "));(function(a,b,c){td=a;rf=b;mf=c})(ae,Hb,Pa);pf({SimpleEventPlugin:lk,EnterLeaveEventPlugin:Yj,ChangeEventPlugin:Xj,SelectEventPlugin:ak,BeforeInputEventPlugin:Wj});var ie=[],ob=-1,Ca={},B={current:Ca},G={current:!1},Ra=Ca,bj=Pd,je=$f,Rg=Lj,aj=Nj,Dc=Oj,Ig=Zh,Jg=ag,Kg=Pj,Lg=Qj,Qg={},yj=Mj,Cj=void 0!==Yh?Yh:function(){},qa=null,
Ec=null,ke=!1,ii=ff(),Y=1E4>ii?ff:function(){return ff()-ii},Ic={current:null},Hc=null,qb=null,Gc=null,Tg=0,Jc=2,Ga=!1,Vb=da.ReactCurrentBatchConfig,$g=(new ea.Component).refs,Mc={isMounted:function(a){return(a=a._reactInternalFiber)?Na(a)===a:!1},enqueueSetState:function(a,b,c){a=a._reactInternalFiber;var d=ka(),e=Vb.suspense;d=Va(d,a,e);e=Ea(d,e);e.payload=b;void 0!==c&&null!==c&&(e.callback=c);Fa(a,e);Ja(a,d)},enqueueReplaceState:function(a,b,c){a=a._reactInternalFiber;var d=ka(),e=Vb.suspense;
d=Va(d,a,e);e=Ea(d,e);e.tag=1;e.payload=b;void 0!==c&&null!==c&&(e.callback=c);Fa(a,e);Ja(a,d)},enqueueForceUpdate:function(a,b){a=a._reactInternalFiber;var c=ka(),d=Vb.suspense;c=Va(c,a,d);d=Ea(c,d);d.tag=Jc;void 0!==b&&null!==b&&(d.callback=b);Fa(a,d);Ja(a,c)}},Qc=Array.isArray,wb=ah(!0),Fe=ah(!1),Sb={},ja={current:Sb},Ub={current:Sb},Tb={current:Sb},D={current:0},Sc=da.ReactCurrentDispatcher,X=da.ReactCurrentBatchConfig,Ia=0,z=null,K=null,J=null,Uc=!1,Tc={readContext:W,useCallback:S,useContext:S,
useEffect:S,useImperativeHandle:S,useLayoutEffect:S,useMemo:S,useReducer:S,useRef:S,useState:S,useDebugValue:S,useResponder:S,useDeferredValue:S,useTransition:S},dj={readContext:W,useCallback:ih,useContext:W,useEffect:eh,useImperativeHandle:function(a,b,c){c=null!==c&&void 0!==c?c.concat([a]):null;return ze(4,2,gh.bind(null,b,a),c)},useLayoutEffect:function(a,b){return ze(4,2,a,b)},useMemo:function(a,b){var c=ub();b=void 0===b?null:b;a=a();c.memoizedState=[a,b];return a},useReducer:function(a,b,c){var d=
ub();b=void 0!==c?c(b):b;d.memoizedState=d.baseState=b;a=d.queue={pending:null,dispatch:null,lastRenderedReducer:a,lastRenderedState:b};a=a.dispatch=ch.bind(null,z,a);return[d.memoizedState,a]},useRef:function(a){var b=ub();a={current:a};return b.memoizedState=a},useState:xe,useDebugValue:Be,useResponder:ue,useDeferredValue:function(a,b){var c=xe(a),d=c[0],e=c[1];eh(function(){var c=X.suspense;X.suspense=void 0===b?null:b;try{e(a)}finally{X.suspense=c}},[a,b]);return d},useTransition:function(a){var b=
xe(!1),c=b[0];b=b[1];return[ih(Ce.bind(null,b,a),[b,a]),c]}},ej={readContext:W,useCallback:Yc,useContext:W,useEffect:Xc,useImperativeHandle:hh,useLayoutEffect:fh,useMemo:jh,useReducer:Vc,useRef:dh,useState:function(a){return Vc(Ua)},useDebugValue:Be,useResponder:ue,useDeferredValue:function(a,b){var c=Vc(Ua),d=c[0],e=c[1];Xc(function(){var c=X.suspense;X.suspense=void 0===b?null:b;try{e(a)}finally{X.suspense=c}},[a,b]);return d},useTransition:function(a){var b=Vc(Ua),c=b[0];b=b[1];return[Yc(Ce.bind(null,
b,a),[b,a]),c]}},fj={readContext:W,useCallback:Yc,useContext:W,useEffect:Xc,useImperativeHandle:hh,useLayoutEffect:fh,useMemo:jh,useReducer:Wc,useRef:dh,useState:function(a){return Wc(Ua)},useDebugValue:Be,useResponder:ue,useDeferredValue:function(a,b){var c=Wc(Ua),d=c[0],e=c[1];Xc(function(){var c=X.suspense;X.suspense=void 0===b?null:b;try{e(a)}finally{X.suspense=c}},[a,b]);return d},useTransition:function(a){var b=Wc(Ua),c=b[0];b=b[1];return[Yc(Ce.bind(null,b,a),[b,a]),c]}},ra=null,Ka=null,Wa=
!1,gj=da.ReactCurrentOwner,ia=!1,Je={dehydrated:null,retryTime:0};var jj=function(a,b,c,d){for(c=b.child;null!==c;){if(5===c.tag||6===c.tag)a.appendChild(c.stateNode);else if(4!==c.tag&&null!==c.child){c.child.return=c;c=c.child;continue}if(c===b)break;for(;null===c.sibling;){if(null===c.return||c.return===b)return;c=c.return}c.sibling.return=c.return;c=c.sibling}};var wh=function(a){};var ij=function(a,b,c,d,e){var f=a.memoizedProps;if(f!==d){var g=b.stateNode;Ta(ja.current);a=null;switch(c){case "input":f=
Cd(g,f);d=Cd(g,d);a=[];break;case "option":f=Fd(g,f);d=Fd(g,d);a=[];break;case "select":f=M({},f,{value:void 0});d=M({},d,{value:void 0});a=[];break;case "textarea":f=Gd(g,f);d=Gd(g,d);a=[];break;default:"function"!==typeof f.onClick&&"function"===typeof d.onClick&&(g.onclick=uc)}Ud(c,d);var h,m;c=null;for(h in f)if(!d.hasOwnProperty(h)&&f.hasOwnProperty(h)&&null!=f[h])if("style"===h)for(m in g=f[h],g)g.hasOwnProperty(m)&&(c||(c={}),c[m]="");else"dangerouslySetInnerHTML"!==h&&"children"!==h&&"suppressContentEditableWarning"!==
h&&"suppressHydrationWarning"!==h&&"autoFocus"!==h&&(db.hasOwnProperty(h)?a||(a=[]):(a=a||[]).push(h,null));for(h in d){var k=d[h];g=null!=f?f[h]:void 0;if(d.hasOwnProperty(h)&&k!==g&&(null!=k||null!=g))if("style"===h)if(g){for(m in g)!g.hasOwnProperty(m)||k&&k.hasOwnProperty(m)||(c||(c={}),c[m]="");for(m in k)k.hasOwnProperty(m)&&g[m]!==k[m]&&(c||(c={}),c[m]=k[m])}else c||(a||(a=[]),a.push(h,c)),c=k;else"dangerouslySetInnerHTML"===h?(k=k?k.__html:void 0,g=g?g.__html:void 0,null!=k&&g!==k&&(a=a||
[]).push(h,k)):"children"===h?g===k||"string"!==typeof k&&"number"!==typeof k||(a=a||[]).push(h,""+k):"suppressContentEditableWarning"!==h&&"suppressHydrationWarning"!==h&&(db.hasOwnProperty(h)?(null!=k&&oa(e,h),a||g===k||(a=[])):(a=a||[]).push(h,k))}c&&(a=a||[]).push("style",c);e=a;if(b.updateQueue=e)b.effectTag|=4}};var kj=function(a,b,c,d){c!==d&&(b.effectTag|=4)};var pj="function"===typeof WeakSet?WeakSet:Set,wj="function"===typeof WeakMap?WeakMap:Map,sj=Math.ceil,gd=da.ReactCurrentDispatcher,
Uh=da.ReactCurrentOwner,H=0,Ye=8,ca=16,ma=32,Xa=0,hd=1,Oh=2,ad=3,bd=4,Xe=5,p=H,U=null,t=null,P=0,F=Xa,id=null,ta=**********,Yb=**********,kd=null,Xb=0,jd=!1,Re=0,Ph=500,l=null,cd=!1,Se=null,La=null,ld=!1,Zb=null,$b=90,bb=null,ac=0,af=null,dd=0,Ja=function(a,b){if(50<ac)throw ac=0,af=null,Error(k(185));a=ed(a,b);if(null!==a){var c=Cc();**********===b?(p&Ye)!==H&&(p&(ca|ma))===H?Te(a):(V(a),p===H&&ha()):V(a);(p&4)===H||98!==c&&99!==c||(null===bb?bb=new Map([[a,b]]):(c=bb.get(a),(void 0===c||c>b)&&bb.set(a,
b)))}};var zj=function(a,b,c){var d=b.expirationTime;if(null!==a){var e=b.pendingProps;if(a.memoizedProps!==e||G.current)ia=!0;else{if(d<c){ia=!1;switch(b.tag){case 3:sh(b);Ee();break;case 5:bh(b);if(b.mode&4&&1!==c&&e.hidden)return b.expirationTime=b.childExpirationTime=1,null;break;case 1:N(b.type)&&Bc(b);break;case 4:se(b,b.stateNode.containerInfo);break;case 10:d=b.memoizedProps.value;e=b.type._context;y(Ic,e._currentValue);e._currentValue=d;break;case 13:if(null!==b.memoizedState){d=b.child.childExpirationTime;
if(0!==d&&d>=c)return th(a,b,c);y(D,D.current&1);b=sa(a,b,c);return null!==b?b.sibling:null}y(D,D.current&1);break;case 19:d=b.childExpirationTime>=c;if(0!==(a.effectTag&64)){if(d)return vh(a,b,c);b.effectTag|=64}e=b.memoizedState;null!==e&&(e.rendering=null,e.tail=null);y(D,D.current);if(!d)return null}return sa(a,b,c)}ia=!1}}else ia=!1;b.expirationTime=0;switch(b.tag){case 2:d=b.type;null!==a&&(a.alternate=null,b.alternate=null,b.effectTag|=2);a=b.pendingProps;e=pb(b,B.current);rb(b,c);e=we(null,
b,d,a,e,c);b.effectTag|=1;if("object"===typeof e&&null!==e&&"function"===typeof e.render&&void 0===e.$$typeof){b.tag=1;b.memoizedState=null;b.updateQueue=null;if(N(d)){var f=!0;Bc(b)}else f=!1;b.memoizedState=null!==e.state&&void 0!==e.state?e.state:null;ne(b);var g=d.getDerivedStateFromProps;"function"===typeof g&&Lc(b,d,g,a);e.updater=Mc;b.stateNode=e;e._reactInternalFiber=b;pe(b,d,a,c);b=Ie(null,b,d,!0,f,c)}else b.tag=0,T(null,b,e,c),b=b.child;return b;case 16:a:{e=b.elementType;null!==a&&(a.alternate=
null,b.alternate=null,b.effectTag|=2);a=b.pendingProps;ri(e);if(1!==e._status)throw e._result;e=e._result;b.type=e;f=b.tag=Gj(e);a=aa(e,a);switch(f){case 0:b=He(null,b,e,a,c);break a;case 1:b=rh(null,b,e,a,c);break a;case 11:b=nh(null,b,e,a,c);break a;case 14:b=oh(null,b,e,aa(e.type,a),d,c);break a}throw Error(k(306,e,""));}return b;case 0:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),He(a,b,d,e,c);case 1:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),rh(a,b,d,e,c);
case 3:sh(b);d=b.updateQueue;if(null===a||null===d)throw Error(k(282));d=b.pendingProps;e=b.memoizedState;e=null!==e?e.element:null;oe(a,b);Qb(b,d,null,c);d=b.memoizedState.element;if(d===e)Ee(),b=sa(a,b,c);else{if(e=b.stateNode.hydrate)Ka=kb(b.stateNode.containerInfo.firstChild),ra=b,e=Wa=!0;if(e)for(c=Fe(b,null,d,c),b.child=c;c;)c.effectTag=c.effectTag&-3|1024,c=c.sibling;else T(a,b,d,c),Ee();b=b.child}return b;case 5:return bh(b),null===a&&De(b),d=b.type,e=b.pendingProps,f=null!==a?a.memoizedProps:
null,g=e.children,Yd(d,e)?g=null:null!==f&&Yd(d,f)&&(b.effectTag|=16),qh(a,b),b.mode&4&&1!==c&&e.hidden?(b.expirationTime=b.childExpirationTime=1,b=null):(T(a,b,g,c),b=b.child),b;case 6:return null===a&&De(b),null;case 13:return th(a,b,c);case 4:return se(b,b.stateNode.containerInfo),d=b.pendingProps,null===a?b.child=wb(b,null,d,c):T(a,b,d,c),b.child;case 11:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),nh(a,b,d,e,c);case 7:return T(a,b,b.pendingProps,c),b.child;case 8:return T(a,
b,b.pendingProps.children,c),b.child;case 12:return T(a,b,b.pendingProps.children,c),b.child;case 10:a:{d=b.type._context;e=b.pendingProps;g=b.memoizedProps;f=e.value;var h=b.type._context;y(Ic,h._currentValue);h._currentValue=f;if(null!==g)if(h=g.value,f=Qa(h,f)?0:("function"===typeof d._calculateChangedBits?d._calculateChangedBits(h,f):**********)|0,0===f){if(g.children===e.children&&!G.current){b=sa(a,b,c);break a}}else for(h=b.child,null!==h&&(h.return=b);null!==h;){var m=h.dependencies;if(null!==
m){g=h.child;for(var l=m.firstContext;null!==l;){if(l.context===d&&0!==(l.observedBits&f)){1===h.tag&&(l=Ea(c,null),l.tag=Jc,Fa(h,l));h.expirationTime<c&&(h.expirationTime=c);l=h.alternate;null!==l&&l.expirationTime<c&&(l.expirationTime=c);Sg(h.return,c);m.expirationTime<c&&(m.expirationTime=c);break}l=l.next}}else g=10===h.tag?h.type===b.type?null:h.child:h.child;if(null!==g)g.return=h;else for(g=h;null!==g;){if(g===b){g=null;break}h=g.sibling;if(null!==h){h.return=g.return;g=h;break}g=g.return}h=
g}T(a,b,e.children,c);b=b.child}return b;case 9:return e=b.type,f=b.pendingProps,d=f.children,rb(b,c),e=W(e,f.unstable_observedBits),d=d(e),b.effectTag|=1,T(a,b,d,c),b.child;case 14:return e=b.type,f=aa(e,b.pendingProps),f=aa(e.type,f),oh(a,b,e,f,d,c);case 15:return ph(a,b,b.type,b.pendingProps,d,c);case 17:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),null!==a&&(a.alternate=null,b.alternate=null,b.effectTag|=2),b.tag=1,N(d)?(a=!0,Bc(b)):a=!1,rb(b,c),Yg(b,d,e),pe(b,d,e,c),Ie(null,
b,d,!0,a,c);case 19:return vh(a,b,c)}throw Error(k(156,b.tag));};var bf=null,Ne=null,la=function(a,b,c,d){return new Fj(a,b,c,d)};ef.prototype.render=function(a){md(a,this._internalRoot,null,null)};ef.prototype.unmount=function(){var a=this._internalRoot,b=a.containerInfo;md(null,a,null,function(){b[Lb]=null})};var Di=function(a){if(13===a.tag){var b=Fc(ka(),150,100);Ja(a,b);df(a,b)}};var Yf=function(a){13===a.tag&&(Ja(a,3),df(a,3))};var Bi=function(a){if(13===a.tag){var b=ka();b=Va(b,a,null);Ja(a,
b);df(a,b)}};sd=function(a,b,c){switch(b){case "input":Dd(a,c);b=c.name;if("radio"===c.type&&null!=b){for(c=a;c.parentNode;)c=c.parentNode;c=c.querySelectorAll("input[name="+JSON.stringify(""+b)+'][type="radio"]');for(b=0;b<c.length;b++){var d=c[b];if(d!==a&&d.form===a.form){var e=ae(d);if(!e)throw Error(k(90));Gf(d);Dd(d,e)}}}break;case "textarea":Lf(a,c);break;case "select":b=c.value,null!=b&&hb(a,!!c.multiple,b,!1)}};(function(a,b,c,d){ee=a;eg=b;vd=c;vf=d})(Qh,function(a,b,c,d,e){var f=p;p|=4;
try{return Da(98,a.bind(null,b,c,d,e))}finally{p=f,p===H&&ha()}},function(){(p&(1|ca|ma))===H&&(uj(),xb())},function(a,b){var c=p;p|=2;try{return a(b)}finally{p=c,p===H&&ha()}});var mk={Events:[Hb,Pa,ae,pf,qd,lb,function(a){Kd(a,Ki)},sf,tf,sc,pc,xb,{current:!1}]};(function(a){var b=a.findFiberByHostInstance;return Ej(M({},a,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:da.ReactCurrentDispatcher,findHostInstanceByFiber:function(a){a=Sf(a);
return null===a?null:a.stateNode},findFiberByHostInstance:function(a){return b?b(a):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null}))})({findFiberByHostInstance:Bb,bundleType:0,version:"16.13.1",rendererPackageName:"react-dom"});I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=mk;I.createPortal=Xh;I.findDOMNode=function(a){if(null==a)return null;if(1===a.nodeType)return a;var b=a._reactInternalFiber;if(void 0===
b){if("function"===typeof a.render)throw Error(k(188));throw Error(k(268,Object.keys(a)));}a=Sf(b);a=null===a?null:a.stateNode;return a};I.flushSync=function(a,b){if((p&(ca|ma))!==H)throw Error(k(187));var c=p;p|=1;try{return Da(99,a.bind(null,b))}finally{p=c,ha()}};I.hydrate=function(a,b,c){if(!bc(b))throw Error(k(200));return nd(null,a,b,!0,c)};I.render=function(a,b,c){if(!bc(b))throw Error(k(200));return nd(null,a,b,!1,c)};I.unmountComponentAtNode=function(a){if(!bc(a))throw Error(k(40));return a._reactRootContainer?
(Rh(function(){nd(null,null,a,!1,function(){a._reactRootContainer=null;a[Lb]=null})}),!0):!1};I.unstable_batchedUpdates=Qh;I.unstable_createPortal=function(a,b){return Xh(a,b,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)};I.unstable_renderSubtreeIntoContainer=function(a,b,c,d){if(!bc(c))throw Error(k(200));if(null==a||void 0===a._reactInternalFiber)throw Error(k(38));return nd(a,b,c,!1,d)};I.version="16.13.1"});
</script>
    <script>const e = React.createElement;

function pathToString(path) {
  if (path[0] === '/') {
    return '/' + path.slice(1).join('/');
  } else {
    return path.join('/');
  }
}

function findCommonPath(files) {
  if (!files || !files.length) {
    return [];
  }

  function isPrefix(arr, prefix) {
    if (arr.length < prefix.length) {
      return false;
    }
    for (let i = prefix.length - 1; i >= 0; --i) {
      if (arr[i] !== prefix[i]) {
        return false;
      }
    }
    return true;
  }

  let commonPath = files[0].path.slice(0, -1);
  while (commonPath.length) {
    if (files.every(file => isPrefix(file.path, commonPath))) {
      break;
    }
    commonPath.pop();
  }
  return commonPath;
}

function findFolders(files) {
  if (!files || !files.length) {
    return [];
  }

  let folders = files.filter(file => file.path.length > 1).map(file => file.path[0]);
  folders = [...new Set(folders)]; // unique
  folders.sort();

  folders = folders.map(folder => {
    let filesInFolder = files
      .filter(file => file.path[0] === folder)
      .map(file => ({
        ...file,
        path: file.path.slice(1),
        parent: [...file.parent, file.path[0]],
      }));

    const children = findFolders(filesInFolder); // recursion

    return {
      is_folder: true,
      path: [folder],
      parent: files[0].parent,
      children,
      covered: children.reduce((sum, file) => sum + file.covered, 0),
      coverable: children.reduce((sum, file) => sum + file.coverable, 0),
      prevRun: {
        covered: children.reduce((sum, file) => sum + file.prevRun.covered, 0),
        coverable: children.reduce((sum, file) => sum + file.prevRun.coverable, 0),
      }
    };
  });

  return [
    ...folders,
    ...files.filter(file => file.path.length === 1),
  ];
}

class App extends React.Component {
  constructor(...args) {
    super(...args);

    this.state = {
      current: [],
    };
  }

  componentDidMount() {
    this.updateStateFromLocation();
    window.addEventListener("hashchange", () => this.updateStateFromLocation(), false);
  }

  updateStateFromLocation() {
    if (window.location.hash.length > 1) {
      const current = window.location.hash.substr(1).split('/');
      this.setState({current});
    } else {
      this.setState({current: []});
    }
  }

  getCurrentPath() {
    let file = this.props.root;
    let path = [file];
    for (let p of this.state.current) {
      file = file.children.find(file => file.path[0] === p);
      if (!file) {
        return path;
      }
      path.push(file);
    }
    return path;
  }

  render() {
    const path = this.getCurrentPath();
    const file = path[path.length - 1];

    let w = null;
    if (file.is_folder) {
      w = e(FilesList, {
        folder: file,
        onSelectFile: this.selectFile.bind(this),
        onBack: path.length > 1 ? this.back.bind(this) : null,
      });
    } else {
      w = e(DisplayFile, {
        file,
        onBack: this.back.bind(this),
      });
    }

    return e('div', {className: 'app'}, w);
  }

  selectFile(file) {
    this.setState(({current}) => {
      return {current: [...current, file.path[0]]};
    }, () => this.updateHash());
  }

  back(file) {
    this.setState(({current}) => {
      return {current: current.slice(0, current.length - 1)};
    }, () => this.updateHash());
  }

  updateHash() {
    if (!this.state.current || !this.state.current.length) {
      window.location = '#';
    } else {
      window.location = '#' + this.state.current.join('/');
    }
  }
}

function FilesList({folder, onSelectFile, onBack}) {
  let files = folder.children;
  return e('div', {className: 'display-folder'},
    e(FileHeader, {file: folder, onBack}),
    e('table', {className: 'files-list'},
      e('thead', {className: 'files-list__head'},
        e('tr', null,
          e('th', null, "Path"),
          e('th', null, "Coverage")
        )
      ),
      e('tbody', {className: 'files-list__body'},
        files.map(file => e(File, {file, onClick: onSelectFile}))
      )
    )
  );
}

function File({file, onClick}) {
  const coverage = file.coverable ? file.covered / file.coverable * 100 : -1;
  const coverageDelta = file.prevRun &&
    (file.covered / file.coverable * 100 - file.prevRun.covered / file.prevRun.coverable * 100);

  return e('tr', {
      className: 'files-list__file'
        + (coverage >= 0 && coverage < 50 ? ' files-list__file_low': '')
        + (coverage >= 50 && coverage < 80 ? ' files-list__file_medium': '')
        + (coverage >= 80 ? ' files-list__file_high': '')
        + (file.is_folder ? ' files-list__file_folder': ''),
      onClick: () => onClick(file),
    },
    e('td', null, e('a', null, pathToString(file.path))),
    e('td', null,
      file.covered + ' / ' + file.coverable +
      (coverage >= 0 ? ' (' + coverage.toFixed(2) + '%)' : ''),
      e('span', {title: 'Change from the previous run'},
        (coverageDelta ? ` (${coverageDelta > 0 ? '+' : ''}${coverageDelta.toFixed(2)}%)` : ''))
    )
  );
}

function DisplayFile({file, onBack}) {
  return e('div', {className: 'display-file'},
    e(FileHeader, {file, onBack}),
    e(FileContent, {file})
  );
}

function FileHeader({file, onBack}) {
  const coverage = file.covered / file.coverable * 100;
  const coverageDelta = file.prevRun && (coverage - file.prevRun.covered / file.prevRun.coverable * 100);

  return e('div', {className: 'file-header'},
    onBack ? e('a', {className: 'file-header__back', onClick: onBack}, 'Back') : null,
    e('div', {className: 'file-header__name'}, pathToString([...file.parent, ...file.path])),
    e('div', {className: 'file-header__stat'},
      'Covered: ' + file.covered + ' of ' + file.coverable +
      (file.coverable ? ' (' + coverage.toFixed(2) + '%)' : ''),
      e('span', {title: 'Change from the previous run'},
        (coverageDelta ? ` (${coverageDelta > 0 ? '+' : ''}${coverageDelta.toFixed(2)}%)` : ''))
    )
  );
}

function FileContent({file}) {
  return e('pre', {className: 'file-content'},
    file.content.split(/\r?\n/).map((line, index) => {
      const trace = file.traces.find(trace => trace.line === index + 1);
      const covered = trace && trace.stats.Line;
      const uncovered = trace && !trace.stats.Line;
      return e('code', {
          className: 'code-line'
            + (covered ? ' code-line_covered' : '')
            + (uncovered ? ' code-line_uncovered' : ''),
          title: trace ? JSON.stringify(trace.stats, null, 2) : null,
        }, line);
    })
  );
}

(function(){
  const commonPath = findCommonPath(data.files);
  const prevFilesMap = new Map();

  previousData && previousData.files.forEach((file) => {
    const path = file.path.slice(commonPath.length).join('/');
    prevFilesMap.set(path, file);
  });

  const files = data.files.map((file) => {
    const path = file.path.slice(commonPath.length);
    const { covered = 0, coverable = 0 } = prevFilesMap.get(path.join('/')) || {};
    return {
      ...file,
      path,
      parent: commonPath,
      prevRun: { covered, coverable },
    };
  });

  const children = findFolders(files);

  const root = {
    is_folder: true,
    children,
    path: commonPath,
    parent: [],
    covered: children.reduce((sum, file) => sum + file.covered, 0),
    coverable: children.reduce((sum, file) => sum + file.coverable, 0),
    prevRun: {
      covered: children.reduce((sum, file) => sum + file.prevRun.covered, 0),
      coverable: children.reduce((sum, file) => sum + file.prevRun.coverable, 0),
    }
  };

  ReactDOM.render(e(App, {root, prevFilesMap}), document.getElementById('root'));
}());
</script>
</body>
</html>