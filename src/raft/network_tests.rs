#[cfg(test)]
mod tests {
    use crate::raft::network::{NetworkConfig, ConfluxNetwork, ConfluxNetworkFactory};
    use openraft::{
        network::RaftNetworkFactory,
        BasicNode,
    };
    use std::collections::HashMap;

    /// Create a test network config
    fn create_test_network_config() -> NetworkConfig {
        let mut addresses = HashMap::new();
        addresses.insert(1, "http://127.0.0.1:8001".to_string());
        addresses.insert(2, "http://127.0.0.1:8002".to_string());
        addresses.insert(3, "http://127.0.0.1:8003".to_string());

        NetworkConfig::new(addresses)
    }

    #[tokio::test]
    async fn test_network_config_creation() {
        let config = NetworkConfig::default();
        assert_eq!(config.timeout_secs, 10);
        assert!(config.node_addresses.read().await.is_empty());
    }

    #[tokio::test]
    async fn test_network_config_with_addresses() {
        let config = create_test_network_config();
        assert_eq!(config.timeout_secs, 10);

        let addresses = config.node_addresses.read().await;
        assert_eq!(addresses.len(), 3);
        assert_eq!(addresses.get(&1), Some(&"http://127.0.0.1:8001".to_string()));
        assert_eq!(addresses.get(&2), Some(&"http://127.0.0.1:8002".to_string()));
        assert_eq!(addresses.get(&3), Some(&"http://127.0.0.1:8003".to_string()));
    }

    #[tokio::test]
    async fn test_network_config_add_node() {
        let config = NetworkConfig::default();
        config.add_node(1, "http://127.0.0.1:8001".to_string()).await;

        let addresses = config.node_addresses.read().await;
        assert_eq!(addresses.len(), 1);
        assert_eq!(addresses.get(&1), Some(&"http://127.0.0.1:8001".to_string()));
    }

    #[tokio::test]
    async fn test_conflux_network_factory_creation() {
        let config = create_test_network_config();
        let mut factory = ConfluxNetworkFactory::new(config);

        // Test that we can create a network instance
        let network = factory.new_client(1, &BasicNode::default()).await;
        assert_eq!(network.target_node_id, 1);
    }

    #[tokio::test]
    async fn test_conflux_network_creation() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(config, 1);

        assert_eq!(network.target_node_id, 1);
    }

    #[tokio::test]
    async fn test_conflux_network_get_target_address() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        let address = network.get_target_address().await;
        assert!(address.is_ok());
        assert_eq!(address.unwrap(), "http://127.0.0.1:8001");
    }

    #[tokio::test]
    async fn test_conflux_network_get_target_address_not_found() {
        let config = NetworkConfig::default();
        let network = ConfluxNetwork::new(999, config);
        
        let address = network.get_target_address().await;
        assert!(address.is_err());
        
        if let Err(NetworkError::RPCError(RPCError::Network(err))) = address {
            assert!(err.to_string().contains("Node address not found"));
        } else {
            panic!("Expected NetworkError::RPCError");
        }
    }

    #[tokio::test]
    async fn test_conflux_network_is_reachable() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        // This will likely fail since we don't have actual servers running
        // but we're testing the method exists and returns a boolean
        let reachable = network.is_reachable().await;
        assert!(!reachable); // Expected to be false in test environment
    }

    #[tokio::test]
    async fn test_conflux_network_connection_stats() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        let stats = network.get_connection_stats().await;
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
        assert!(stats.last_request_time.is_none());
    }

    #[tokio::test]
    async fn test_vote_request_handling() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        let vote_request = VoteRequest {
            vote: Vote::new(1, 1),
            last_log_id: None,
        };
        
        // This will fail since we don't have actual servers, but we test the method signature
        let result = network.vote(&vote_request, RPCOption::default()).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_append_entries_request_handling() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        let append_request = AppendEntriesRequest {
            vote: Vote::new(1, 1),
            prev_log_id: None,
            entries: vec![],
            leader_commit: None,
        };
        
        // This will fail since we don't have actual servers, but we test the method signature
        let result = network.append_entries(&append_request, RPCOption::default()).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_install_snapshot_request_handling() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        let snapshot_request = InstallSnapshotRequest {
            vote: Vote::new(1, 1),
            meta: openraft::storage::SnapshotMeta {
                last_log_id: None,
                last_membership: openraft::StoredMembership::default(),
                snapshot_id: "test-snapshot".to_string(),
            },
            offset: 0,
            data: vec![],
            done: true,
        };
        
        // This will fail since we don't have actual servers, but we test the method signature
        let result = network.install_snapshot(&snapshot_request, RPCOption::default()).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_network_error_handling() {
        let config = NetworkConfig::default(); // Empty config
        let network = ConfluxNetwork::new(999, config); // Non-existent node
        
        // Test that all RPC methods properly handle missing node addresses
        let vote_request = VoteRequest {
            vote: Vote::new(1, 1),
            last_log_id: None,
        };
        
        let vote_result = network.vote(&vote_request, RPCOption::default()).await;
        assert!(vote_result.is_err());
        
        let append_request = AppendEntriesRequest {
            vote: Vote::new(1, 1),
            prev_log_id: None,
            entries: vec![],
            leader_commit: None,
        };
        
        let append_result = network.append_entries(&append_request, RPCOption::default()).await;
        assert!(append_result.is_err());
    }

    #[tokio::test]
    async fn test_connection_stats_tracking() {
        let config = create_test_network_config();
        let network = ConfluxNetwork::new(1, config);
        
        // Initial stats should be zero
        let initial_stats = network.get_connection_stats().await;
        assert_eq!(initial_stats.total_requests, 0);
        assert_eq!(initial_stats.successful_requests, 0);
        assert_eq!(initial_stats.failed_requests, 0);
        
        // After a failed request, stats should update
        let vote_request = VoteRequest {
            vote: Vote::new(1, 1),
            last_log_id: None,
        };
        
        let _ = network.vote(&vote_request, RPCOption::default()).await;
        
        let updated_stats = network.get_connection_stats().await;
        assert_eq!(updated_stats.total_requests, 1);
        assert_eq!(updated_stats.failed_requests, 1);
        assert_eq!(updated_stats.successful_requests, 0);
        assert!(updated_stats.last_request_time.is_some());
    }

    #[tokio::test]
    async fn test_network_timeout_configuration() {
        let mut config = create_test_network_config();
        config.timeout_secs = 5;
        
        let network = ConfluxNetwork::new(1, config);
        assert_eq!(network.timeout, Duration::from_secs(5));
    }

    #[tokio::test]
    async fn test_multiple_network_instances() {
        let config = create_test_network_config();
        let factory = ConfluxNetworkFactory::new(config);
        
        // Create multiple network instances for different nodes
        let network1 = factory.new_client(1, &BasicNode::default()).await;
        let network2 = factory.new_client(2, &BasicNode::default()).await;
        let network3 = factory.new_client(3, &BasicNode::default()).await;
        
        assert!(network1.is_ok());
        assert!(network2.is_ok());
        assert!(network3.is_ok());
        
        // Verify they target different nodes
        let net1 = network1.unwrap();
        let net2 = network2.unwrap();
        let net3 = network3.unwrap();
        
        assert_eq!(net1.target_node, 1);
        assert_eq!(net2.target_node, 2);
        assert_eq!(net3.target_node, 3);
    }
}
