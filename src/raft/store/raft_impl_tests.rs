#[cfg(test)]
mod tests {
    use super::super::types::Store;
    use crate::raft::types::*;
    use std::sync::Arc;
    use tempfile::TempDir;

    async fn create_test_store() -> (Arc<Store>, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let store = Arc::new(Store::new(temp_dir.path()).await.unwrap());
        (store, temp_dir)
    }

    #[tokio::test]
    async fn test_apply_command_create_config() {
        let (store, _temp_dir) = create_test_store().await;
        
        let command = RaftCommand::CreateConfig {
            namespace: ConfigNamespace {
                tenant: "test".to_string(),
                app: "app".to_string(),
                env: "dev".to_string(),
            },
            name: "test-config".to_string(),
            description: "Test configuration".to_string(),
        };
        
        let response = store.apply_command(&command).await.unwrap();
        assert!(response.success);
        assert!(response.data.is_some());
        
        // Verify config was created
        let configs = store.get_configs(&ConfigNamespace {
            tenant: "test".to_string(),
            app: "app".to_string(),
            env: "dev".to_string(),
        }).await.unwrap();
        
        assert_eq!(configs.len(), 1);
        assert_eq!(configs[0].name, "test-config");
    }

    #[tokio::test]
    async fn test_apply_command_create_version() {
        let (store, _temp_dir) = create_test_store().await;
        
        // First create a config
        let create_config_cmd = RaftCommand::CreateConfig {
            namespace: ConfigNamespace {
                tenant: "test".to_string(),
                app: "app".to_string(),
                env: "dev".to_string(),
            },
            name: "test-config".to_string(),
            description: "Test configuration".to_string(),
        };
        
        let config_response = store.apply_command(&create_config_cmd).await.unwrap();
        assert!(config_response.success);
        
        // Extract config ID from response
        let config_data = config_response.data.unwrap();
        let config_id = config_data.as_object().unwrap()["id"].as_str().unwrap().to_string();
        
        // Now create a version
        let create_version_cmd = RaftCommand::CreateVersion {
            config_id: config_id.clone(),
            content: serde_json::json!({"key": "value"}),
            description: "Test version".to_string(),
        };
        
        let version_response = store.apply_command(&create_version_cmd).await.unwrap();
        assert!(version_response.success);
        assert!(version_response.data.is_some());
        
        // Verify version was created
        let versions = store.get_versions(&config_id).await.unwrap();
        assert_eq!(versions.len(), 1);
        assert_eq!(versions[0].description, "Test version");
    }

    #[tokio::test]
    async fn test_apply_command_get_config() {
        let (store, _temp_dir) = create_test_store().await;
        
        // First create a config
        let create_config_cmd = RaftCommand::CreateConfig {
            namespace: ConfigNamespace {
                tenant: "test".to_string(),
                app: "app".to_string(),
                env: "dev".to_string(),
            },
            name: "test-config".to_string(),
            description: "Test configuration".to_string(),
        };
        
        let config_response = store.apply_command(&create_config_cmd).await.unwrap();
        let config_data = config_response.data.unwrap();
        let config_id = config_data.as_object().unwrap()["id"].as_str().unwrap().to_string();
        
        // Now get the config
        let get_config_cmd = RaftCommand::GetConfig {
            config_id: config_id.clone(),
        };
        
        let get_response = store.apply_command(&get_config_cmd).await.unwrap();
        assert!(get_response.success);
        assert!(get_response.data.is_some());
        
        let retrieved_config = get_response.data.unwrap();
        assert_eq!(retrieved_config.as_object().unwrap()["name"].as_str().unwrap(), "test-config");
    }

    #[tokio::test]
    async fn test_apply_command_update_release_rules() {
        let (store, _temp_dir) = create_test_store().await;
        
        // First create a config
        let create_config_cmd = RaftCommand::CreateConfig {
            namespace: ConfigNamespace {
                tenant: "test".to_string(),
                app: "app".to_string(),
                env: "dev".to_string(),
            },
            name: "test-config".to_string(),
            description: "Test configuration".to_string(),
        };
        
        let config_response = store.apply_command(&create_config_cmd).await.unwrap();
        let config_data = config_response.data.unwrap();
        let config_id = config_data.as_object().unwrap()["id"].as_str().unwrap().to_string();
        
        // Update release rules
        let update_rules_cmd = RaftCommand::UpdateReleaseRules {
            config_id: config_id.clone(),
            rules: vec![
                ReleaseRule {
                    environment: "production".to_string(),
                    percentage: 100,
                    conditions: vec![],
                },
            ],
        };
        
        let rules_response = store.apply_command(&update_rules_cmd).await.unwrap();
        assert!(rules_response.success);
        
        // Verify rules were updated
        let config = store.get_config(&config_id).await.unwrap();
        assert!(config.is_some());
        assert_eq!(config.unwrap().release_rules.len(), 1);
    }

    #[tokio::test]
    async fn test_apply_command_delete_config() {
        let (store, _temp_dir) = create_test_store().await;
        
        // First create a config
        let create_config_cmd = RaftCommand::CreateConfig {
            namespace: ConfigNamespace {
                tenant: "test".to_string(),
                app: "app".to_string(),
                env: "dev".to_string(),
            },
            name: "test-config".to_string(),
            description: "Test configuration".to_string(),
        };
        
        let config_response = store.apply_command(&create_config_cmd).await.unwrap();
        let config_data = config_response.data.unwrap();
        let config_id = config_data.as_object().unwrap()["id"].as_str().unwrap().to_string();
        
        // Delete the config
        let delete_config_cmd = RaftCommand::DeleteConfig {
            config_id: config_id.clone(),
        };
        
        let delete_response = store.apply_command(&delete_config_cmd).await.unwrap();
        assert!(delete_response.success);
        
        // Verify config was deleted
        let config = store.get_config(&config_id).await.unwrap();
        assert!(config.is_none());
    }

    #[tokio::test]
    async fn test_apply_command_invalid_config_id() {
        let (store, _temp_dir) = create_test_store().await;
        
        let get_config_cmd = RaftCommand::GetConfig {
            config_id: "non-existent-id".to_string(),
        };
        
        let response = store.apply_command(&get_config_cmd).await.unwrap();
        assert!(!response.success);
        assert!(response.error.is_some());
    }

    #[tokio::test]
    async fn test_apply_command_create_duplicate_config() {
        let (store, _temp_dir) = create_test_store().await;
        
        let namespace = ConfigNamespace {
            tenant: "test".to_string(),
            app: "app".to_string(),
            env: "dev".to_string(),
        };
        
        // Create first config
        let create_config_cmd = RaftCommand::CreateConfig {
            namespace: namespace.clone(),
            name: "test-config".to_string(),
            description: "Test configuration".to_string(),
        };
        
        let response1 = store.apply_command(&create_config_cmd).await.unwrap();
        assert!(response1.success);
        
        // Try to create duplicate config
        let create_duplicate_cmd = RaftCommand::CreateConfig {
            namespace,
            name: "test-config".to_string(),
            description: "Duplicate configuration".to_string(),
        };
        
        let response2 = store.apply_command(&create_duplicate_cmd).await.unwrap();
        assert!(!response2.success);
        assert!(response2.error.is_some());
    }
}
